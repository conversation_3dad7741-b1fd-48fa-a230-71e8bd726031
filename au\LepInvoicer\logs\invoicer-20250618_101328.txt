[2025-06-18 10:13:28.277 +10:00 INF] Starting LEP Invoicer application
[2025-06-18 10:13:28.306 +10:00 INF] Initializing FastReport...
[2025-06-18 10:13:28.384 +10:00 INF] FastReport initialized successfully
[2025-06-18 10:13:28.795 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-18 10:13:30.110 +10:00 INF] Starting LEP Invoicer at "2025-06-18T10:13:30.1098561+10:00"
[2025-06-18 10:13:30.113 +10:00 INF] Initializing database service...
[2025-06-18 10:13:30.116 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-18 10:13:30.219 +10:00 INF] Database connection established successfully
[2025-06-18 10:13:30.221 +10:00 INF] Database service initialized successfully
[2025-06-18 10:13:30.224 +10:00 INF] Checking for pending work...
[2025-06-18 10:13:30.226 +10:00 INF] Getting 500 orders to invoice
[2025-06-18 10:13:31.192 +10:00 INF] Found 0 orders to invoice (filtered 16 candidates)
[2025-06-18 10:13:31.194 +10:00 INF] Getting 100 credits to invoice
[2025-06-18 10:13:31.214 +10:00 INF] Found 0 credits to invoice
[2025-06-18 10:13:31.219 +10:00 INF] Getting 100 refunds to invoice
[2025-06-18 10:13:31.227 +10:00 INF] Found 0 refunds to invoice
[2025-06-18 10:13:31.228 +10:00 INF] No pending work found
[2025-06-18 10:13:31.229 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-18 10:13:31.231 +10:00 INF] Cleaning up invoicer logs...
[2025-06-18 10:13:31.339 +10:00 INF] LEP Invoicer completed successfully in 1229ms. No work to process.
[2025-06-18 10:13:31.347 +10:00 INF] Database connection disposed
[2025-06-18 10:13:31.352 +10:00 INF] LEP Invoicer completed with result: 0
