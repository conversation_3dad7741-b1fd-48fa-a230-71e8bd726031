namespace lep.security.impl
{
	//public class AlwaysAllowSecurityManager : ISecurityApplication, IInitializingObject
	//   {
	//       #region services

	//       protected IUserApplication userApp;

	//       #endregion

	//       #region properties

	//       public IUserApplication UserApplication
	//       {
	//           set { userApp = value; }
	//       }

	//       #endregion

	//       public void AfterPropertiesSet()
	//       {
	//           ApplicationSecurityManager.SecurityApp = this;
	//       }

	//       public bool HasAnyTask(string app)
	//       {
	//           return true;
	//       }

	//       public bool HasTask(string app, string task)
	//       {
	//           return true;
	//       }

	//       public bool HasSession()
	//       {
	//           return false;
	//       }

	//       public bool AttemptLogin(string username, string password)
	//       {
	//           return false;
	//       }

	//       public void AbandonSession()
	//       {
	//           // no-op
	//       }

	//       public IUser Identity
	//       {
	//           get { return GetSystemUser() as IUser; }
	//       }

	//       public string HomePage
	//       {
	//           get { return ""; }
	//       }

	//       public void AssertPermission(string task)
	//       {
	//           // NOOP
	//       }

	//       public void AssertIsCustomerUser(ICustomerUser customer)
	//       {
	//       }

	//       public void DoPrivileged(PrivilegeDelegate func)
	//       {
	//           func();
	//       }

	//       public IStaff GetSystemUser()
	//       {
	//           return userApp.GetSystemUser();
	//       }
	//   }
}