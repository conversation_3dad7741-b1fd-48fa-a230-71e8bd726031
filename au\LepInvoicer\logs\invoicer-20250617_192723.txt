[2025-06-17 19:27:23.656 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 19:27:23.686 +10:00 INF] Initializing FastReport...
[2025-06-17 19:27:23.763 +10:00 INF] FastReport initialized successfully
[2025-06-17 19:27:24.182 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 19:27:25.419 +10:00 INF] Starting LEP Invoicer at "2025-06-17T19:27:25.4196703+10:00"
[2025-06-17 19:27:25.423 +10:00 INF] Initializing database service...
[2025-06-17 19:27:25.426 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 19:27:25.528 +10:00 INF] Database connection established successfully
[2025-06-17 19:27:25.529 +10:00 INF] Database service initialized successfully
[2025-06-17 19:27:25.532 +10:00 INF] Checking for pending work...
[2025-06-17 19:27:25.535 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 19:27:26.419 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 19:27:26.422 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 19:27:26.433 +10:00 INF] Found 0 credits to invoice
[2025-06-17 19:27:26.435 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 19:27:26.439 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 19:27:26.441 +10:00 INF] No pending work found
[2025-06-17 19:27:26.443 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 19:27:26.444 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 19:27:26.518 +10:00 INF] LEP Invoicer completed successfully in 1098ms. No work to process.
[2025-06-17 19:27:26.524 +10:00 INF] Database connection disposed
[2025-06-17 19:27:26.526 +10:00 INF] LEP Invoicer completed with result: 0
