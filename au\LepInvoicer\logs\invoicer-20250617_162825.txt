[2025-06-17 16:28:25.571 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 16:28:25.606 +10:00 INF] Initializing FastReport...
[2025-06-17 16:28:25.702 +10:00 INF] FastReport initialized successfully
[2025-06-17 16:28:26.127 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 16:28:27.388 +10:00 INF] Starting LEP Invoicer at "2025-06-17T16:28:27.3885183+10:00"
[2025-06-17 16:28:27.392 +10:00 INF] Initializing database service...
[2025-06-17 16:28:27.395 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 16:28:27.492 +10:00 INF] Database connection established successfully
[2025-06-17 16:28:27.494 +10:00 INF] Database service initialized successfully
[2025-06-17 16:28:27.496 +10:00 INF] Checking for pending work...
[2025-06-17 16:28:27.499 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 16:28:28.406 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 16:28:28.410 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 16:28:28.424 +10:00 INF] Found 0 credits to invoice
[2025-06-17 16:28:28.426 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 16:28:28.431 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 16:28:28.432 +10:00 INF] No pending work found
[2025-06-17 16:28:28.434 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 16:28:28.440 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 16:28:28.524 +10:00 INF] LEP Invoicer completed successfully in 1136ms. No work to process.
[2025-06-17 16:28:28.531 +10:00 INF] Database connection disposed
[2025-06-17 16:28:28.533 +10:00 INF] LEP Invoicer completed with result: 0
