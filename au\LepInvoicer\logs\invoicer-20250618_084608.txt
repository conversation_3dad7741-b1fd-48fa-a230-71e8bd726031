[2025-06-18 08:46:08.655 +10:00 INF] Starting LEP Invoicer application
[2025-06-18 08:46:08.690 +10:00 INF] Initializing FastReport...
[2025-06-18 08:46:08.780 +10:00 INF] FastReport initialized successfully
[2025-06-18 08:46:09.216 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-18 08:46:10.553 +10:00 INF] Starting LEP Invoicer at "2025-06-18T08:46:10.5533357+10:00"
[2025-06-18 08:46:10.557 +10:00 INF] Initializing database service...
[2025-06-18 08:46:10.560 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-18 08:46:10.670 +10:00 INF] Database connection established successfully
[2025-06-18 08:46:10.671 +10:00 INF] Database service initialized successfully
[2025-06-18 08:46:10.673 +10:00 INF] Checking for pending work...
[2025-06-18 08:46:10.678 +10:00 INF] Getting 500 orders to invoice
[2025-06-18 08:46:11.547 +10:00 INF] Found 0 orders to invoice (filtered 16 candidates)
[2025-06-18 08:46:11.550 +10:00 INF] Getting 100 credits to invoice
[2025-06-18 08:46:11.565 +10:00 INF] Found 0 credits to invoice
[2025-06-18 08:46:11.567 +10:00 INF] Getting 100 refunds to invoice
[2025-06-18 08:46:11.571 +10:00 INF] Found 0 refunds to invoice
[2025-06-18 08:46:11.573 +10:00 INF] No pending work found
[2025-06-18 08:46:11.574 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-18 08:46:11.576 +10:00 INF] Cleaning up invoicer logs...
[2025-06-18 08:46:11.656 +10:00 INF] LEP Invoicer completed successfully in 1102ms. No work to process.
[2025-06-18 08:46:11.663 +10:00 INF] Database connection disposed
[2025-06-18 08:46:11.664 +10:00 INF] LEP Invoicer completed with result: 0
