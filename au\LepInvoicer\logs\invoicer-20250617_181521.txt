[2025-06-17 18:15:21.615 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 18:15:21.642 +10:00 INF] Initializing FastReport...
[2025-06-17 18:15:21.711 +10:00 INF] FastReport initialized successfully
[2025-06-17 18:15:22.114 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 18:15:23.319 +10:00 INF] Starting LEP Invoicer at "2025-06-17T18:15:23.3187024+10:00"
[2025-06-17 18:15:23.322 +10:00 INF] Initializing database service...
[2025-06-17 18:15:23.325 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 18:15:23.431 +10:00 INF] Database connection established successfully
[2025-06-17 18:15:23.433 +10:00 INF] Database service initialized successfully
[2025-06-17 18:15:23.435 +10:00 INF] Checking for pending work...
[2025-06-17 18:15:23.439 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 18:15:24.402 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 18:15:24.404 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 18:15:24.417 +10:00 INF] Found 0 credits to invoice
[2025-06-17 18:15:24.419 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 18:15:24.424 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 18:15:24.426 +10:00 INF] No pending work found
[2025-06-17 18:15:24.427 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 18:15:24.431 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 18:15:24.507 +10:00 INF] LEP Invoicer completed successfully in 1188ms. No work to process.
[2025-06-17 18:15:24.517 +10:00 INF] Database connection disposed
[2025-06-17 18:15:24.520 +10:00 INF] LEP Invoicer completed with result: 0
