[2025-06-17 20:19:47.775 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 20:19:47.804 +10:00 INF] Initializing FastReport...
[2025-06-17 20:19:47.874 +10:00 INF] FastReport initialized successfully
[2025-06-17 20:19:48.292 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 20:19:49.713 +10:00 INF] Starting LEP Invoicer at "2025-06-17T20:19:49.7134162+10:00"
[2025-06-17 20:19:49.717 +10:00 INF] Initializing database service...
[2025-06-17 20:19:49.721 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 20:19:49.872 +10:00 INF] Database connection established successfully
[2025-06-17 20:19:49.874 +10:00 INF] Database service initialized successfully
[2025-06-17 20:19:49.900 +10:00 INF] Checking for pending work...
[2025-06-17 20:19:49.903 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 20:19:50.802 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 20:19:50.807 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 20:19:50.821 +10:00 INF] Found 0 credits to invoice
[2025-06-17 20:19:50.823 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 20:19:50.827 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 20:19:50.828 +10:00 INF] No pending work found
[2025-06-17 20:19:50.831 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 20:19:50.835 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 20:19:50.916 +10:00 INF] LEP Invoicer completed successfully in 1202ms. No work to process.
[2025-06-17 20:19:50.922 +10:00 INF] Database connection disposed
[2025-06-17 20:19:50.924 +10:00 INF] LEP Invoicer completed with result: 0
