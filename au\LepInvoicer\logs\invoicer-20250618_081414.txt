[2025-06-18 08:14:14.454 +10:00 INF] Starting LEP Invoicer application
[2025-06-18 08:14:14.482 +10:00 INF] Initializing FastReport...
[2025-06-18 08:14:14.559 +10:00 INF] FastReport initialized successfully
[2025-06-18 08:14:15.065 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-18 08:14:16.346 +10:00 INF] Starting LEP Invoicer at "2025-06-18T08:14:16.3466394+10:00"
[2025-06-18 08:14:16.350 +10:00 INF] Initializing database service...
[2025-06-18 08:14:16.353 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-18 08:14:16.454 +10:00 INF] Database connection established successfully
[2025-06-18 08:14:16.456 +10:00 INF] Database service initialized successfully
[2025-06-18 08:14:16.459 +10:00 INF] Checking for pending work...
[2025-06-18 08:14:16.462 +10:00 INF] Getting 500 orders to invoice
[2025-06-18 08:14:17.348 +10:00 INF] Found 0 orders to invoice (filtered 16 candidates)
[2025-06-18 08:14:17.350 +10:00 INF] Getting 100 credits to invoice
[2025-06-18 08:14:17.364 +10:00 INF] Found 0 credits to invoice
[2025-06-18 08:14:17.367 +10:00 INF] Getting 100 refunds to invoice
[2025-06-18 08:14:17.403 +10:00 INF] Found 0 refunds to invoice
[2025-06-18 08:14:17.405 +10:00 INF] No pending work found
[2025-06-18 08:14:17.406 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-18 08:14:17.408 +10:00 INF] Cleaning up invoicer logs...
[2025-06-18 08:14:17.486 +10:00 INF] LEP Invoicer completed successfully in 1139ms. No work to process.
[2025-06-18 08:14:17.492 +10:00 INF] Database connection disposed
[2025-06-18 08:14:17.493 +10:00 INF] LEP Invoicer completed with result: 0
