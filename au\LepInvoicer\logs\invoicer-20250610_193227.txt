[2025-06-10 19:32:27.552 +10:00 INF] Starting LEP Invoicer application
[2025-06-10 19:32:28.467 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-10 19:32:30.777 +10:00 INF] Starting LEP Invoicer at "2025-06-10T19:32:30.7768557+10:00"
[2025-06-10 19:32:30.785 +10:00 INF] Initializing services...
[2025-06-10 19:32:30.790 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-10 19:32:30.959 +10:00 INF] Database connection established successfully
[2025-06-10 19:32:30.965 +10:00 INF] Initializing MYOB service...
[2025-06-10 19:32:30.968 +10:00 INF] Initializing MYOB with OAuth authentication
[2025-06-10 19:32:30.972 +10:00 INF] Starting OAuth authentication flow
[2025-06-10 19:32:31.089 +10:00 INF] Using existing OAuth tokens
[2025-06-10 19:32:31.091 +10:00 INF] Initializing MYOB services with OAuth keystore
[2025-06-10 19:32:31.096 +10:00 INF] MYOB services initialized successfully
[2025-06-10 19:32:31.097 +10:00 INF] OAuth keystore set for API calls
[2025-06-10 19:32:31.099 +10:00 INF] Getting company files from MYOB
[2025-06-10 19:32:31.456 +10:00 INF] MYOB service initialized with OAuth credentials. Using company file: LEP Colour Printers Pty Ltd
[2025-06-10 19:32:31.469 +10:00 INF] Current cache stats: {"AccountsCount":269,"TaxCodesCount":18,"CustomersCount":393,"LastCacheLoad":"2025-06-10T19:31:07.5727667+10:00","CacheAge":"00:01:23.8907212","IsExpired":false,"CacheDirectory":"C:\\Users\\<USER>\\AppData\\Local\\LepInvoicer\\cache"}
[2025-06-10 19:32:31.492 +10:00 INF] Using cached MYOB data: {"AccountsCount":269,"TaxCodesCount":18,"CustomersCount":393,"LastCacheLoad":"2025-06-10T19:31:07.5727667+10:00","CacheAge":"00:01:23.8907212","IsExpired":false,"CacheDirectory":"C:\\Users\\<USER>\\AppData\\Local\\LepInvoicer\\cache"}
[2025-06-10 19:32:32.279 +10:00 INF] Using cached GST tax code
[2025-06-10 19:32:32.281 +10:00 INF] Using cached freight account
[2025-06-10 19:32:32.282 +10:00 INF] Using cached discounts account
[2025-06-10 19:32:32.284 +10:00 INF] MYOB cache initialization complete: {"AccountsCount":269,"TaxCodesCount":18,"CustomersCount":393,"LastCacheLoad":"2025-06-10T19:31:07.5727667+10:00","CacheAge":"00:01:24.7112904","IsExpired":false,"CacheDirectory":"C:\\Users\\<USER>\\AppData\\Local\\LepInvoicer\\cache"}
[2025-06-10 19:32:32.293 +10:00 INF] MYOB service initialized successfully with company file: LEP Colour Printers Pty Ltd
[2025-06-10 19:32:32.295 +10:00 INF] Services initialized successfully
[2025-06-10 19:32:32.299 +10:00 INF] Processing order invoices...
[2025-06-10 19:32:32.304 +10:00 INF] Getting 500 orders to invoice
[2025-06-10 19:32:33.219 +10:00 INF] Found 2 orders to invoice
[2025-06-10 19:32:33.243 +10:00 INF] Found 2 orders to process
[2025-06-10 19:32:33.257 +10:00 INF] Getting order 1417006
[2025-06-10 19:32:33.717 +10:00 INF] Processing order 1417006 with 1 jobs, total: ¤0.00
[2025-06-10 19:32:33.724 +10:00 WRN] Order 1417006 has zero price, marking as invoiced
[2025-06-10 19:32:33.728 +10:00 INF] Getting order 1416838
[2025-06-10 19:32:33.739 +10:00 INF] Processing order 1416838 with 1 jobs, total: ¤0.00
[2025-06-10 19:32:33.743 +10:00 WRN] Order 1416838 has zero price, marking as invoiced
[2025-06-10 19:32:33.747 +10:00 INF] Order processing completed. Processed: 2, Success: 0, Failed: 2
[2025-06-10 19:32:33.751 +10:00 INF] Processing credit invoices...
[2025-06-10 19:32:33.756 +10:00 INF] Getting 100 credits to invoice
[2025-06-10 19:32:33.784 +10:00 INF] Found 0 credits to invoice
[2025-06-10 19:32:33.802 +10:00 INF] Processing refund invoices...
[2025-06-10 19:32:33.805 +10:00 INF] Getting 100 refunds to invoice
[2025-06-10 19:32:33.819 +10:00 INF] Found 0 refunds to invoice
[2025-06-10 19:32:33.937 +10:00 INF] Cleaning up invoicer logs...
[2025-06-10 19:32:34.015 +10:00 INF] LEP Invoicer completed successfully in 3238ms. Orders: 2, Credits: 0, Refunds: 0
[2025-06-10 19:32:34.027 +10:00 INF] Database connection disposed
[2025-06-10 19:32:34.032 +10:00 INF] LEP Invoicer completed with result: 0
