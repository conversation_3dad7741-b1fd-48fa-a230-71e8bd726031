[2025-06-17 18:19:42.599 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 18:19:42.627 +10:00 INF] Initializing FastReport...
[2025-06-17 18:19:42.721 +10:00 INF] FastReport initialized successfully
[2025-06-17 18:19:43.151 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 18:19:44.372 +10:00 INF] Starting LEP Invoicer at "2025-06-17T18:19:44.3718346+10:00"
[2025-06-17 18:19:44.375 +10:00 INF] Initializing database service...
[2025-06-17 18:19:44.378 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 18:19:44.482 +10:00 INF] Database connection established successfully
[2025-06-17 18:19:44.484 +10:00 INF] Database service initialized successfully
[2025-06-17 18:19:44.487 +10:00 INF] Checking for pending work...
[2025-06-17 18:19:44.490 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 18:19:45.415 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 18:19:45.418 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 18:19:45.430 +10:00 INF] Found 0 credits to invoice
[2025-06-17 18:19:45.432 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 18:19:45.436 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 18:19:45.437 +10:00 INF] No pending work found
[2025-06-17 18:19:45.439 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 18:19:45.441 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 18:19:45.524 +10:00 INF] LEP Invoicer completed successfully in 1152ms. No work to process.
[2025-06-17 18:19:45.531 +10:00 INF] Database connection disposed
[2025-06-17 18:19:45.534 +10:00 INF] LEP Invoicer completed with result: 0
