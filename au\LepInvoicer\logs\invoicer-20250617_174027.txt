[2025-06-17 17:40:27.498 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 17:40:27.525 +10:00 INF] Initializing FastReport...
[2025-06-17 17:40:27.597 +10:00 INF] FastReport initialized successfully
[2025-06-17 17:40:28.024 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 17:40:29.244 +10:00 INF] Starting LEP Invoicer at "2025-06-17T17:40:29.2437006+10:00"
[2025-06-17 17:40:29.248 +10:00 INF] Initializing database service...
[2025-06-17 17:40:29.252 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 17:40:29.362 +10:00 INF] Database connection established successfully
[2025-06-17 17:40:29.363 +10:00 INF] Database service initialized successfully
[2025-06-17 17:40:29.366 +10:00 INF] Checking for pending work...
[2025-06-17 17:40:29.369 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 17:40:30.298 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 17:40:30.314 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 17:40:30.330 +10:00 INF] Found 0 credits to invoice
[2025-06-17 17:40:30.335 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 17:40:30.340 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 17:40:30.360 +10:00 INF] No pending work found
[2025-06-17 17:40:30.361 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 17:40:30.363 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 17:40:30.437 +10:00 INF] LEP Invoicer completed successfully in 1194ms. No work to process.
[2025-06-17 17:40:30.444 +10:00 INF] Database connection disposed
[2025-06-17 17:40:30.445 +10:00 INF] LEP Invoicer completed with result: 0
