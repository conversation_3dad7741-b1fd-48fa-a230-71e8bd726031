[2025-06-17 16:54:42.522 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 16:54:42.549 +10:00 INF] Initializing FastReport...
[2025-06-17 16:54:42.628 +10:00 INF] FastReport initialized successfully
[2025-06-17 16:54:43.001 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 16:54:44.245 +10:00 INF] Starting LEP Invoicer at "2025-06-17T16:54:44.2452974+10:00"
[2025-06-17 16:54:44.250 +10:00 INF] Initializing database service...
[2025-06-17 16:54:44.253 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 16:54:44.347 +10:00 INF] Database connection established successfully
[2025-06-17 16:54:44.349 +10:00 INF] Database service initialized successfully
[2025-06-17 16:54:44.351 +10:00 INF] Checking for pending work...
[2025-06-17 16:54:44.354 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 16:54:45.272 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 16:54:45.275 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 16:54:45.290 +10:00 INF] Found 0 credits to invoice
[2025-06-17 16:54:45.293 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 16:54:45.298 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 16:54:45.299 +10:00 INF] No pending work found
[2025-06-17 16:54:45.303 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 16:54:45.304 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 16:54:45.403 +10:00 INF] LEP Invoicer completed successfully in 1158ms. No work to process.
[2025-06-17 16:54:45.410 +10:00 INF] Database connection disposed
[2025-06-17 16:54:45.412 +10:00 INF] LEP Invoicer completed with result: 0
