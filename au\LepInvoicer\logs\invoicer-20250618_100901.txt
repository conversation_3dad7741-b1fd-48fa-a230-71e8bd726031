[2025-06-18 10:09:01.740 +10:00 INF] Starting LEP Invoicer application
[2025-06-18 10:09:01.779 +10:00 INF] Initializing FastReport...
[2025-06-18 10:09:01.871 +10:00 INF] FastReport initialized successfully
[2025-06-18 10:09:02.523 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-18 10:09:03.801 +10:00 INF] Starting LEP Invoicer at "2025-06-18T10:09:03.8016234+10:00"
[2025-06-18 10:09:03.805 +10:00 INF] Initializing database service...
[2025-06-18 10:09:03.808 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-18 10:09:03.910 +10:00 INF] Database connection established successfully
[2025-06-18 10:09:03.912 +10:00 INF] Database service initialized successfully
[2025-06-18 10:09:03.915 +10:00 INF] Checking for pending work...
[2025-06-18 10:09:03.918 +10:00 INF] Getting 500 orders to invoice
[2025-06-18 10:09:04.782 +10:00 INF] Found 0 orders to invoice (filtered 16 candidates)
[2025-06-18 10:09:04.785 +10:00 INF] Getting 100 credits to invoice
[2025-06-18 10:09:04.797 +10:00 INF] Found 0 credits to invoice
[2025-06-18 10:09:04.798 +10:00 INF] Getting 100 refunds to invoice
[2025-06-18 10:09:04.802 +10:00 INF] Found 0 refunds to invoice
[2025-06-18 10:09:04.804 +10:00 INF] No pending work found
[2025-06-18 10:09:04.806 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-18 10:09:04.807 +10:00 INF] Cleaning up invoicer logs...
[2025-06-18 10:09:04.879 +10:00 INF] LEP Invoicer completed successfully in 1077ms. No work to process.
[2025-06-18 10:09:04.885 +10:00 INF] Database connection disposed
[2025-06-18 10:09:04.887 +10:00 INF] LEP Invoicer completed with result: 0
