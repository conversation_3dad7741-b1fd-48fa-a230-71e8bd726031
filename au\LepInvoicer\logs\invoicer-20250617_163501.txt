[2025-06-17 16:35:01.825 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 16:35:01.857 +10:00 INF] Initializing FastReport...
[2025-06-17 16:35:01.956 +10:00 INF] FastReport initialized successfully
[2025-06-17 16:35:02.554 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 16:35:04.048 +10:00 INF] Starting LEP Invoicer at "2025-06-17T16:35:04.0477786+10:00"
[2025-06-17 16:35:04.051 +10:00 INF] Initializing database service...
[2025-06-17 16:35:04.054 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 16:35:04.150 +10:00 INF] Database connection established successfully
[2025-06-17 16:35:04.152 +10:00 INF] Database service initialized successfully
[2025-06-17 16:35:04.156 +10:00 INF] Checking for pending work...
[2025-06-17 16:35:04.160 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 16:35:05.138 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 16:35:05.141 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 16:35:05.153 +10:00 INF] Found 0 credits to invoice
[2025-06-17 16:35:05.157 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 16:35:05.162 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 16:35:05.164 +10:00 INF] No pending work found
[2025-06-17 16:35:05.166 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 16:35:05.170 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 16:35:05.248 +10:00 INF] LEP Invoicer completed successfully in 1200ms. No work to process.
[2025-06-17 16:35:05.255 +10:00 INF] Database connection disposed
[2025-06-17 16:35:05.257 +10:00 INF] LEP Invoicer completed with result: 0
