﻿global using System;
global using System.Collections.Generic;
global using Microsoft.Data.SqlClient;
global using System.IO;
global using System.Linq;
global using System.Net.Mail;
global using System.Net;
global using System.Text;
global using System.Threading.Tasks;
global using System.Text.RegularExpressions;
global using lep.job;
global using lep.order;
global using lep.user;
global using lep;

global using LepCore.Dto;
global using MYOB.AccountRight.SDK;
global using MYOB.AccountRight.SDK.Contracts;
global using MYOB.AccountRight.SDK.Services.Sale;
global using MYOB.AccountRight.SDK.Services.Contact;
global using MYOB.AccountRight.SDK.Services.Inventory;
global using MYOB.AccountRight.SDK.Services.GeneralLedger;
global using MYOB.AccountRight.SDK.Contracts.Version2.GeneralLedger;
global using MYOB.AccountRight.SDK.Contracts.Version2.Contact;
global using AutoMapper;
global using NHibernate.Linq;
global using MYOB.AccountRight.SDK.Services;
global using Myob = MYOB.AccountRight.SDK.Services;
global using MYOB.AccountRight.SDK.Contracts.Version2;
global using Newtonsoft.Json;
global using System.Windows.Forms;
global using MYOB.AccountRight.SDK.Contracts.Version2.Sale;
global using FastReport.Export.PdfSimple;
global using FastReport.Export.Html;
global using NHibernate.Criterion;

// New service-related usings
global using LepInvoicer.Implementations;
global using LepInvoicer.Interfaces;

// Real types from the LEP system
global using IOrder = lep.order.IOrder;
global using OrderCredit = lep.order.OrderCredit;
global using AccountLink = MYOB.AccountRight.SDK.Contracts.Version2.GeneralLedger.AccountLink;
global using IJob = lep.job.IJob;
global using ICustomerUser = lep.user.ICustomerUser;
