[2025-06-17 15:26:59.681 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 15:26:59.710 +10:00 INF] Initializing FastReport...
[2025-06-17 15:26:59.793 +10:00 INF] FastReport initialized successfully
[2025-06-17 15:27:00.283 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 15:27:01.586 +10:00 INF] Starting LEP Invoicer at "2025-06-17T15:27:01.5861436+10:00"
[2025-06-17 15:27:01.591 +10:00 INF] Initializing database service...
[2025-06-17 15:27:01.595 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 15:27:01.724 +10:00 INF] Database connection established successfully
[2025-06-17 15:27:01.725 +10:00 INF] Database service initialized successfully
[2025-06-17 15:27:01.729 +10:00 INF] Checking for pending work...
[2025-06-17 15:27:01.732 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 15:27:02.916 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 15:27:02.919 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 15:27:02.933 +10:00 INF] Found 0 credits to invoice
[2025-06-17 15:27:02.935 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 15:27:02.941 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 15:27:02.943 +10:00 INF] No pending work found
[2025-06-17 15:27:02.945 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 15:27:02.950 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 15:27:03.067 +10:00 INF] LEP Invoicer completed successfully in 1480ms. No work to process.
[2025-06-17 15:27:03.073 +10:00 INF] Database connection disposed
[2025-06-17 15:27:03.076 +10:00 INF] LEP Invoicer completed with result: 0
