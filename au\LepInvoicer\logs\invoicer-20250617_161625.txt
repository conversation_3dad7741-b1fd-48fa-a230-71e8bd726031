[2025-06-17 16:16:25.467 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 16:16:25.502 +10:00 INF] Initializing FastReport...
[2025-06-17 16:16:25.575 +10:00 INF] FastReport initialized successfully
[2025-06-17 16:16:25.980 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 16:16:27.265 +10:00 INF] Starting LEP Invoicer at "2025-06-17T16:16:27.2653329+10:00"
[2025-06-17 16:16:27.269 +10:00 INF] Initializing database service...
[2025-06-17 16:16:27.272 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 16:16:27.385 +10:00 INF] Database connection established successfully
[2025-06-17 16:16:27.387 +10:00 INF] Database service initialized successfully
[2025-06-17 16:16:27.390 +10:00 INF] Checking for pending work...
[2025-06-17 16:16:27.393 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 16:16:28.308 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 16:16:28.311 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 16:16:28.324 +10:00 INF] Found 0 credits to invoice
[2025-06-17 16:16:28.326 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 16:16:28.329 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 16:16:28.333 +10:00 INF] No pending work found
[2025-06-17 16:16:28.335 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 16:16:28.341 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 16:16:28.450 +10:00 INF] LEP Invoicer completed successfully in 1184ms. No work to process.
[2025-06-17 16:16:28.456 +10:00 INF] Database connection disposed
[2025-06-17 16:16:28.459 +10:00 INF] LEP Invoicer completed with result: 0
