[2025-06-18 09:56:59.538 +10:00 INF] Starting LEP Invoicer application
[2025-06-18 09:56:59.564 +10:00 INF] Initializing FastReport...
[2025-06-18 09:56:59.634 +10:00 INF] FastReport initialized successfully
[2025-06-18 09:57:00.104 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-18 09:57:01.662 +10:00 INF] Starting LEP Invoicer at "2025-06-18T09:57:01.6614998+10:00"
[2025-06-18 09:57:01.665 +10:00 INF] Initializing database service...
[2025-06-18 09:57:01.669 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-18 09:57:01.773 +10:00 INF] Database connection established successfully
[2025-06-18 09:57:01.774 +10:00 INF] Database service initialized successfully
[2025-06-18 09:57:01.776 +10:00 INF] Checking for pending work...
[2025-06-18 09:57:01.779 +10:00 INF] Getting 500 orders to invoice
[2025-06-18 09:57:02.783 +10:00 INF] Found 0 orders to invoice (filtered 16 candidates)
[2025-06-18 09:57:02.787 +10:00 INF] Getting 100 credits to invoice
[2025-06-18 09:57:02.803 +10:00 INF] Found 0 credits to invoice
[2025-06-18 09:57:02.806 +10:00 INF] Getting 100 refunds to invoice
[2025-06-18 09:57:02.817 +10:00 INF] Found 0 refunds to invoice
[2025-06-18 09:57:02.819 +10:00 INF] No pending work found
[2025-06-18 09:57:02.820 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-18 09:57:02.821 +10:00 INF] Cleaning up invoicer logs...
[2025-06-18 09:57:02.915 +10:00 INF] LEP Invoicer completed successfully in 1253ms. No work to process.
[2025-06-18 09:57:02.927 +10:00 INF] Database connection disposed
[2025-06-18 09:57:02.929 +10:00 INF] LEP Invoicer completed with result: 0
