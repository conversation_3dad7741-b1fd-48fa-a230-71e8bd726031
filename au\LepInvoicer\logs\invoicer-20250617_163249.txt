[2025-06-17 16:32:50.056 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 16:32:50.084 +10:00 INF] Initializing FastReport...
[2025-06-17 16:32:50.160 +10:00 INF] FastReport initialized successfully
[2025-06-17 16:32:50.655 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 16:32:51.994 +10:00 INF] Starting LEP Invoicer at "2025-06-17T16:32:51.9936801+10:00"
[2025-06-17 16:32:52.000 +10:00 INF] Initializing database service...
[2025-06-17 16:32:52.007 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 16:32:52.110 +10:00 INF] Database connection established successfully
[2025-06-17 16:32:52.111 +10:00 INF] Database service initialized successfully
[2025-06-17 16:32:52.114 +10:00 INF] Checking for pending work...
[2025-06-17 16:32:52.118 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 16:32:53.107 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 16:32:53.111 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 16:32:53.124 +10:00 INF] Found 0 credits to invoice
[2025-06-17 16:32:53.126 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 16:32:53.130 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 16:32:53.131 +10:00 INF] No pending work found
[2025-06-17 16:32:53.133 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 16:32:53.134 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 16:32:53.236 +10:00 INF] LEP Invoicer completed successfully in 1243ms. No work to process.
[2025-06-17 16:32:53.243 +10:00 INF] Database connection disposed
[2025-06-17 16:32:53.245 +10:00 INF] LEP Invoicer completed with result: 0
