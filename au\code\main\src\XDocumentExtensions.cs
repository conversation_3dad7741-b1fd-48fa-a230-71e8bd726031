using System.Text;
using System.Xml;
using System.Xml.Linq;

namespace lep
{
	//public static class XDocumentExtensions
 //   {
 //       public static void SaveWithOutBOM(this XDocument doc, string filename)
 //       {
 //           doc.DocumentType.InternalSubset = null;
 //           var settings = new XmlWriterSettings
 //           {
 //               Indent = true,
 //               Encoding = new UTF8Encoding(false)
 //           };
 //           using (var w = XmlWriter.Create(filename, settings))
 //           {
 //               doc.Save(w);
 //           }
 //       }
 //   }
}
