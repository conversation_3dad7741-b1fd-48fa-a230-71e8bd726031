[2025-06-17 17:18:40.412 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 17:18:40.439 +10:00 INF] Initializing FastReport...
[2025-06-17 17:18:40.516 +10:00 INF] FastReport initialized successfully
[2025-06-17 17:18:40.901 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 17:18:42.157 +10:00 INF] Starting LEP Invoicer at "2025-06-17T17:18:42.1576429+10:00"
[2025-06-17 17:18:42.175 +10:00 INF] Initializing database service...
[2025-06-17 17:18:42.178 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 17:18:42.293 +10:00 INF] Database connection established successfully
[2025-06-17 17:18:42.299 +10:00 INF] Database service initialized successfully
[2025-06-17 17:18:42.302 +10:00 INF] Checking for pending work...
[2025-06-17 17:18:42.305 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 17:18:43.192 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 17:18:43.194 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 17:18:43.207 +10:00 INF] Found 0 credits to invoice
[2025-06-17 17:18:43.209 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 17:18:43.213 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 17:18:43.214 +10:00 INF] No pending work found
[2025-06-17 17:18:43.216 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 17:18:43.218 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 17:18:43.290 +10:00 INF] LEP Invoicer completed successfully in 1133ms. No work to process.
[2025-06-17 17:18:43.297 +10:00 INF] Database connection disposed
[2025-06-17 17:18:43.298 +10:00 INF] LEP Invoicer completed with result: 0
