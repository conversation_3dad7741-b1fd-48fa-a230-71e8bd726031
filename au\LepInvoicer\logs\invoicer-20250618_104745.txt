[2025-06-18 10:47:45.365 +10:00 INF] Starting LEP Invoicer application
[2025-06-18 10:47:45.398 +10:00 INF] Initializing FastReport...
[2025-06-18 10:47:45.501 +10:00 INF] FastReport initialized successfully
[2025-06-18 10:47:46.131 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-18 10:47:48.520 +10:00 INF] Starting LEP Invoicer at "2025-06-18T10:47:48.5199324+10:00"
[2025-06-18 10:47:48.578 +10:00 INF] Initializing database service...
[2025-06-18 10:47:48.583 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-18 10:47:48.758 +10:00 INF] Database connection established successfully
[2025-06-18 10:47:48.763 +10:00 INF] Database service initialized successfully
[2025-06-18 10:47:48.766 +10:00 INF] Checking for pending work...
[2025-06-18 10:47:48.771 +10:00 INF] Getting 500 orders to invoice
[2025-06-18 10:47:49.979 +10:00 INF] Marking 17 zero-priced orders as invoiced
[2025-06-18 10:47:50.044 +10:00 INF] Found 0 orders to invoice (marked 17 zero-priced as invoiced, filtered 17 candidates)
[2025-06-18 10:47:50.048 +10:00 INF] Getting 100 credits to invoice
[2025-06-18 10:47:50.067 +10:00 INF] Found 0 credits to invoice
[2025-06-18 10:47:50.069 +10:00 INF] Getting 100 refunds to invoice
[2025-06-18 10:47:50.078 +10:00 INF] Found 0 refunds to invoice
[2025-06-18 10:47:50.079 +10:00 INF] No pending work found
[2025-06-18 10:47:50.080 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-18 10:47:50.082 +10:00 INF] Cleaning up invoicer logs...
[2025-06-18 10:47:50.179 +10:00 INF] LEP Invoicer completed successfully in 1659ms. No work to process.
[2025-06-18 10:47:50.190 +10:00 INF] Database connection disposed
[2025-06-18 10:47:50.192 +10:00 INF] LEP Invoicer completed with result: 0
