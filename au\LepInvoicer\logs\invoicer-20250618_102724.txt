[2025-06-18 10:27:24.495 +10:00 INF] Starting LEP Invoicer application
[2025-06-18 10:27:24.523 +10:00 INF] Initializing FastReport...
[2025-06-18 10:27:24.602 +10:00 INF] FastReport initialized successfully
[2025-06-18 10:27:25.047 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-18 10:27:26.419 +10:00 INF] Starting LEP Invoicer at "2025-06-18T10:27:26.4194790+10:00"
[2025-06-18 10:27:26.423 +10:00 INF] Initializing database service...
[2025-06-18 10:27:26.426 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-18 10:27:26.525 +10:00 INF] Database connection established successfully
[2025-06-18 10:27:26.527 +10:00 INF] Database service initialized successfully
[2025-06-18 10:27:26.529 +10:00 INF] Checking for pending work...
[2025-06-18 10:27:26.532 +10:00 INF] Getting 500 orders to invoice
[2025-06-18 10:27:27.468 +10:00 INF] Found 0 orders to invoice (filtered 16 candidates)
[2025-06-18 10:27:27.475 +10:00 INF] Getting 100 credits to invoice
[2025-06-18 10:27:27.488 +10:00 INF] Found 0 credits to invoice
[2025-06-18 10:27:27.493 +10:00 INF] Getting 100 refunds to invoice
[2025-06-18 10:27:27.498 +10:00 INF] Found 0 refunds to invoice
[2025-06-18 10:27:27.500 +10:00 INF] No pending work found
[2025-06-18 10:27:27.502 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-18 10:27:27.503 +10:00 INF] Cleaning up invoicer logs...
[2025-06-18 10:27:27.581 +10:00 INF] LEP Invoicer completed successfully in 1161ms. No work to process.
[2025-06-18 10:27:27.587 +10:00 INF] Database connection disposed
[2025-06-18 10:27:27.589 +10:00 INF] LEP Invoicer completed with result: 0
