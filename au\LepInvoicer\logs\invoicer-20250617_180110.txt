[2025-06-17 18:01:10.587 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 18:01:10.615 +10:00 INF] Initializing FastReport...
[2025-06-17 18:01:10.690 +10:00 INF] FastReport initialized successfully
[2025-06-17 18:01:11.077 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 18:01:12.312 +10:00 INF] Starting LEP Invoicer at "2025-06-17T18:01:12.3125434+10:00"
[2025-06-17 18:01:12.316 +10:00 INF] Initializing database service...
[2025-06-17 18:01:12.320 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 18:01:12.418 +10:00 INF] Database connection established successfully
[2025-06-17 18:01:12.420 +10:00 INF] Database service initialized successfully
[2025-06-17 18:01:12.437 +10:00 INF] Checking for pending work...
[2025-06-17 18:01:12.441 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 18:01:13.310 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 18:01:13.321 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 18:01:13.334 +10:00 INF] Found 0 credits to invoice
[2025-06-17 18:01:13.336 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 18:01:13.340 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 18:01:13.345 +10:00 INF] No pending work found
[2025-06-17 18:01:13.347 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 18:01:13.348 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 18:01:13.431 +10:00 INF] LEP Invoicer completed successfully in 1118ms. No work to process.
[2025-06-17 18:01:13.438 +10:00 INF] Database connection disposed
[2025-06-17 18:01:13.439 +10:00 INF] LEP Invoicer completed with result: 0
