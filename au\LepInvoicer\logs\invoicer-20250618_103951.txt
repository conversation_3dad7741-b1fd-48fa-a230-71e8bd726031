[2025-06-18 10:39:52.063 +10:00 INF] Starting LEP Invoicer application
[2025-06-18 10:39:52.116 +10:00 INF] Initializing FastReport...
[2025-06-18 10:39:52.229 +10:00 INF] FastReport initialized successfully
[2025-06-18 10:39:53.185 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-18 10:39:54.990 +10:00 INF] Starting LEP Invoicer at "2025-06-18T10:39:54.9903247+10:00"
[2025-06-18 10:39:54.995 +10:00 INF] Initializing database service...
[2025-06-18 10:39:55.005 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-18 10:39:55.127 +10:00 INF] Database connection established successfully
[2025-06-18 10:39:55.128 +10:00 INF] Database service initialized successfully
[2025-06-18 10:39:55.131 +10:00 INF] Checking for pending work...
[2025-06-18 10:39:55.134 +10:00 INF] Getting 500 orders to invoice
[2025-06-18 10:39:56.210 +10:00 INF] Found 0 orders to invoice (filtered 17 candidates)
[2025-06-18 10:39:56.213 +10:00 INF] Getting 100 credits to invoice
[2025-06-18 10:39:56.229 +10:00 INF] Found 0 credits to invoice
[2025-06-18 10:39:56.232 +10:00 INF] Getting 100 refunds to invoice
[2025-06-18 10:39:56.236 +10:00 INF] Found 0 refunds to invoice
[2025-06-18 10:39:56.237 +10:00 INF] No pending work found
[2025-06-18 10:39:56.239 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-18 10:39:56.240 +10:00 INF] Cleaning up invoicer logs...
[2025-06-18 10:39:56.333 +10:00 INF] LEP Invoicer completed successfully in 1343ms. No work to process.
[2025-06-18 10:39:56.344 +10:00 INF] Database connection disposed
[2025-06-18 10:39:56.346 +10:00 INF] LEP Invoicer completed with result: 0
