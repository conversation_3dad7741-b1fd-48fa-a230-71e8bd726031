[2025-06-18 08:38:20.592 +10:00 INF] Starting LEP Invoicer application
[2025-06-18 08:38:20.618 +10:00 INF] Initializing FastReport...
[2025-06-18 08:38:20.690 +10:00 INF] FastReport initialized successfully
[2025-06-18 08:38:21.084 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-18 08:38:22.367 +10:00 INF] Starting LEP Invoicer at "2025-06-18T08:38:22.3668895+10:00"
[2025-06-18 08:38:22.371 +10:00 INF] Initializing database service...
[2025-06-18 08:38:22.374 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-18 08:38:22.470 +10:00 INF] Database connection established successfully
[2025-06-18 08:38:22.472 +10:00 INF] Database service initialized successfully
[2025-06-18 08:38:22.475 +10:00 INF] Checking for pending work...
[2025-06-18 08:38:22.478 +10:00 INF] Getting 500 orders to invoice
[2025-06-18 08:38:23.482 +10:00 INF] Found 0 orders to invoice (filtered 16 candidates)
[2025-06-18 08:38:23.487 +10:00 INF] Getting 100 credits to invoice
[2025-06-18 08:38:23.501 +10:00 INF] Found 0 credits to invoice
[2025-06-18 08:38:23.504 +10:00 INF] Getting 100 refunds to invoice
[2025-06-18 08:38:23.519 +10:00 INF] Found 0 refunds to invoice
[2025-06-18 08:38:23.521 +10:00 INF] No pending work found
[2025-06-18 08:38:23.523 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-18 08:38:23.526 +10:00 INF] Cleaning up invoicer logs...
[2025-06-18 08:38:23.618 +10:00 INF] LEP Invoicer completed successfully in 1251ms. No work to process.
[2025-06-18 08:38:23.626 +10:00 INF] Database connection disposed
[2025-06-18 08:38:23.628 +10:00 INF] LEP Invoicer completed with result: 0
