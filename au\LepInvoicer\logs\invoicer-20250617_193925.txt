[2025-06-17 19:39:25.572 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 19:39:25.609 +10:00 INF] Initializing FastReport...
[2025-06-17 19:39:25.696 +10:00 INF] FastReport initialized successfully
[2025-06-17 19:39:26.115 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 19:39:27.396 +10:00 INF] Starting LEP Invoicer at "2025-06-17T19:39:27.3960747+10:00"
[2025-06-17 19:39:27.400 +10:00 INF] Initializing database service...
[2025-06-17 19:39:27.402 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 19:39:27.505 +10:00 INF] Database connection established successfully
[2025-06-17 19:39:27.507 +10:00 INF] Database service initialized successfully
[2025-06-17 19:39:27.509 +10:00 INF] Checking for pending work...
[2025-06-17 19:39:27.512 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 19:39:28.399 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 19:39:28.402 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 19:39:28.423 +10:00 INF] Found 0 credits to invoice
[2025-06-17 19:39:28.424 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 19:39:28.428 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 19:39:28.432 +10:00 INF] No pending work found
[2025-06-17 19:39:28.433 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 19:39:28.435 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 19:39:28.514 +10:00 INF] LEP Invoicer completed successfully in 1118ms. No work to process.
[2025-06-17 19:39:28.520 +10:00 INF] Database connection disposed
[2025-06-17 19:39:28.522 +10:00 INF] LEP Invoicer completed with result: 0
