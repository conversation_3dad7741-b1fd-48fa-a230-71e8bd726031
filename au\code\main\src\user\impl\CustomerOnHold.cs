using lep.configuration;
using lep.content;
using lep.email;
using System;

namespace lep.user.impl
{
	//public class CustomerOnHold : IInitializingObject
 //   {
 //       private IConfigurationApplication configurationApp;
 //       private IEmailApplication emailApp;
 //       private bool initialised;
 //       private IUserApplication userApp;

 //       public CustomerOnHold()
 //       {
 //       }

 //       public IUserApplication UserApplication
 //       {
 //           set { userApp = value; }
 //       }

 //       public IConfigurationApplication ConfigurationApplication
 //       {
 //           set { configurationApp = value; }
 //       }

 //       public IEmailApplication EmailApplication
 //       {
 //           set { emailApp = value; }
 //       }

 //       public void AfterPropertiesSet()
 //       {
 //           if (userApp == null)
 //           {
 //               throw new ArgumentNullException("UserApplication");
 //           }
 //           if (configurationApp == null)
 //           {
 //               throw new ArgumentNullException("ConfigurationApplication");
 //           }
 //           if (emailApp == null)
 //           {
 //               throw new ArgumentNullException("EmailApplication");
 //           }
 //           initialised = true;
 //       }

 //       public void CronTask()
 //       {
 //           if (!initialised)
 //           {
 //               throw new ApplicationException("CustomerOnHold not initialised");
 //           }
 //           var lastDateTime = DateTime.MinValue;
 //           if (DateTime.Now.Day == 15 && DateTime.Now.Hour == 23)
 //           {
 //               if (configurationApp.GetValue(Configuration.OnHoldChangeTime) != DateTime.Now.ToString("yyyy-MM"))
 //               {
 //                   ChangeCustomerPayment();
 //               }
 //           }
 //           if (DateTime.Now.Day == 7 && DateTime.Now.Hour == 23)
 //           {
 //               lastDateTime = DateTime.MinValue;
 //               if (configurationApp.GetValue(Configuration.OnHoldNotificationTime) != DateTime.Now.ToString("yyyy-MM"))
 //               {
 //                   OnHoldNotification();
 //               }
 //           }
 //       }

 //       private void ChangeCustomerPayment()
 //       {
 //           foreach (var c in userApp.FindOnHoldCustomer())
 //           {
 //               c.PaymentTerms = PaymentTermsOptions.PrePay;
 //               userApp.Save(c);
 //           }
 //           configurationApp.SetValue(Configuration.OnHoldChangeTime, DateTime.Now.ToString("yyyy-MM"));
 //       }

 //       private void OnHoldNotification()
 //       {
 //           foreach (var c in userApp.FindOnHoldCustomer())
 //           {
 //               emailApp.SendCustomerNotification(c, ContentType.CustomerOnHold);
 //           }
 //           configurationApp.SetValue(Configuration.OnHoldNotificationTime, DateTime.Now.ToString("yyyy-MM"));
 //       }
 //   }
}
