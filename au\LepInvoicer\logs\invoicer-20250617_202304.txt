[2025-06-17 20:23:04.757 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 20:23:04.786 +10:00 INF] Initializing FastReport...
[2025-06-17 20:23:04.856 +10:00 INF] FastReport initialized successfully
[2025-06-17 20:23:05.277 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 20:23:06.601 +10:00 INF] Starting LEP Invoicer at "2025-06-17T20:23:06.6008950+10:00"
[2025-06-17 20:23:06.606 +10:00 INF] Initializing database service...
[2025-06-17 20:23:06.609 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 20:23:06.711 +10:00 INF] Database connection established successfully
[2025-06-17 20:23:06.712 +10:00 INF] Database service initialized successfully
[2025-06-17 20:23:06.715 +10:00 INF] Checking for pending work...
[2025-06-17 20:23:06.718 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 20:23:07.569 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 20:23:07.571 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 20:23:07.587 +10:00 INF] Found 0 credits to invoice
[2025-06-17 20:23:07.589 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 20:23:07.592 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 20:23:07.595 +10:00 INF] No pending work found
[2025-06-17 20:23:07.599 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 20:23:07.600 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 20:23:07.683 +10:00 INF] LEP Invoicer completed successfully in 1082ms. No work to process.
[2025-06-17 20:23:07.690 +10:00 INF] Database connection disposed
[2025-06-17 20:23:07.691 +10:00 INF] LEP Invoicer completed with result: 0
