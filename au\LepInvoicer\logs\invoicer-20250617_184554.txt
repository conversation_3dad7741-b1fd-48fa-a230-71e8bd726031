[2025-06-17 18:45:54.647 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 18:45:54.677 +10:00 INF] Initializing FastReport...
[2025-06-17 18:45:54.762 +10:00 INF] FastReport initialized successfully
[2025-06-17 18:45:55.154 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 18:45:56.434 +10:00 INF] Starting LEP Invoicer at "2025-06-17T18:45:56.4337000+10:00"
[2025-06-17 18:45:56.437 +10:00 INF] Initializing database service...
[2025-06-17 18:45:56.441 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 18:45:56.537 +10:00 INF] Database connection established successfully
[2025-06-17 18:45:56.539 +10:00 INF] Database service initialized successfully
[2025-06-17 18:45:56.542 +10:00 INF] Checking for pending work...
[2025-06-17 18:45:56.545 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 18:45:57.408 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 18:45:57.410 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 18:45:57.431 +10:00 INF] Found 0 credits to invoice
[2025-06-17 18:45:57.433 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 18:45:57.436 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 18:45:57.438 +10:00 INF] No pending work found
[2025-06-17 18:45:57.442 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 18:45:57.446 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 18:45:57.527 +10:00 INF] LEP Invoicer completed successfully in 1093ms. No work to process.
[2025-06-17 18:45:57.533 +10:00 INF] Database connection disposed
[2025-06-17 18:45:57.535 +10:00 INF] LEP Invoicer completed with result: 0
