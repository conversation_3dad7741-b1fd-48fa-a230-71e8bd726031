﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <OutputType>Exe</OutputType>
        <TargetFramework>net8.0-windows7.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <UseWindowsForms>true</UseWindowsForms>
         <Nullable>disable</Nullable>
        <EnforceCodeStyleInBuild>True</EnforceCodeStyleInBuild>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="FastReport.Data.Json" Version="2024.1.0" />
        <PackageReference Include="FastReport.OpenSource" Version="2024.1.0" />
        <PackageReference Include="FastReport.OpenSource.Export.PdfSimple" Version="2024.1.0" />
        <PackageReference Include="Microsoft.Data.SqlClient" Version="6.0.2" />
        <PackageReference Include="MYOB.AccountRight.API.SDK" Version="2023.10.534" />
        <PackageReference Include="Newtonsoft.Json" Version="13.0.1" />
        <PackageReference Include="Microsoft.Extensions.Hosting" Version="8.0.0" />
        <PackageReference Include="Microsoft.Extensions.Configuration" Version="8.0.0" />
        <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="8.0.0" />
        <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.0" />
        <PackageReference Include="Microsoft.Extensions.Logging" Version="8.0.0" />
        <PackageReference Include="Microsoft.Extensions.Options.ConfigurationExtensions" Version="8.0.0" />
        <PackageReference Include="Serilog" Version="3.1.1" />
        <PackageReference Include="Serilog.Extensions.Hosting" Version="8.0.0" />
        <PackageReference Include="Serilog.Sinks.Console" Version="5.0.1" />
        <PackageReference Include="Serilog.Sinks.File" Version="5.0.0" />
        <PackageReference Include="AutoMapper" Version="12.0.0" />
    </ItemGroup>

    
    <ItemGroup>
        <ProjectReference Include="..\code\main\main.csproj" />
        <ProjectReference Include="..\LepCore.DTOs\LepCore.DTOs.csproj" />
        <ProjectReference Include="..\lumen\lumen.csproj" />
    </ItemGroup>

    
    <ItemGroup>
      <None Update="appsettings.json">
        <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      </None>
      <None Update="reports\lep-invoice-order.frx">
        <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      </None>
      <None Update="reports\lep-invoice-refund2.frx">
        <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      </None>
      <None Update="Tokens.json">
        <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      </None>
    </ItemGroup>

    <ItemGroup>
      <Content Include="reports\lep-invoice-order2.frx">
        <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      </Content>
      <Content Include="reports\lep-invoice-refund.frx">
        <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      </Content>
    </ItemGroup>

    <ItemGroup>
      <Compile Remove="docs\**" />
      <EmbeddedResource Remove="docs\**" />
      <None Remove="docs\**" />
    </ItemGroup>

</Project>
