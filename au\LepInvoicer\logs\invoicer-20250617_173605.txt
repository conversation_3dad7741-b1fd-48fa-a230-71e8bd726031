[2025-06-17 17:36:05.219 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 17:36:05.247 +10:00 INF] Initializing FastReport...
[2025-06-17 17:36:05.321 +10:00 INF] FastReport initialized successfully
[2025-06-17 17:36:05.758 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 17:36:07.093 +10:00 INF] Starting LEP Invoicer at "2025-06-17T17:36:07.0927307+10:00"
[2025-06-17 17:36:07.096 +10:00 INF] Initializing database service...
[2025-06-17 17:36:07.099 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 17:36:07.202 +10:00 INF] Database connection established successfully
[2025-06-17 17:36:07.215 +10:00 INF] Database service initialized successfully
[2025-06-17 17:36:07.219 +10:00 INF] Checking for pending work...
[2025-06-17 17:36:07.223 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 17:36:08.147 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 17:36:08.150 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 17:36:08.163 +10:00 INF] Found 0 credits to invoice
[2025-06-17 17:36:08.166 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 17:36:08.171 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 17:36:08.172 +10:00 INF] No pending work found
[2025-06-17 17:36:08.174 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 17:36:08.175 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 17:36:08.254 +10:00 INF] LEP Invoicer completed successfully in 1162ms. No work to process.
[2025-06-17 17:36:08.261 +10:00 INF] Database connection disposed
[2025-06-17 17:36:08.262 +10:00 INF] LEP Invoicer completed with result: 0
