[2025-06-17 15:42:22.698 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 15:42:22.726 +10:00 INF] Initializing FastReport...
[2025-06-17 15:42:22.809 +10:00 INF] FastReport initialized successfully
[2025-06-17 15:42:23.216 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 15:42:24.517 +10:00 INF] Starting LEP Invoicer at "2025-06-17T15:42:24.5176435+10:00"
[2025-06-17 15:42:24.528 +10:00 INF] Initializing database service...
[2025-06-17 15:42:24.531 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 15:42:24.644 +10:00 INF] Database connection established successfully
[2025-06-17 15:42:24.645 +10:00 INF] Database service initialized successfully
[2025-06-17 15:42:24.652 +10:00 INF] Checking for pending work...
[2025-06-17 15:42:24.654 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 15:42:25.595 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 15:42:25.600 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 15:42:25.622 +10:00 INF] Found 0 credits to invoice
[2025-06-17 15:42:25.629 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 15:42:25.633 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 15:42:25.647 +10:00 INF] No pending work found
[2025-06-17 15:42:25.654 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 15:42:25.656 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 15:42:25.745 +10:00 INF] LEP Invoicer completed successfully in 1227ms. No work to process.
[2025-06-17 15:42:25.753 +10:00 INF] Database connection disposed
[2025-06-17 15:42:25.755 +10:00 INF] LEP Invoicer completed with result: 0
