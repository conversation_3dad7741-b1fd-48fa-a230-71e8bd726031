[2025-06-17 17:58:58.492 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 17:58:58.524 +10:00 INF] Initializing FastReport...
[2025-06-17 17:58:58.624 +10:00 INF] FastReport initialized successfully
[2025-06-17 17:58:59.023 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 17:59:00.248 +10:00 INF] Starting LEP Invoicer at "2025-06-17T17:59:00.2477302+10:00"
[2025-06-17 17:59:00.252 +10:00 INF] Initializing database service...
[2025-06-17 17:59:00.255 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 17:59:00.354 +10:00 INF] Database connection established successfully
[2025-06-17 17:59:00.355 +10:00 INF] Database service initialized successfully
[2025-06-17 17:59:00.358 +10:00 INF] Checking for pending work...
[2025-06-17 17:59:00.361 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 17:59:01.315 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 17:59:01.321 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 17:59:01.337 +10:00 INF] Found 0 credits to invoice
[2025-06-17 17:59:01.340 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 17:59:01.347 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 17:59:01.351 +10:00 INF] No pending work found
[2025-06-17 17:59:01.353 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 17:59:01.354 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 17:59:01.457 +10:00 INF] LEP Invoicer completed successfully in 1209ms. No work to process.
[2025-06-17 17:59:01.465 +10:00 INF] Database connection disposed
[2025-06-17 17:59:01.472 +10:00 INF] LEP Invoicer completed with result: 0
