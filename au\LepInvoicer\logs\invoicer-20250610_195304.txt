[2025-06-10 19:53:04.715 +10:00 INF] Starting LEP Invoicer application
[2025-06-10 19:53:05.475 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-10 19:53:08.096 +10:00 INF] Starting LEP Invoicer at "2025-06-10T19:53:08.0963464+10:00"
[2025-06-10 19:53:08.104 +10:00 INF] Initializing database service...
[2025-06-10 19:53:08.111 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-10 19:53:08.323 +10:00 INF] Database connection established successfully
[2025-06-10 19:53:08.325 +10:00 INF] Database service initialized successfully
[2025-06-10 19:53:08.334 +10:00 INF] Checking for pending work...
[2025-06-10 19:53:08.342 +10:00 INF] Getting 500 orders to invoice
[2025-06-10 19:53:09.381 +10:00 INF] Found 2 orders to invoice
[2025-06-10 19:53:09.387 +10:00 INF] Getting 100 credits to invoice
[2025-06-10 19:53:09.436 +10:00 INF] Found 0 credits to invoice
[2025-06-10 19:53:09.441 +10:00 INF] Getting 100 refunds to invoice
[2025-06-10 19:53:09.457 +10:00 INF] Found 0 refunds to invoice
[2025-06-10 19:53:09.459 +10:00 INF] Found pending work: 2 orders
[2025-06-10 19:53:09.462 +10:00 INF] Initializing MYOB and other services...
[2025-06-10 19:53:09.466 +10:00 INF] Initializing MYOB service...
[2025-06-10 19:53:09.469 +10:00 INF] Initializing MYOB with OAuth authentication
[2025-06-10 19:53:09.473 +10:00 INF] Starting OAuth authentication flow
[2025-06-10 19:53:09.577 +10:00 INF] Using existing OAuth tokens
[2025-06-10 19:53:09.579 +10:00 INF] Initializing MYOB services with OAuth keystore
[2025-06-10 19:53:09.584 +10:00 INF] MYOB services initialized successfully
[2025-06-10 19:53:09.586 +10:00 INF] OAuth keystore set for API calls
[2025-06-10 19:53:09.589 +10:00 INF] Getting company files from MYOB
[2025-06-10 19:53:10.016 +10:00 INF] MYOB service initialized with OAuth credentials. Using company file: LEP Colour Printers Pty Ltd
[2025-06-10 19:53:10.036 +10:00 INF] Current cache stats: {"AccountsCount":269,"TaxCodesCount":18,"CustomersCount":393,"LastCacheLoad":"2025-06-10T19:31:07.5727667+10:00","CacheAge":"00:22:02.4530700","IsExpired":false,"CacheDirectory":"C:\\Users\\<USER>\\AppData\\Local\\LepInvoicer\\cache"}
[2025-06-10 19:53:10.073 +10:00 INF] Using cached MYOB data: {"AccountsCount":269,"TaxCodesCount":18,"CustomersCount":393,"LastCacheLoad":"2025-06-10T19:31:07.5727667+10:00","CacheAge":"00:22:02.4530700","IsExpired":false,"CacheDirectory":"C:\\Users\\<USER>\\AppData\\Local\\LepInvoicer\\cache"}
[2025-06-10 19:53:10.796 +10:00 INF] Using cached GST tax code
[2025-06-10 19:53:10.798 +10:00 INF] Using cached freight account
[2025-06-10 19:53:10.800 +10:00 INF] Using cached discounts account
[2025-06-10 19:53:10.801 +10:00 INF] MYOB cache initialization complete: {"AccountsCount":269,"TaxCodesCount":18,"CustomersCount":393,"LastCacheLoad":"2025-06-10T19:31:07.5727667+10:00","CacheAge":"00:22:03.2286758","IsExpired":false,"CacheDirectory":"C:\\Users\\<USER>\\AppData\\Local\\LepInvoicer\\cache"}
[2025-06-10 19:53:10.813 +10:00 INF] MYOB service initialized successfully with company file: LEP Colour Printers Pty Ltd
[2025-06-10 19:53:10.816 +10:00 INF] All services initialized successfully
[2025-06-10 19:53:10.820 +10:00 INF] Processing order invoices...
[2025-06-10 19:53:10.920 +10:00 INF] Getting 500 orders to invoice
[2025-06-10 19:53:11.251 +10:00 INF] Found 2 orders to invoice
[2025-06-10 19:53:11.253 +10:00 INF] Found 2 orders to process
[2025-06-10 19:53:11.256 +10:00 INF] Getting order 1417006
[2025-06-10 19:53:11.584 +10:00 INF] Processing order 1417006 with 1 jobs, total: ¤0.00
[2025-06-10 19:53:11.591 +10:00 WRN] Order 1417006 has zero price, marking as invoiced
[2025-06-10 19:53:11.593 +10:00 INF] Getting order 1416838
[2025-06-10 19:53:11.604 +10:00 INF] Processing order 1416838 with 1 jobs, total: ¤0.00
[2025-06-10 19:53:11.608 +10:00 WRN] Order 1416838 has zero price, marking as invoiced
[2025-06-10 19:53:11.610 +10:00 INF] Order processing completed. Processed: 2, Success: 0, Failed: 2
[2025-06-10 19:53:11.619 +10:00 INF] Processing credit invoices...
[2025-06-10 19:53:11.622 +10:00 INF] Getting 100 credits to invoice
[2025-06-10 19:53:11.630 +10:00 INF] Found 0 credits to invoice
[2025-06-10 19:53:11.638 +10:00 INF] Processing refund invoices...
[2025-06-10 19:53:11.640 +10:00 INF] Getting 100 refunds to invoice
[2025-06-10 19:53:11.651 +10:00 INF] Found 0 refunds to invoice
[2025-06-10 19:53:11.654 +10:00 INF] Cleaning up invoicer logs...
[2025-06-10 19:53:11.726 +10:00 INF] LEP Invoicer completed successfully in 3629ms. Orders: 2, Credits: 0, Refunds: 0
[2025-06-10 19:53:11.751 +10:00 INF] Database connection disposed
[2025-06-10 19:53:11.754 +10:00 INF] LEP Invoicer completed with result: 0
