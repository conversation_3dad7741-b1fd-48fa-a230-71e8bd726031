[2025-06-18 09:40:37.900 +10:00 INF] Starting LEP Invoicer application
[2025-06-18 09:40:37.947 +10:00 INF] Initializing FastReport...
[2025-06-18 09:40:38.049 +10:00 INF] FastReport initialized successfully
[2025-06-18 09:40:38.656 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-18 09:40:40.044 +10:00 INF] Starting LEP Invoicer at "2025-06-18T09:40:40.0445830+10:00"
[2025-06-18 09:40:40.049 +10:00 INF] Initializing database service...
[2025-06-18 09:40:40.052 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-18 09:40:40.164 +10:00 INF] Database connection established successfully
[2025-06-18 09:40:40.167 +10:00 INF] Database service initialized successfully
[2025-06-18 09:40:40.169 +10:00 INF] Checking for pending work...
[2025-06-18 09:40:40.172 +10:00 INF] Getting 500 orders to invoice
[2025-06-18 09:40:41.160 +10:00 INF] Found 0 orders to invoice (filtered 16 candidates)
[2025-06-18 09:40:41.163 +10:00 INF] Getting 100 credits to invoice
[2025-06-18 09:40:41.178 +10:00 INF] Found 0 credits to invoice
[2025-06-18 09:40:41.180 +10:00 INF] Getting 100 refunds to invoice
[2025-06-18 09:40:41.190 +10:00 INF] Found 0 refunds to invoice
[2025-06-18 09:40:41.192 +10:00 INF] No pending work found
[2025-06-18 09:40:41.194 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-18 09:40:41.195 +10:00 INF] Cleaning up invoicer logs...
[2025-06-18 09:40:41.278 +10:00 INF] LEP Invoicer completed successfully in 1234ms. No work to process.
[2025-06-18 09:40:41.284 +10:00 INF] Database connection disposed
[2025-06-18 09:40:41.286 +10:00 INF] LEP Invoicer completed with result: 0
