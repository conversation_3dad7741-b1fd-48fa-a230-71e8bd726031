[2025-06-18 10:01:22.661 +10:00 INF] Starting LEP Invoicer application
[2025-06-18 10:01:22.689 +10:00 INF] Initializing FastReport...
[2025-06-18 10:01:22.764 +10:00 INF] FastReport initialized successfully
[2025-06-18 10:01:23.196 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-18 10:01:24.669 +10:00 INF] Starting LEP Invoicer at "2025-06-18T10:01:24.6687689+10:00"
[2025-06-18 10:01:24.673 +10:00 INF] Initializing database service...
[2025-06-18 10:01:24.677 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-18 10:01:24.795 +10:00 INF] Database connection established successfully
[2025-06-18 10:01:24.801 +10:00 INF] Database service initialized successfully
[2025-06-18 10:01:24.804 +10:00 INF] Checking for pending work...
[2025-06-18 10:01:24.809 +10:00 INF] Getting 500 orders to invoice
[2025-06-18 10:01:25.719 +10:00 INF] Found 0 orders to invoice (filtered 16 candidates)
[2025-06-18 10:01:25.721 +10:00 INF] Getting 100 credits to invoice
[2025-06-18 10:01:25.734 +10:00 INF] Found 0 credits to invoice
[2025-06-18 10:01:25.736 +10:00 INF] Getting 100 refunds to invoice
[2025-06-18 10:01:25.740 +10:00 INF] Found 0 refunds to invoice
[2025-06-18 10:01:25.743 +10:00 INF] No pending work found
[2025-06-18 10:01:25.745 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-18 10:01:25.746 +10:00 INF] Cleaning up invoicer logs...
[2025-06-18 10:01:25.823 +10:00 INF] LEP Invoicer completed successfully in 1154ms. No work to process.
[2025-06-18 10:01:25.830 +10:00 INF] Database connection disposed
[2025-06-18 10:01:25.832 +10:00 INF] LEP Invoicer completed with result: 0
