[2025-06-17 16:42:41.462 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 16:42:41.491 +10:00 INF] Initializing FastReport...
[2025-06-17 16:42:41.561 +10:00 INF] FastReport initialized successfully
[2025-06-17 16:42:42.025 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 16:42:43.229 +10:00 INF] Starting LEP Invoicer at "2025-06-17T16:42:43.2292533+10:00"
[2025-06-17 16:42:43.233 +10:00 INF] Initializing database service...
[2025-06-17 16:42:43.235 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 16:42:43.332 +10:00 INF] Database connection established successfully
[2025-06-17 16:42:43.334 +10:00 INF] Database service initialized successfully
[2025-06-17 16:42:43.336 +10:00 INF] Checking for pending work...
[2025-06-17 16:42:43.339 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 16:42:44.610 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 16:42:44.620 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 16:42:44.633 +10:00 INF] Found 0 credits to invoice
[2025-06-17 16:42:44.635 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 16:42:44.640 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 16:42:44.642 +10:00 INF] No pending work found
[2025-06-17 16:42:44.643 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 16:42:44.645 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 16:42:44.719 +10:00 INF] LEP Invoicer completed successfully in 1490ms. No work to process.
[2025-06-17 16:42:44.725 +10:00 INF] Database connection disposed
[2025-06-17 16:42:44.737 +10:00 INF] LEP Invoicer completed with result: 0
