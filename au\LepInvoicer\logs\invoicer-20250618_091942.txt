[2025-06-18 09:19:42.531 +10:00 INF] Starting LEP Invoicer application
[2025-06-18 09:19:42.562 +10:00 INF] Initializing FastReport...
[2025-06-18 09:19:42.637 +10:00 INF] FastReport initialized successfully
[2025-06-18 09:19:43.046 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-18 09:19:44.264 +10:00 INF] Starting LEP Invoicer at "2025-06-18T09:19:44.2644509+10:00"
[2025-06-18 09:19:44.268 +10:00 INF] Initializing database service...
[2025-06-18 09:19:44.271 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-18 09:19:44.384 +10:00 INF] Database connection established successfully
[2025-06-18 09:19:44.386 +10:00 INF] Database service initialized successfully
[2025-06-18 09:19:44.388 +10:00 INF] Checking for pending work...
[2025-06-18 09:19:44.392 +10:00 INF] Getting 500 orders to invoice
[2025-06-18 09:19:45.272 +10:00 INF] Found 0 orders to invoice (filtered 16 candidates)
[2025-06-18 09:19:45.274 +10:00 INF] Getting 100 credits to invoice
[2025-06-18 09:19:45.288 +10:00 INF] Found 0 credits to invoice
[2025-06-18 09:19:45.291 +10:00 INF] Getting 100 refunds to invoice
[2025-06-18 09:19:45.295 +10:00 INF] Found 0 refunds to invoice
[2025-06-18 09:19:45.297 +10:00 INF] No pending work found
[2025-06-18 09:19:45.298 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-18 09:19:45.299 +10:00 INF] Cleaning up invoicer logs...
[2025-06-18 09:19:45.385 +10:00 INF] LEP Invoicer completed successfully in 1120ms. No work to process.
[2025-06-18 09:19:45.392 +10:00 INF] Database connection disposed
[2025-06-18 09:19:45.396 +10:00 INF] LEP Invoicer completed with result: 0
