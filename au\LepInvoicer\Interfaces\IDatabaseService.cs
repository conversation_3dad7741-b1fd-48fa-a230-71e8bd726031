namespace LepInvoicer.Interfaces;

/// <summary>
/// Database service interface for LEP Invoicer operations
/// </summary>
public interface IDatabaseService : IDisposable
{
	/// <summary>
	/// Initialize database connection and NHibernate session
	/// </summary>
	Task Initialize();

	/// <summary>
	/// Get orders that need to be invoiced
	/// </summary>
	Task<List<KeyValuePair<int, string>>> GetOrdersToInvoice(int batchSize);

	/// <summary>
	/// Get credits that need to be invoiced
	/// </summary>
	Task<List<OrderCredit>> GetCreditsToInvoice(int batchSize);

	/// <summary>
	/// Get refunds that need to be invoiced
	/// </summary>
	Task<List<OrderCredit>> GetRefundsToInvoice(int batchSize);

	/// <summary>
	/// Get a specific order by ID
	/// </summary>
	Task<IOrder> GetOrder(int orderId);

	/// <summary>
	/// Get orders that were successfully invoiced after a specific date
	/// </summary>
	Task<List<IOrder>> GetInvoicedOrdersAfterDate(DateTime fromDate);

	/// <summary>
	/// Mark order as successfully invoiced
	/// </summary>
	Task MarkOrderInvoiced(int orderId);

	/// <summary>
	/// Mark order as failed with error details
	/// </summary>
	Task MarkOrderFailed(int orderId, string errorDetails);

	/// <summary>
	/// Mark credit as invoiced
	/// </summary>
	Task MarkCreditInvoiced(int creditId);

	/// <summary>
	/// Mark credit as failed
	/// </summary>
	Task MarkCreditFailed(int creditId);

	/// <summary>
	/// Log invoicing result to Invoicer2Log table
	/// </summary>
	Task LogInvoicingResult(int orderId, int jobCount, decimal total, DateTime finishDate, bool success, string details);

	/// <summary>
	/// Cleanup old invoicer logs
	/// </summary>
	Task CleanupInvoicerLogs();

	/// <summary>
	/// Get summary of OrderCredit types for debugging
	/// </summary>
	Task<Dictionary<string, int>> GetOrderCreditTypeSummary();

	/// <summary>
	/// Get sales consultant ID by name
	/// </summary>
	Task<int?> GetSalesConsultantId(string salesConsultantName);

	/// <summary>
	/// Create Invoicer2Log entries for 10 credits of each type
	/// </summary>
	Task CreateInvoicer2LogEntriesForCredits();
}
