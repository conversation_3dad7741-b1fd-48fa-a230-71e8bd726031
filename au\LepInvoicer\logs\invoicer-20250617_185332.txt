[2025-06-17 18:53:32.633 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 18:53:32.661 +10:00 INF] Initializing FastReport...
[2025-06-17 18:53:32.730 +10:00 INF] FastReport initialized successfully
[2025-06-17 18:53:33.154 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 18:53:34.391 +10:00 INF] Starting LEP Invoicer at "2025-06-17T18:53:34.3915104+10:00"
[2025-06-17 18:53:34.395 +10:00 INF] Initializing database service...
[2025-06-17 18:53:34.399 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 18:53:34.509 +10:00 INF] Database connection established successfully
[2025-06-17 18:53:34.510 +10:00 INF] Database service initialized successfully
[2025-06-17 18:53:34.513 +10:00 INF] Checking for pending work...
[2025-06-17 18:53:34.516 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 18:53:35.393 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 18:53:35.395 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 18:53:35.408 +10:00 INF] Found 0 credits to invoice
[2025-06-17 18:53:35.409 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 18:53:35.413 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 18:53:35.414 +10:00 INF] No pending work found
[2025-06-17 18:53:35.416 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 18:53:35.417 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 18:53:35.490 +10:00 INF] LEP Invoicer completed successfully in 1098ms. No work to process.
[2025-06-17 18:53:35.497 +10:00 INF] Database connection disposed
[2025-06-17 18:53:35.499 +10:00 INF] LEP Invoicer completed with result: 0
