[2025-06-18 10:37:37.577 +10:00 INF] Starting LEP Invoicer application
[2025-06-18 10:37:37.610 +10:00 INF] Initializing FastReport...
[2025-06-18 10:37:37.682 +10:00 INF] FastReport initialized successfully
[2025-06-18 10:37:38.231 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-18 10:37:41.088 +10:00 INF] Starting LEP Invoicer at "2025-06-18T10:37:41.0880990+10:00"
[2025-06-18 10:37:41.092 +10:00 INF] Initializing database service...
[2025-06-18 10:37:41.095 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-18 10:37:41.205 +10:00 INF] Database connection established successfully
[2025-06-18 10:37:41.206 +10:00 INF] Database service initialized successfully
[2025-06-18 10:37:41.209 +10:00 INF] Checking for pending work...
[2025-06-18 10:37:41.212 +10:00 INF] Getting 500 orders to invoice
[2025-06-18 10:37:42.143 +10:00 INF] Found 0 orders to invoice (filtered 17 candidates)
[2025-06-18 10:37:42.148 +10:00 INF] Getting 100 credits to invoice
[2025-06-18 10:37:42.164 +10:00 INF] Found 0 credits to invoice
[2025-06-18 10:37:42.167 +10:00 INF] Getting 100 refunds to invoice
[2025-06-18 10:37:42.172 +10:00 INF] Found 0 refunds to invoice
[2025-06-18 10:37:42.179 +10:00 INF] No pending work found
[2025-06-18 10:37:42.181 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-18 10:37:42.183 +10:00 INF] Cleaning up invoicer logs...
[2025-06-18 10:37:42.298 +10:00 INF] LEP Invoicer completed successfully in 1209ms. No work to process.
[2025-06-18 10:37:42.306 +10:00 INF] Database connection disposed
[2025-06-18 10:37:42.309 +10:00 INF] LEP Invoicer completed with result: 0
