using lep.address.impl;
using lep.configuration;
using lep.content;
using lep.courier;
using lep.despatch;
using lep.despatch.impl;
using lep.email;
using lep.extensionmethods;
using lep.freight;
using lep.job;
using lep.jobmonitor;
using lep.pricing;
using lep.promotion;
using lep.run;
using lep.security;
using lep.user;
using NHibernate;
using NHibernate.Criterion;
using Serilog;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Xml.Linq;
using System.Xml.XPath;
using lep.job.impl;
using static lep.job.Facility;
using static lep.job.JobCelloglazeOptions;
using static lep.job.JobStatusOptions;
using static lep.job.JobTypeOptions;
using static lep.OrderPaymentStatusOptions;
using static lep.OrderStatusOptions;
using static lep.PaymentTermsOptions;
using static NHibernate.Criterion.Restrictions;
using static NHibernate.Impl.CriteriaImpl;

namespace lep.order.impl
{
	/// <summary>
	/// </summary>
	public partial class OrderApplication : BaseApplication, IOrderApplication
	{
		// private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

		private IConfigurationApplication _configApp;
		private IEmailApplication emailApp;
		private IFreightApplication freightApp;
		private IRunApplication _runApp;
		private IJobApplication jobApp;

		private IPricingEngine priceEngine;
		private IPromotionApplication promotionApp;
		private IUserApplication userApp;
		private readonly LabelPrinterApplication _labelPrinterApplication;
		private readonly LepArtworkScriptFiles _lepArtworkScriptFiles;
 
		public OrderApplication(
			ISession sf,
			ISecurityApplication _securityApp,
			IJobApplication jobApp,
			IRunApplication runApplication,
			IConfigurationApplication configApp,
			IEmailApplication emailApp,
			IUserApplication userApp,
			IFreightApplication freightApp,
			IPromotionApplication promotionApp,
			IPricingEngine priceEngine,
			LabelPrinterApplication labelPrinterApplication,
			PrintEngine printEngine,
			LepArtworkScriptFiles lepArtworkScriptFiles

		) : base(sf, _securityApp)
		{
			this.jobApp = jobApp;
			this._configApp = configApp;
			this.emailApp = emailApp;
			this.userApp = userApp;
			this.freightApp = freightApp;
			this.promotionApp = promotionApp;
			this.priceEngine = priceEngine;
			this._runApp = runApplication;
			PrintEngine = printEngine;
			_labelPrinterApplication = labelPrinterApplication;
			_lepArtworkScriptFiles = lepArtworkScriptFiles;
		}
 

		public void CronTask_ArchiveOrders()
		{
			try
			{
				var archiveday = 0;
				int.TryParse(_configApp.GetValue(Configuration.ArchiveOrdersPeriod), out archiveday);

				var orderIds = Session.CreateCriteria(typeof(IOrder), "ox")
					.Add(Eq("ox.Status", OrderStatusOptions.Dispatched))
					.Add(Lt("ox.DispatchDate", DateTime.Now.AddDays(-archiveday)))
					.Add(Gt("ox.DispatchDate", DateTime.Now.AddYears(-15)))
					.AddOrder(NHibernate.Criterion.Order.Desc("ox.Id"))
					.SetMaxResults(20)
					.SetProjection(
						Projections.Distinct(
							Projections.ProjectionList().Add(Projections.Property("ox.Id"), "OrderId")))
					.List<int>();

				if (archiveday > 0)
					foreach (var orderId in orderIds)
						try
						{
							//RunInTransaction(() => {
							var orderX = GetOrder(orderId);

							//Session.Refresh(orderX);
							foreach (var j in orderX.Jobs) j.Status = Complete;
							//MoveOrderFile(orderX);
							orderX.FileRemoved = true;
							orderX.Status = Archived;

							base.Save(orderX);
							//Save(orderX);
							Log.Debug("Archived order " + orderId);
							//});
						}
						catch (Exception ex)
						{
							Log.Error(ex.Message);
						}
			}
			catch (Exception ex)
			{
				var m = ex.Message;
				throw;
			}
			try
			{
				if (DateTime.Now.Hour == 23) foreach (var o in FindEmptyOrder()) Delete(o);
			}
			catch (Exception ex)
			{
				var m = ex.Message;
				throw;
			}

			var orderIds2 = Session.CreateCriteria(typeof(IOrder), "ox")
			   .Add(Lt("ox.DateCreated", DateTime.Now.AddYears(-5)))
			   .AddOrder(NHibernate.Criterion.Order.Asc("ox.Id"))
			   .SetMaxResults(20000)
			   .SetProjection(
				   Projections.Distinct(
					   Projections.ProjectionList().Add(Projections.Property("ox.Id"), "OrderId")))
			   .List<int>();

			foreach (var orderId in orderIds2)
			{
				var orderX = GetOrder(orderId);
				Delete(orderX);
			}
		}

		#region IOrderApplication Members

		public IOrder NewOrder()
		{
			//AssertPermission("order.create");
			IOrder order = new Order();
			order.DateCreated = DateTime.Now;
			// at this point we dont know the customer so shouldnt assign GST
			// order.GST = decimal.Parse(configApp.GetValue(configuration.Configuration.GST));
			order.GST = 0;
			order.DeliveryAddress = new PhysicalAddress();
			order.PackDetail.FGCourier = CourierType.None;
			order.PackDetail.PMCourier = CourierType.None;
			order.Courier = CourierType.None;

			return order;
		}

		public IOrder NewOrder(ICustomerUser customer)
		{
			//AssertPermission("order.create");
			IOrder order = new Order(customer);
			order.DateCreated = order.DateModified = DateTime.Now;

			if (order.Customer.IsChargedGST) order.GST = decimal.Parse(_configApp.GetValue(Configuration.GST));
			else order.GST = 0;

			order.PackDetail.FGCourier = CourierType.None;
			order.PackDetail.PMCourier = CourierType.None;
			order.Courier = CourierType.None;

			order.FreightPriceCode = customer.FreightPriceCode;

			if (order.Customer.PaymentTerms == Account)
				order.PaymentStatus = NotNeeded;
			else order.PaymentStatus = AwaitingPayment;

			return order;
		}

		public IOrder GetOrder(int Id)
		{
			//AssertPermission("order.read");
			return Get<IOrder>(Id);
		}

		public ISession BaseSession => Session;

		//use evict load to avoid refresh child collection bug
		public IOrder Refresh(IOrder order)
		{
			var id = order.Id;
			Session.Evict(order);
			return Session.Get<IOrder>(id);
		}

		public void Submit(IOrder order, IUser submittingUser)
		{
			var facilityFromDeliveryPostCode =  GetProductionFacilityByPostCode(order.DeliveryAddress.Postcode);
			if (facilityFromDeliveryPostCode == null)
				facilityFromDeliveryPostCode = FG;

			Facility? facilityFromCustomerSelectedPickup = null;

			if (order.Courier.IsPickupFG && order.Jobs.All(j => jobApp.IsFacilityAvailable(FG, j)))
			{
				facilityFromCustomerSelectedPickup = FG;
			}
			else if (order.Courier.IsPickupPM && order.Jobs.All(j => jobApp.IsFacilityAvailable(PM, j)))
			{
				facilityFromCustomerSelectedPickup = PM;
			}

			order.FacilityAtSubmit = facilityFromCustomerSelectedPickup ?? facilityFromDeliveryPostCode;

			//reset price
			//Mike Greenan - investigate reason for this code (apart from autoExpire - e.g order created 6 months aga, price rise etc.
			if (submittingUser is ICustomerUser)
				foreach (var j in order.Jobs)
					if ( /*j.Enable && */order.IsQuote == false && j.IsAutoPriceExpired)
					{
						var modQty = 0;
						var productpriceCode = "";
						productpriceCode = ((ICustomerUser)submittingUser).ProductPriceCode;
						var price = priceEngine.PriceJob(j, new StringBuilder(), out modQty, productpriceCode);
						j.SetPrice(submittingUser, price, 0, false);
					}

			((Order)order).Submit(submittingUser);

			foreach (var j in order.Jobs)
			{
				if (j.QuoteOutcome == QuoteStatus.WonPendingSubmitted.ToString())
					j.QuoteOutcome = QuoteStatus.Won.ToString();
			}
				
			//Get latest Dispatch estimate
			var estimated = true;
			order.DispatchEst = GetDispatchDate(order, DateTime.Now, out estimated);

			Save(order);

			freightApp.LEPInternalFreightSelection(order);
			Save(order);

			CreditLimitCheck(order.Customer);

			promotionApp.UpdateUsedPromotion(order);

			if (submittingUser is ICustomerUser)
			{
				//var track = false;
				var quote = order.Jobs.Any(_ => string.IsNullOrEmpty(_.Price));

				emailApp.SendNotification(order, quote ? ContentType.JobQuoteRequestSubmit : ContentType.OrderSubmit);

				foreach (var job in order.Jobs)
					if (string.IsNullOrEmpty(job.Price))
						jobApp.PrintJob(job);
			}

			foreach (var j in order.Jobs)
				jobApp.CreateArtworkScript(j);

			if (order.Jobs.All(x => x.ReOrderSourceJobId != 0 && !x.IsNCRBook() && !x.IsEnvelope()
				&& !string.IsNullOrEmpty(x.Price)
				//&& !x.IsBusinessCard() && !x.IsDigital()
			))
			{
				// LORD-537
				// After submit order
				// try to make all the reordered jobs Prefligt done
				// then try to push the order to ready state so the jobs show up in runs list


				Log.Information("LORD-1324 LORD-1273");

				// move jobs to preflight done
				order.Jobs.Where(j => j.ReOrderSourceJobId != 0).ForEach(j =>
				{
					j.JobSaveState(submittingUser, false);
					if (j.Status == JobStatusOptions.PreflightDone && j.InvolvesOutwork)
					{
						//emailApp.SendRaisePurchaseOrderForJobToAcounts(j);
					}
				});

				try
				{
					var user = (submittingUser is ICustomerUser) ? userApp.GetSystemUser() : (IStaff)submittingUser;
					SubmitReady(order, user);
					
				}
				catch (Exception ex)
				{
					Log.Error(ex.Message);
				}
			}

			order.Customer.LastOrderDate = DateTime.Now;
			userApp.Save(order.Customer);
		}


		public bool consideredForGanging(IJob job)
		{
			var gsms = new[] { 310, 360, 420 };
			var stocks = new[] { 29, 31 };

			return (gsms.Contains(job.FinalStock.GSM) || stocks.Contains(job.FinalStock.Id))
				&& job.Template.Is(BusinessCard, BusinessCardNdd, Postcard);
		}


		/// <summary>
		/// Submit an Order as Ready Status so that Production can begin
		/// Checks first if the customer submitted it
		/// Pushes jobs underneath it to appropiate statuses
		/// </summary>
		/// <param name="order"></param>
		/// <param name="staff"></param>
		public void SubmitReady(IOrder order, IStaff staff)
		{
			if (order.Status != OrderStatusOptions.Submitted)
			{
				throw new Exception("order is not submitted");
			}

			bool IsReady = order.Jobs.All(j => j.Status == PreflightDone || j.Status == DPCPreProduction);
			if (!IsReady)
				throw new Exception("order not ready. Not all jobs are at status PreflightDone or DPCProduction");

			if (!order.Price.HasValue)
			{
				throw new Exception("order price does not have a value");
			}
			decimal limit = 0;
			decimal.TryParse(_configApp.GetValue(Configuration.OrderLimit), out limit);
			if (order.Price.HasValue && order.Price.Value > limit)
			{
				throw new Exception("order price exceed limit");
			}

			order.Ready(staff);
			Save(order);

			//foreach (var job in order.Jobs)
			//{
			//	if (job.Status == PreflightDone && job.IsDigital())
			//	{
			//		if (job.IsSDD())
			//		{
			//			job.SetStatus(DPCPreProduction, staff);
			//		}
			//		else if (consideredForGanging(job))
			//		{
			//			// do not push to DPC, consider for ganging
			//		}
			//		else
			//		{
			//			job.SetStatus(DPCPreProduction, staff);
			//		}
						

			//		jobApp.Save(job);
					
			//		if(job.Status == JobStatusOptions.DPCPreProduction)
					
			//		_labelPrinterApplication.Print(LabelType.DPCProcessing, job);
			//		PrintEngine.ReceivePrintQueue();
					
			//	}
			//}			//foreach (var job in order.Jobs)
			//{
			//	if (job.Status == PreflightDone && job.IsDigital())
			//	{
			//		if (job.IsSDD())
			//		{
			//			job.SetStatus(DPCPreProduction, staff);
			//		}
			//		else if (consideredForGanging(job))
			//		{
			//			// do not push to DPC, consider for ganging
			//		}
			//		else
			//		{
			//			job.SetStatus(DPCPreProduction, staff);
			//		}
						

			//		jobApp.Save(job);
					
			//		if(job.Status == JobStatusOptions.DPCPreProduction)
					
			//		_labelPrinterApplication.Print(LabelType.DPCProcessing, job);
			//		PrintEngine.ReceivePrintQueue();
					
			//	}
			//}
				

			foreach (var job in order.Jobs)
				if (job.Status == PreflightDone) //&& job.IsWideFormat()
				{
					if (job.PrintType == PrintType.N || job.IsNCRBook() || job.IsEnvelope())
					{
						job.SetStatus(Outwork, staff);
						jobApp.Save(job);
						emailApp.SendRaisePurchaseOrderForJobToAcounts(job);
					} 
					else if (job.PrintType == PrintType.W)
					{
						job.SetStatus(WideFormatProduction, staff);
						jobApp.Save(job);
						_labelPrinterApplication.Print(LabelType.WideFormatProcessing, job);
						PrintEngine.ReceivePrintQueue();
					}
				}

			Save(order); // in jobhbm inverse=true, so this line is necessary

			try
			{
				foreach (var job in order.Jobs)
				{
					if(job.PrintType == PrintType.O)
					{
						if ((job.IsPresentationFolder()) // offset presentation folders 
							|| (job.Template.Is(Poster) && job.FinishedSize.PaperSize.Name.Is("A1")) // offset A1 posters
							|| (job.Template.Is(Poster) && job.FinishedSize.PaperSize.Name.Is("A2") && job.Stock.Name.Contains("Matt")) // offset A2 posters on Matt stock
							|| (job.IsMagazine() && job.BindingOption.Id.Is(1/*Saddle*/, 3/*Burst*/)) // book work with Saddle and burst
							)
						{
							var run = _runApp.NewRun(job);
							run.AddJob(job, (IStaff)CurrentUser, false);
							run.SetStatus(RunStatusOptions.LayoutRequired, CurrentUser);
							_runApp.Save(run);
							job.AddComment((IStaff)CurrentUser, $"Auto assigned to Run# {run.Id}", true);
							jobApp.Save(job);
						}
					}
				}
			} catch (Exception ex)
			{
				Log.Error("LORD-1262", ex.Message);
			}
		}

		public void CreditLimitCheck(ICustomerUser customer)
		{
			if (customer.PaymentTerms == Account ||
				customer.PaymentTerms == OnHold)
				if (userApp.GetCustomerAvailableBalance(customer.Id) < 0)
				{
					//Place customer onhold
					customer.PaymentTerms = OnHold;
					//Log the event
					Log.Information("CreditLimitCheck Event for customer " + customer.Username);
					//Email the details
					var email = emailApp.NewHtmlMessage(SiteLocation.AU);
					email.Subject = customer.Name + " (Username:'" + customer.Username +
									"') has exceeded their credit limit.";
					email.Body =
						"<HTML><body><H3>This customer has exceeded their credit limit and requires your urgent attention.</H3><ul><li>Customer Name:" +
						customer.Name + "</li><li>Credit Limit:" + string.Format("{0:C0}", customer.CreditLimit) +
						"</li><li>MYOB balance:$" + string.Format("{0:C0}", customer.MYOBBalance) +
						"</li><li>LEP Online Balance:" +
						string.Format("{0:C0}", userApp.GetCustomerLEPOnlineBalance(customer.Id)) +
						"</li><li><strong><em>Available balance:" +
						string.Format("{0:C0}", userApp.GetCustomerAvailableBalance(customer.Id)) +
						"</strong></em></li></ul></body></html>";
					email.To = _configApp.GetValue(Configuration.EmailCustomerCreditLimitExceeded);
					emailApp.Send(email);
				}
		}

		//public void AutoAssignJobFacility(IJob job)
		//{
		//	if (job.IsCustomFacility && job.Facility.HasValue) return;

		//	if (job.Order != null &&
		//		(job.Order.Status == OrderStatusOptions.Open || job.Order.Status == OrderStatusOptions.Submitted)) {
		//		job.IsCustomFacility = false;
		//		var facility = _configApp.GetProductionFacilityByPostCode(job.Order.DeliveryAddress.Postcode);
		//		if (facility != null)
		//			if (jobApp.IsFacilityAvailable(facility.Value, job.PrintType, job.Template, job.Stock))
		//				job.Facility = facility.Value;
		//			else job.Facility = Facility.FG;
		//		else job.Facility = null;
		//	}
		//}

		public void BaseSave(IOrder order)
		{
			base.Save<IOrder>(order);
		}

		public void Save(IOrder order)
		{
			//AssertPermission("order.update");

			var createScript = order.Id == 0;
			IList<IJob> newJobs = new List<IJob>();
			var orderDir = LepGlobal.Instance.ArtworkDirectory(order);

			Facility? facilityFromDeliveryPostCode = GetProductionFacilityByPostCode(order.DeliveryAddress.Postcode);

			Facility? facilityFromCustomerSelectedPickup = null;

			if (order.Courier.IsPickupFG && order.Jobs.All(j => jobApp.IsFacilityAvailable(FG, j)))
			{
				facilityFromCustomerSelectedPickup = FG;
			}
			else if (order.Courier.IsPickupPM && order.Jobs.All(j => jobApp.IsFacilityAvailable(PM, j)))
			{
				facilityFromCustomerSelectedPickup = PM;
			}

			var forceFreightCheck = false;

			if (order.IsQuote)
			{
				order.Promotion = null;
				order.PromotionJobPriceBenefit = 0;
				base.Save(order);
			}

			foreach (var j in order.Jobs)
			{
				if (order.Status == OrderStatusOptions.Open || order.Status == OrderStatusOptions.Submitted)
				{
					//The default is Forest Glen (1), until set otherwise.
					//Based on the following level of criteria.
					//Level 1 - Print Type
					//Level 2 - Job Type
					//Level 3 - Stock Type
					//Level 4 - PostCode
					//Level 5 - Some custom rules.

					if (facilityFromCustomerSelectedPickup != null)
						j.Facility = facilityFromCustomerSelectedPickup;
					else
						if (facilityFromDeliveryPostCode.HasValue && j.Status < DPCPreProduction) ///PreflightDone
						if (!j.IsCustomFacility)
							if (jobApp.IsFacilityAvailable(facilityFromDeliveryPostCode.Value, j))
								j.Facility = facilityFromDeliveryPostCode.Value;
							else
								j.Facility = FG;
				}
				else if (j.HasFacilityChange)
				{
					forceFreightCheck = true;
				}

				//if (!order.DeliveryAddress.Equals(order.Customer.PostalAddress))
				//	j.ActualPackaging = j.GetActualPackaging();
				if (j.Id == 0)
				{
					newJobs.Add(j);
				}
				else
				{
					try
					{
						if (orderDir.Exists)
						{
							//if old job..check if quantity made the filename change
							var jobDir = LepGlobal.Instance.ArtworkDirectory(j, false);
							if (!jobDir.Exists)
							{
								var oldDir = orderDir.GetDirectories(j.JobNr + "-*");
								if (oldDir.Length > 0)
								{
									oldDir[0].MoveTo(jobDir.FullName);
									newJobs.Add(j);
								}
							}
						}
					}
					catch (Exception ex)
					{
						Log.Error(ex.Message);
					}
				}
			}
			//order.SetExtraStatus();
			base.Save(order);

			if (order.Customer.PaymentTerms == Account)
			{
				forceFreightCheck = true;
			}

			// LORD-1269 allow promotion to  be calculated even at ready stage
			if (order.Status == OrderStatusOptions.Open || order.Status == OrderStatusOptions.Submitted || order.Status == OrderStatusOptions.Ready)
			{
				promotionApp.CalculatePromotion(order, new StringBuilder());
			}

			if (order.Status == OrderStatusOptions.Open || order.Status == OrderStatusOptions.Submitted)
			{
				if (!order.PackDetail.IsCustomPackages)
				{
					foreach (var j in order.Jobs)
						if (!j.Freight.IsCustom)
							freightApp.SetFreight(j);

					freightApp.SetFreight(order);
				}

				if (order.PaymentStatus != Paid)
				{
					//freightApp.SetFreightPrice(order);
				}

				
				//base.Save(order);
			}
			else if (forceFreightCheck)
			{
				if (order.Jobs.All(j => (int)j.Status < (int)Packed))
				{
					freightApp.SetFreight(order);
					order.SetDispatchLabels();
				}
			}

			if (createScript) CreateOrderScript(order);
			foreach (var newJ in newJobs) jobApp.CreateArtworkScript(newJ);
			base.Save(order);
		}

		private void CopyStream(Stream input, Stream output)
		{
			var bin = new BufferedStream(input, 20480);
			var bout = new BufferedStream(output, 20480);
			try
			{
				while (true)
				{
					var buffer = new byte[1024];
					var count = bin.Read(buffer, 0, 1024);
					if (count == 0) break;
					bout.Write(buffer, 0, count);
				}
			}
			finally
			{
				bout.Close();
			}
		}

		private void CreateOrderScript(IOrder order)
		{
			try
			{
				if (LepArtworkScriptFiles.OrderMacScript == null || !LepArtworkScriptFiles.OrderMacScript.Exists || LepArtworkScriptFiles.OrderIcon == null || !LepArtworkScriptFiles.OrderIcon.Exists || LepArtworkScriptFiles.OrderWinScript == null || !LepArtworkScriptFiles.OrderWinScript.Exists)
				{
					Log.Error("CreateOrderScript");
					return;
				}

				var dir = LepGlobal.Instance.ArtworkDirectory(order);
				if (!dir.Exists) dir.Create();

				var extraFilesDir = LepGlobal.Instance.GetOrderExtraFilesFolder(order);
			 

				StreamReader scriptReader = null;
				string script = null;
				FileInfo f = null;
				var url = LepGlobal.Instance.AbsolutePathURL + "/setorderstatus.aspx?id=" + order.Id;

				/*
				if (false)//LegacyMacWorkflow
				{
					//scriptReader = LepArtworkScriptFiles.OrderMacScript.OpenText();
					//script = scriptReader.ReadToEnd();
					//scriptReader.Close();

					//f = new FileInfo(string.Format("{0}/done.wflow", dir.FullName));
					//if (!f.Exists)
					//{
					//	var scriptdata = script.Replace("[[OrderNumber]]", order.OrderNr);
					//	scriptdata = scriptdata.Replace("[[OrderDate]]", string.Format("{0:yyyyMMdd}", order.DateCreated));
					//	scriptdata = scriptdata.Replace("[[URL]]", url);
					//	scriptdata = scriptdata.Replace("[[ScriptFileName]]", "done.wflow");
					//	var writer = f.CreateText();
					//	writer.Write(scriptdata);
					//	writer.Close();
					//}
					//LepArtworkScriptFiles.OrderIcon.CopyTo(string.Format("{0}/._done.wflow", dir.FullName, true));
				}
				else
				{
					var filename = string.Format("{0}/done.webloc", dir.FullName);
					CreateMacScript(string.Format("lep:orderdone:{0}", order.Id)).SaveWithOutBOM(filename);
					new FileInfo(filename).CopyMacIconFrom(LepArtworkScriptFiles.OrderIcon);
				}
				*/

				scriptReader = LepArtworkScriptFiles.OrderWinScript.OpenText();
				script = scriptReader.ReadToEnd();
				scriptReader.Close();
				f = new FileInfo(string.Format("{0}/._done.vbs", dir.FullName));
				if (!f.Exists)
				{
					var scriptdata = script.Replace("[[URL]]", url);
					scriptdata = scriptdata.Replace("[[ScriptFileName]]", "._done.vbs");
					var writer = f.CreateText();
					writer.Write(scriptdata);
					writer.Close();
				}
				//try
				//{
				//	File.Copy(Path.Combine(LepGlobal.Instance.DataDirectory.FullName, "copyFileOutOfJobsFolder.ps1"),
				//			  Path.Combine(dir.FullName, "copyFileOutOfJobsFolder.ps1"));
				//} catch (Exception ex)
				//{
				//	Log.Error(Path.Combine(dir.FullName, "copyFileOutOfJobsFolder.ps1"));
				//}
			}
			catch (Exception ex)
			{
				Log.Error(ex.Message);
			}
		}

		private XDocument CreateMacScript(string url)
		{
			var doc =
				XDocument.Parse(
					"<?xml version=\"1.0\" encoding=\"UTF-8\"?><!DOCTYPE plist PUBLIC \"-//Apple//DTD PLIST 1.0//EN\" \"http://www.apple.com/DTDs/PropertyList-1.0.dtd\"><plist version=\"1.0\"><dict><key>URL</key><string/></dict></plist>");
			var el = doc.XPathSelectElement("/plist/dict/string");
			el.Value = url;

			return doc;
		}

		public string GenerateOrderFolderScript(IOrder order)
		{
			if (LepArtworkScriptFiles.MountScriptTemplate != null && LepArtworkScriptFiles.MountScriptTemplate.Exists)
			{
				var reader = LepArtworkScriptFiles.MountScriptTemplate.OpenText();
				var templateString = reader.ReadToEnd();
				reader.Close();
				templateString = templateString.Replace("[[Order Dir]]",
					string.Format("{0:yyyyMMdd}/{1}", order.DateCreated, order.OrderNr));
				return templateString;
			}
			return "";
		}

		public void Delete(IOrder order)
		{
			//AssertPermission("order.delete");
			base.Delete(order);
		}

		public void Forget(IOrder order)
		{
			Evict(order);
		}

		public ICriteria OrderCriteria()
		{
			var criteria = Session.CreateCriteria(typeof(IOrder), "o");
			return criteria;
		}

		public List<IJobTemplate> JobTemplatesGetAllPossibleValues(JobTypeOptions? jobTypeOptions)
		{
			var types = new List<IJobTemplate>();
			if (jobTypeOptions != null)
				switch ((JobTypeOptions)Convert.ToInt32(jobTypeOptions))
				{
					case Letterhead:
						types.Add(jobApp.GetJobTemplate(Letterhead));
						types.Add(jobApp.GetJobTemplate(Compliments));
						break;

					case DL:
						types.Add(jobApp.GetJobTemplate(DL));
						types.Add(jobApp.GetJobTemplate(DLSpecial));
						break;

					case Brochure:
						types.Add(jobApp.GetJobTemplate(Brochure));
						types.Add(jobApp.GetJobTemplate(BrochureSpecial));
						break;

					case Magazine:
						types.Add(jobApp.GetJobTemplate(Magazine));
						types.Add(jobApp.GetJobTemplate(MagazineSeparate));
						break;

					case BusinessCard:
						types.Add(jobApp.GetJobTemplate(BusinessCard));
						types.Add(jobApp.GetJobTemplate(Postcard));
						break;

					case BusinessCardNdd:
						types.Add(jobApp.GetJobTemplate(BusinessCardNdd));
						break;

					case BusinessCardSdd:
						types.Add(jobApp.GetJobTemplate(BusinessCardSdd));
						break;

					default:
						types.Add(jobApp.GetJobTemplate((int)jobTypeOptions));
						break;
				}
			return types;
		}

		public int GetOrdersCountByCustomer(ICustomerUser customer)
		{
			var count = (int)Session.CreateCriteria(typeof(IOrder), "o")
				.Add(Eq("Customer", customer))
				.Add(Not(Eq("o.Status", OrderStatusOptions.Open)))
				.SetProjection(Projections.Count("Id")).UniqueResult();
			return count;
		}

		public IList<IOrder> FindArchiveOrder(int day)
		{
			return Session.CreateCriteria(typeof(IOrder))
				//	MikeGreenan - Regardless of status(apart for already archived orders), archive orders where datecreated is greater than archive days.
				.Add(Eq("Status", OrderStatusOptions.Dispatched))
				.Add(Lt("DispatchDate", DateTime.Now.AddDays(-day)))
				.Add(Gt("DispatchDate", DateTime.Now.AddYears(-day)))
				//.Add(!Restrictions.Eq("Status", OrderStatusOptions.Archived))
				//.Add(Restrictions.Lt("DateCreated", DateTime.Now.AddDays(-day)))
				.List<IOrder>();
		}

		public IList<IOrder> FindRecentOrders(int days)
		{
			return Session.CreateCriteria(typeof(IOrder))
				.Add(Lt("DispatchDate", DateTime.Now.AddDays(-days)))
				.List<IOrder>();
		}

		//public void MoveOrderFile(IOrder order)
		//{
		//	var fromdir = _configApp.ArtworkDirectory(order);

		//	if (fromdir.Exists)
		//	{
		//		var todir = _configApp.OldArtworkDirectory(order);
		//		if (!todir.Exists)
		//		{
		//			if (!todir.Parent.Exists) todir.Parent.Create();
		//			fromdir.MoveTo(todir.FullName);
		//		}
		//	}
		//}

		private IOrder GetNewReprintOrder(IJob srcJob)
		{
			var srcOrder = srcJob.Order;

			var order = NewOrder(srcOrder.Customer);
			order.PaymentStatus = srcOrder.PaymentStatus;
			order.DeliveryInstructions = srcOrder.DeliveryInstructions;
			order.Contact = srcOrder.Contact;
			order.Courier = srcOrder.Courier;
			order.RecipientName = srcOrder.RecipientName;
			order.RecipientPhone = srcOrder.RecipientPhone;
			order.PurchaseOrder = srcOrder.PurchaseOrder;
			order.DeliveryAddress = srcOrder.DeliveryAddress;
			order.Status = OrderStatusOptions.Open;
			return order;
		}

		private void CopyJobProperties(IJob src, IJob dest)
		{
			dest.Status = JobStatusOptions.Open;

			dest.CustomSlot = src.CustomSlot;
			dest.Stock = src.Stock;
			dest.StockOverride = src.StockOverride;
			dest.StockForCoverOverride = src.StockForCoverOverride;

			dest.FrontPrinting = src.FrontPrinting == JobPrintOptions.BW? JobPrintOptions.Printed: src.FrontPrinting;
			dest.BackPrinting = src.BackPrinting == JobPrintOptions.BW ? JobPrintOptions.Printed : src.BackPrinting;

			dest.FrontCelloglaze = src.FrontCelloglaze;
			dest.BackCelloglaze = src.BackCelloglaze;

			dest.FrontCelloglazeOverride = src.FrontCelloglazeOverride;
			dest.BackCelloglazeOverride = src.BackCelloglazeOverride;

			dest.FoilColour = src.FoilColour;
			dest.Envelope = src.Envelope;
			dest.EnvelopeType = src.EnvelopeType;
			dest.Rotation = src.Rotation;
			dest.BoundEdge = src.BoundEdge;
			dest.FinishedSize = src.FinishedSize;


			// only for old 'Double Business Card (184 x 54)'
			// covert them to 'Double Business Card (180 x 54)'
			if (dest.FinishedSize.PaperSize.Id == 20)
			{
				if (dest.FinishedSize.Width  == 184) dest.FinishedSize.Width = 180;
				if (dest.FinishedSize.Height == 184) dest.FinishedSize.Height = 180;
			}


			dest.FoldedSize = src.FoldedSize;
				
			// LORD-1215
			if (dest.Template.Is(JobTypeOptions.BrochureSDD))
			{
				dest.FoldedSize = null;
			}
			dest.RoundOption = src.RoundOption;
			dest.RoundDetailOption = src.RoundDetailOption;
			dest.CustomDieCut = src.CustomDieCut;
			dest.BindingOption = src.BindingOption;
			dest.TRround = src.TRround;
			dest.TLround = src.TLround;
			dest.BRround = src.BRround;
			dest.BLround = src.BLround;
			dest.Perforating = src.Perforating;
			dest.PerforatingInstructions = src.PerforatingInstructions;
			dest.Scoring = src.Scoring;
			dest.ScoringInstructions = src.PerforatingInstructions;
			dest.HoleDrilling = src.HoleDrilling;
			dest.PadDirection = src.PadDirection;
			dest.NumberOfMagnets = src.NumberOfMagnets;
			
			dest.Pages = src.Pages;
			dest.SelfCovered = src.SelfCovered;
			dest.StockForCover = src.StockForCover;
			//dest.StockForCoverOverride = src.StockForCoverOverride;
			dest.Urgent = src.Urgent;
			dest.DieCutType = src.DieCutType;
			dest.Status = JobStatusOptions.Open;
			//dest.MYOB = src.MYOB;
			dest.PrintType = src.PrintType;
			dest.Facility = src.Facility;
			dest.SendSamples = src.SendSamples;

			dest.NumberOfHoles = src.NumberOfHoles;
			dest.BrochureDistPackInfo = new BrochureDistPackInfo(src.BrochureDistPackInfo);

			dest.InvolvesOutwork = src.InvolvesOutwork;

			//dest.QuoteEstimator = src.QuoteEstimator;
			//dest.QuoteCOGS = src.QuoteCOGS;
			//dest.QuoteOutworkCost = src.QuoteOutworkCost;
			//dest.QuoteComments = src.QuoteComments;
			//dest.QuotePrimary = src.QuotePrimary;

			dest.Splits = src.Splits;
			
			if (src.CustomerRequestedPrice.HasValue)
				dest.CustomerRequestedPrice = src.CustomerRequestedPrice;

			ChangeStockTo420GSM(src, dest);
		}

		//cr21
		public IOrder ReprintJob(IUser user, IJob originalJob, decimal invoicePrice, decimal reprintCost, bool copyPreflight,
			bool hasDispatch, string reason, string result, string ncrNo, string predefinedReason, int quantity)
		{
			var newReprintOrder = GetNewReprintOrder(originalJob);

			string newJobName = string.Format("Reprint of (Job# {0})", originalJob.JobNr);
			var newReprintJob = newReprintOrder.NewJob(newJobName, quantity, originalJob.TrackProgress, originalJob.SpecialInstructions, originalJob.Template, CurrentUser);

			//cr21
			CopyJobProperties(originalJob, newReprintJob);

			//cr21 point 2. chargable amount from UI labled Invoice price, chargable amount, shows up in job sheet, allow 0
			newReprintJob.Price = invoicePrice.ToString();
			//cr21 point 3. this one from UI labled Reprint Cost, shows up on repprint report only
			newReprintJob.ReprintCost = reprintCost.ToString();
			//cr21 point 4. from UI box labeled “NCR No:” for LEP user to enter number
			newReprintJob.NCRNo = ncrNo;

			//SA
			newReprintJob.PrintType = originalJob.PrintType;
			newReprintJob.IsReprint = true;
			newReprintJob.ReprintReason = predefinedReason + " " + reason;
			newReprintJob.ReprintReasonPredefined = predefinedReason;
			newReprintJob.ReprintResult = result;
			newReprintJob.IsReprintJobDispatch = hasDispatch;
			newReprintJob.ReprintBy = CurrentUser;

			try
			{
				newReprintJob.ReprintFromPreviousJobNo = originalJob.Id;
				newReprintJob.ReprintFromPreviousRunNo = originalJob.Runs.FirstOrDefault()?.Id ?? 0;
			} catch(Exception ex)
			{
				Log.Error("LORD-887");
			}

			//newReprintJob.RequiredByDate = originalJob.RequiredByDate;
			newReprintJob.RequiredByDate = null;

			//	newReprintJob.ReceivedDate = DateTime.Now;
			//newReprintJob.Status = PreflightDone;
			//newReprintOrder.Status = Ready;

			Save(newReprintOrder);
			Save(newReprintJob);

			jobApp.CopyArtworks(originalJob, newReprintJob);

			Save(newReprintOrder);
			Save(newReprintJob);



			if (copyPreflight) newReprintJob.SetStatus(PreflightDone, user);

			originalJob.AddComment(CurrentUser,
				string.Format("Job is being reprinted at no additional cost. See {0}, {1}", newReprintJob.Id, reason));

			newReprintJob.ProductionInstructions += newReprintJob.ProductionInstructions + $"\n{reason}";
			newReprintJob.SpecialInstructions += newReprintJob.SpecialInstructions + $"\n{result}";

			var commentStaff = $"{reason}";
			if (originalJob.Runs.Any() && originalJob.Runs[0].Id != 0)
			{
				commentStaff += $"\nPrevious Run number was {originalJob.Runs[0].Id}";
			}

			newReprintJob.AddComment(CurrentUser, commentStaff, true);

			var commentCustomer = $"{result}";
			newReprintJob.AddComment(CurrentUser, commentCustomer);

			Save(originalJob.Order);
			Save(newReprintOrder);
			emailApp.SendNotification(newReprintJob, ContentType.Reprint);
			return newReprintOrder;
		}





		public IOrder ReprintOrder(IUser user, IOrder srcOrder, bool copyPreflight,
			bool hasDispatch, string reason, string result, string ncrNo, string predefinedReason )
		{
			var order = NewOrder(srcOrder.Customer);
			order.PaymentStatus = srcOrder.PaymentStatus;
			order.DeliveryInstructions = srcOrder.DeliveryInstructions;
			order.Contact = srcOrder.Contact;
			order.Courier = srcOrder.Courier;
			order.RecipientName = srcOrder.RecipientName;
			order.RecipientPhone = srcOrder.RecipientPhone;
			order.PurchaseOrder = srcOrder.PurchaseOrder;
			order.DeliveryAddress = srcOrder.DeliveryAddress;
			order.Status = OrderStatusOptions.Open;



			foreach (IJob originalJob in srcOrder.Jobs)
			{
				string newJobName = string.Format("Reprint of (Job# {0})", originalJob.JobNr);
				var newReprintJob = order.NewJob(newJobName, originalJob.Quantity, originalJob.TrackProgress, originalJob.SpecialInstructions, originalJob.Template, CurrentUser);

				CopyJobProperties(originalJob, newReprintJob);

				//cr21 point 2. chargable amount from UI labled Invoice price, chargable amount, shows up in job sheet, allow 0
				newReprintJob.Price = (0).ToString();
				//cr21 point 3. this one from UI labled Reprint Cost, shows up on repprint report only
				newReprintJob.ReprintCost = (originalJob.Price).ToString();
				//cr21 point 4. from UI box labeled “NCR No:” for LEP user to enter number
				newReprintJob.NCRNo = ncrNo;

				//SA
				newReprintJob.PrintType = originalJob.PrintType;
				newReprintJob.IsReprint = true;
				newReprintJob.ReprintReason = predefinedReason + " " + reason;
				newReprintJob.ReprintReasonPredefined = predefinedReason;
				newReprintJob.ReprintResult = result;
				newReprintJob.IsReprintJobDispatch = hasDispatch;
				newReprintJob.ReprintBy = CurrentUser;

				try
				{
					newReprintJob.ReprintFromPreviousJobNo = originalJob.Id;
					newReprintJob.ReprintFromPreviousRunNo = originalJob.Runs.FirstOrDefault()?.Id ?? 0;
				}
				catch (Exception ex)
				{
					Log.Error("LORD-887");
				}

				//newReprintJob.RequiredByDate = originalJob.RequiredByDate;
				newReprintJob.RequiredByDate = null;

				//	newReprintJob.ReceivedDate = DateTime.Now;
				//newReprintJob.Status = PreflightDone;
				//newReprintOrder.Status = Ready;

			
				//Save(newReprintJob);
				Save(order);

				jobApp.CopyArtworks(originalJob, newReprintJob);
				Save(newReprintJob);

				if (copyPreflight) newReprintJob.SetStatus(PreflightDone, user);

				originalJob.AddComment(CurrentUser,
					string.Format("Job is being reprinted at no additional cost. See {0}, {1}", newReprintJob.Id, reason));

				newReprintJob.ProductionInstructions += newReprintJob.ProductionInstructions + $"\n{reason}";
				newReprintJob.SpecialInstructions += newReprintJob.SpecialInstructions + $"\n{result}";

				var commentStaff = $"{reason}";
				if (originalJob.Runs.Any() && originalJob.Runs[0].Id != 0)
				{
					commentStaff += $"\nPrevious Run number was {originalJob.Runs[0].Id}";
				}

				newReprintJob.AddComment(CurrentUser, commentStaff, true);

				var commentCustomer = $"{result}";
				newReprintJob.AddComment(CurrentUser, commentCustomer);

				Save(originalJob.Order);
				Save(order);
				//emailApp.SendNotification(newReprintJob, ContentType.Reprint);
			}

			return order;
		}







		public IOrder ReprintOrder(IUser currentUser, IOrder srcOrder, OrderReprintRestartDto reprintRestartArgs)
		{
			var order = NewOrder(srcOrder.Customer);
			order.PaymentStatus = srcOrder.PaymentStatus;
			order.DeliveryInstructions = srcOrder.DeliveryInstructions;
			order.Contact = srcOrder.Contact;
			order.Courier = srcOrder.Courier;
			order.RecipientName = srcOrder.RecipientName;
			order.RecipientPhone = srcOrder.RecipientPhone;
			order.PurchaseOrder = srcOrder.PurchaseOrder;
			order.DeliveryAddress = srcOrder.DeliveryAddress;
			order.Status = OrderStatusOptions.Open;

			foreach (var x in reprintRestartArgs.Jobs)
			{
				IJob originalJob = srcOrder.Jobs.Where( _ => _.Id == x.Id).First();

				string newJobName = string.Format("Reprint of (Job# {0})", originalJob.JobNr);
				var newReprintJob = order.NewJob(newJobName, originalJob.Quantity, originalJob.TrackProgress, originalJob.SpecialInstructions, originalJob.Template, CurrentUser);

				CopyJobProperties(originalJob, newReprintJob);

				//cr21 point 2. chargable amount from UI labled Invoice price, chargable amount, shows up in job sheet, allow 0
				newReprintJob.Price = (0).ToString();
				//cr21 point 3. this one from UI labled Reprint Cost, shows up on repprint report only
				newReprintJob.ReprintCost = (originalJob.Price).ToString();
				//cr21 point 4. from UI box labeled “NCR No:” for LEP user to enter number
				newReprintJob.NCRNo = x.NcrNo;

				//SA
				newReprintJob.PrintType = originalJob.PrintType;
				newReprintJob.IsReprint = true;
				newReprintJob.ReprintReason = x.PredefinedReason + " " + x.Reason;
				newReprintJob.ReprintReasonPredefined = x.PredefinedReason;
				newReprintJob.ReprintResult = x.Result;
				newReprintJob.IsReprintJobDispatch = x.HasDispatch;
				newReprintJob.ReprintBy = CurrentUser;

				try
				{
					newReprintJob.ReprintFromPreviousJobNo = originalJob.Id;
					newReprintJob.ReprintFromPreviousRunNo = originalJob.Runs.FirstOrDefault()?.Id ?? 0;
				}
				catch (Exception ex)
				{
					Log.Error("LORD-1087/1170 Reprint whole order");
				}

				//newReprintJob.RequiredByDate = originalJob.RequiredByDate;
				newReprintJob.RequiredByDate = null;

				//newReprintJob.ReceivedDate = DateTime.Now;
				//newReprintJob.Status = PreflightDone;
				//newReprintOrder.Status = Ready;


				//Save(newReprintJob);
				Save(order);

				jobApp.CopyArtworks(originalJob, newReprintJob);
				Save(newReprintJob);

				if (x.CopyPreflight) newReprintJob.SetStatus(PreflightDone, currentUser);

				originalJob.AddComment(CurrentUser,
					string.Format("Job is being reprinted at no additional cost. See {0}, {1}", newReprintJob.Id, x.Reason));

				newReprintJob.ProductionInstructions += newReprintJob.ProductionInstructions + $"\n{x.Reason}";
				newReprintJob.SpecialInstructions += newReprintJob.SpecialInstructions + $"\n{x.Result}";

				var commentStaff = $"{x.Reason}";
				if (originalJob.Runs.Any() && originalJob.Runs[0].Id != 0)
				{
					commentStaff += $"\nPrevious Run number was {originalJob.Runs[0].Id}";
				}

				newReprintJob.AddComment(CurrentUser, commentStaff, true);

				var commentCustomer = $"{x.Result}";
				newReprintJob.AddComment(CurrentUser, commentCustomer);

				Save(originalJob.Order);
				Save(order);
				//emailApp.SendNotification(newReprintJob, ContentType.Reprint);
			}

			return order;
		}




		//cr21
		public IOrder RestartJob(IJob job, decimal invoicePrice, decimal reprintCost, bool copyPreflight,
			bool hasDispatch, string reason, string result, string ncrNo, string predefinedReason, int quantity)
		{
			//cr21 point 2. chargable amount from UI labled Invoice price, chargable amount, shows up in job sheet, allow 0
			//job.Price = invoicePrice.ToString();
			//cr21 point 3. this one from UI labled Reprint Cost, shows up on repprint report only
			//job.ReprintCost = reprintCost.ToString();
			//cr21 point 4. from UI box labeled “NCR No:” for LEP user to enter number
			job.NCRNo = ncrNo;

			job.IsReprint = true;
			job.ReprintReason = predefinedReason + " " + reason;
			job.ReprintReasonPredefined = predefinedReason;
			job.ReprintResult = result;
			job.IsReprintJobDispatch = false;
			job.ReprintBy = CurrentUser;

			//LORD-851
			//if (quantity != 0)
			//{
			//	job.Quantity = quantity;
			//}

			var previousRunId = 0;

			if (job.Runs.Any())
			{
				previousRunId = job.Runs[0].Id;
				foreach (var r in job.Runs) if (r.Jobs.Contains(job)) r.Jobs.Remove(job);
				job.Runs.Clear();
			}

			job.ReprintFromPreviousJobNo = job.Id;
			job.ReprintFromPreviousRunNo = previousRunId;

			//job.Enable = true;
			if (copyPreflight)
			{
				job.Status = PreflightDone;
				job.Order.Status = Ready;
			}
			else
			{
				job.Status = JobStatusOptions.Submitted;
				job.Order.Status = OrderStatusOptions.Submitted;
				jobApp.CreateArtworkScript(job);
			}

			var commentStaff = $"{reason}";
			if (previousRunId != 0)
			{
				commentStaff += $"\nPrevious Run number was {previousRunId}";
			}

			if (job.Stock.Id == 18)
			{
				job.Stock = new Stock() { Id = 91 };
				job.AddComment(SecurityApplication.GetSystemUser(), "'400 GSM Deluxe Artboard' is replaced by '420 GSM Deluxe Artboard'");
			}

			job.AddComment(CurrentUser, commentStaff, true);

			var commentCustomer = $"{result}";
			job.AddComment(CurrentUser, commentCustomer);

			job.ProductionInstructions += job.ProductionInstructions + $"\n{reason}";
			job.SpecialInstructions += job.SpecialInstructions + $"\n{result}";

			job.ProductionInstructions = job.ProductionInstructions.TrimNewLines();
			job.SpecialInstructions = job.SpecialInstructions.TrimNewLines();

			emailApp.SendNotification(job, ContentType.Restart);
			base.Save(job.Order);
			return job.Order;
		}

		/*
		copy all previous Job Specs including delivery address info.
		Bring in pre-flighted artwork from previous order.
		Reprice jobs. If system unable to price for whatever reason, then to go quote required.
		Job specs to be locked i.e. not editable, other than stock and qty.
		 */

		public IOrder ReorderJob(IJob originalJob)
		{
			//var copyPreflight = true;
			//bool copyPreflight = true;

			var newOrder = NewOrder(originalJob.Order.Customer);
			//newOrder.Courier = originalJob.Order.Courier;
			newOrder.PaymentStatus = AwaitingPayment;

			string name = $"Reorder of (Job# {originalJob.JobNr})";
			var newJob = newOrder.NewJob(name, originalJob.Quantity,
				originalJob.TrackProgress, originalJob.SpecialInstructions, originalJob.Template, base.CurrentUser);
			CopyJobProperties(originalJob, newJob);
			newJob.ReprintBy = CurrentUser;
			newJob.ReOrderSourceJobId = originalJob.Id;
			newJob.AddComment(CurrentUser, name);
			Save(newOrder);

			jobApp.CopyArtworks(originalJob, newJob);

			//originalJob.AddComment( CurrentUser, String.Format( "Job is being reprinted at no additional cost. See {0}, {1}", configApp.JobLink( newReprintJob ), reason ) );
			//newReprintJob.AddComment( CurrentUser, String.Format( "Job reprinted at no additional cost from {0}, {1}", configApp.JobLink( originalJob ), reason ) );

			var modQty = newJob.Quantity;

			//:TODO MikeGreenan Customer ProductPriceCode stuff
			//if current user is Customer then use customer.ProductPriceCode
			//else if Staff use job.Order.Customer.ProductPriceCode
			var productpriceCode = "";
			if (CurrentUser is ICustomerUser) productpriceCode = ((ICustomerUser)CurrentUser).ProductPriceCode;
			else productpriceCode = newOrder.Customer.ProductPriceCode;

			var oldPrice = originalJob.Price;

			var price = priceEngine.PriceJob(newJob, new StringBuilder(), out modQty, productpriceCode);
			newJob.SetPrice(CurrentUser, price, 0, false);

			if (oldPrice != price.ToString())
			{
				newJob.AddComment(CurrentUser, $"Old Price {oldPrice}, New Price {price}");
			}

			//newJob.Price = price.ToString();
			Save(newOrder);
			return newOrder;
		}

		/*
		Just after submit notification, see if it’s a reorder and then move to Orderstatus ready and job status preflight done
		create new order from existing, with artwork copy.
		Reprice
		 */

		public IOrder ReorderOrder(IOrder sourceOrder)
		{
			var newOrder = NewOrder(sourceOrder.Customer);
			newOrder.DeliveryInstructions = sourceOrder.DeliveryInstructions;
			newOrder.PaymentStatus = sourceOrder.PaymentStatus;
			newOrder.Contact = sourceOrder.Contact;
			//newOrder.Courier = sourceOrder.Courier;
			newOrder.RecipientName = sourceOrder.RecipientName;
			newOrder.RecipientPhone = sourceOrder.RecipientPhone;
			newOrder.PurchaseOrder = sourceOrder.PurchaseOrder;
			newOrder.DeliveryAddress = sourceOrder.DeliveryAddress;
			newOrder.Status = OrderStatusOptions.Open;

			foreach (var originalJob in sourceOrder.Jobs)
			{
				string name = $"Reorder of (Job# {originalJob.JobNr})";
				var newJob = newOrder.NewJob(name, originalJob.Quantity, originalJob.TrackProgress, originalJob.SpecialInstructions, originalJob.Template, CurrentUser);
				newJob.ReprintFromPreviousJobNo = 0;
				newJob.ReOrderSourceJobId = originalJob.Id;

				CopyJobProperties(originalJob, newJob);

				//newJob.Price = originalJob.Price;
				//newJob.IsQuotePrice = originalJob.IsQuotePrice;
				//newJob.PriceDate = originalJob.PriceDate;

				//if (!newJob.IsQuotePrice) {
				var productpriceCode = "";
				if (CurrentUser is ICustomerUser) productpriceCode = ((ICustomerUser)CurrentUser).ProductPriceCode;
				else productpriceCode = newOrder.Customer.ProductPriceCode;

				var oldPrice = originalJob.Price;
				var modQty = newJob.Quantity;
				var price = priceEngine.PriceJob(newJob, new StringBuilder(), out modQty, productpriceCode);
				newJob.SetPrice(CurrentUser, price, 0, false);
				newJob.SupplyArtworkApproval = JobApprovalOptions.NotNeeded;
				newJob.QuoteNeedApprove = originalJob.QuoteNeedApprove;
				newJob.ReadyArtworkApproval = JobApprovalOptions.NotNeeded;
			}
			Save(newOrder);

			foreach (var originalJob in sourceOrder.Jobs)
			{
				IJob newJob = newOrder.Jobs.Where(_ => _.ReOrderSourceJobId == originalJob.Id).First();
				jobApp.CopyArtworks(originalJob, newJob);
			}
			Save(newOrder);
			return newOrder;
		}

		public IOrder CopyOrder2(IOrder sourceOrder)
		{
			var newOrder = NewOrder(sourceOrder.Customer);
			newOrder.DeliveryInstructions = sourceOrder.DeliveryInstructions;
			newOrder.PaymentStatus = AwaitingPayment;
			newOrder.Contact = sourceOrder.Contact;
			//newOrder.Courier = sourceOrder.Courier;
			newOrder.RecipientName = sourceOrder.RecipientName;
			newOrder.RecipientPhone = sourceOrder.RecipientPhone;
			newOrder.PurchaseOrder = sourceOrder.PurchaseOrder;
			newOrder.DeliveryAddress = sourceOrder.DeliveryAddress;
			newOrder.Status = OrderStatusOptions.Open;

			foreach (var j in sourceOrder.Jobs)
			{
				var newJob = newOrder.NewJob(j.Name, j.Quantity, j.TrackProgress, j.SpecialInstructions, j.Template, CurrentUser);
				CopyJobProperties(j, newJob);
				var msg = $"Copied from Order {sourceOrder.Id}, Job {j.Id}";
				newJob.AddComment(CurrentUser, msg);
				newJob.Price = j.Price;
				//newJob.IsQuotePrice = j.IsQuotePrice;
				newJob.PriceDate = j.PriceDate;

				//if (!newJob.IsQuotePrice) { LORD-582
				var productpriceCode = "";
				if (CurrentUser is ICustomerUser) productpriceCode = ((ICustomerUser)CurrentUser).ProductPriceCode;
				else productpriceCode = newOrder.Customer.ProductPriceCode;

				var oldPrice = j.Price;
				var modQty = newJob.Quantity;
				var price = priceEngine.PriceJob(newJob, new StringBuilder(), out modQty, productpriceCode);
				newJob.SetPrice(CurrentUser, price, 0, false);

				if (oldPrice != price.ToString())
				{
					newJob.AddComment(CurrentUser, $"Old Price {oldPrice}, New Price {price}");
				}
				//}

				//newJob.QuoteNeedApprove = j.QuoteNeedApprove;
				Save(newOrder);
			}

			return newOrder;
		}

		public void ChangeStockTo420GSM(IJob src, IJob dest)
		{
			var sId = src.Stock.Id;
			if (sId == 18)
			{
				dest.Stock = new Stock() { Id = 91 };
				dest.AddComment(SecurityApplication.GetSystemUser(), "'400 GSM Deluxe Artboard' is replaced by '420 GSM Deluxe Artboard'");
			}
			if (sId == 13)
			{
				dest.Stock = new Stock() { Id = 92 };
				dest.AddComment(SecurityApplication.GetSystemUser(), "'95 GSM Gloss Art' is replaced by '100 GSM Gloss Art'");
			}


			if (sId == 19)
			{
				dest.Stock = new Stock() { Id = 95 };
				dest.AddComment(SecurityApplication.GetSystemUser(), "'240 GSM Incada Silk' is replaced by 'Supreme 250 GSM Systems Board'");
			}


			if (sId == 95) //Supreme 250 GSM Systems Board
			{
				//97, '255 GSM Barry Bleach Board',
				dest.Stock = new Stock() { Id = 97 };
				dest.AddComment(SecurityApplication.GetSystemUser(), "'Supreme 250 GSM Systems Board' is replaced by '255 GSM Barry Bleach Board'");
			}

			//For Presentation folders change 420 GSM to 360GSM - lord1335
			if(src.IsPresentationFolder() && sId == 91 /*420 GSM Deluxe Artboard*/)
			{
				dest.Stock = new Stock() { Id = 93 };
				dest.AddComment(SecurityApplication.GetSystemUser(), "'420 GSM Deluxe Artboard' is replaced by '360 GSM Deluxe Artboard'");
			}


			
			//if (src.Stock.Id == 52)
			//{
			//	dest.Stock = new Stock() { Id = 96 };
			//	dest.AddComment(SecurityApplication.GetSystemUser(), "'140 GSM Uncoated' is replaced by '135 GSM Uncoated'");
			//}

		}

		public IOrder CopyOrder2(IOrder sourceOrder, IJob j)
		{
			var newOrder = NewOrder(sourceOrder.Customer);
			newOrder.DeliveryInstructions = sourceOrder.DeliveryInstructions;
			newOrder.PaymentStatus = AwaitingPayment;
			newOrder.Contact = sourceOrder.Contact;
			//newOrder.Courier = sourceOrder.Courier;
			newOrder.RecipientName = sourceOrder.RecipientName;
			newOrder.RecipientPhone = sourceOrder.RecipientPhone;
			newOrder.PurchaseOrder = sourceOrder.PurchaseOrder;
			newOrder.DeliveryAddress = sourceOrder.DeliveryAddress;
			newOrder.Status = OrderStatusOptions.Open;

			var newJob = newOrder.NewJob(j.Name, j.Quantity, j.TrackProgress, j.SpecialInstructions, j.Template, CurrentUser);
			CopyJobProperties(j, newJob);
			var msg = $"Copied from Order {sourceOrder.Id}, Job {j.Id}";
			newJob.AddComment(CurrentUser, msg);
			newJob.Price = j.Price;
			//newJob.IsQuotePrice = j.IsQuotePrice;
			newJob.PriceDate = j.PriceDate;

			//if (!newJob.IsQuotePrice) { LORD-582
			var productpriceCode = "";
			if (CurrentUser is ICustomerUser) productpriceCode = ((ICustomerUser)CurrentUser).ProductPriceCode;
			else productpriceCode = newOrder.Customer.ProductPriceCode;

			var oldPrice = j.Price;
			var modQty = newJob.Quantity;
			var price = priceEngine.PriceJob(newJob, new StringBuilder(), out modQty, productpriceCode);
			newJob.SetPrice(CurrentUser, price, 0, false);

			if (oldPrice != price.ToString())
			{
				newJob.AddComment(CurrentUser, $"Old Price {oldPrice}, New Price {price}");
			}
			//}
			//newJob.QuoteNeedApprove = j.QuoteNeedApprove;

			Save(newOrder);

			return newOrder;
		}

		public ICriteria ReprintCriteria()
		{
			var criteria = Session.CreateCriteria(typeof(IJob));
			criteria.CreateAlias("Order", "o");
			criteria.CreateAlias("o.Customer", "c");
			criteria.CreateAlias("ReprintBy", "u");
			criteria.Add(Eq("IsReprint", true));
			return criteria;
		}

		public string Render(OrderStatusOptions orderStatus)
		{
			bool isStaff = CurrentUser is IStaff;
			switch (orderStatus)
			{
				case OrderStatusOptions.Open:
					return isStaff ? "Not Submitted" : "Not Submitted";

				case OrderStatusOptions.Submitted:
					return isStaff ? "New Order" : "Submitted";

				case Ready:
					return isStaff ? "Ready" : "In Production";

				case OrderStatusOptions.Finished:
					return "Completed";

				case OrderStatusOptions.Dispatched:
					return "Dispatched";

				case Archived:
					return "Archived";

				default:
					return orderStatus.ToString();
			}
		}

		public string Render(IOrder order)
		{
			//TODO: MikeGreenan
			//if (!order.Enable) return "Cancelled";

			//foreach (var j in order.Jobs)
			//	if (j.HasReject) return "Rejected";
			//	else if (j.NeedApproval) return "Requires Approval";
			bool isStaff = CurrentUser is IStaff;
			switch (order.Status)
			{
				case OrderStatusOptions.Open:
					return "Not Submitted";

				case OrderStatusOptions.Submitted:
					return isStaff ? "New Order" : "Submitted";

				case Ready:
					return isStaff ? "Ready" : "In Production";

				case OrderStatusOptions.Finished:
					return "Completed";

				case OrderStatusOptions.Dispatched:
					return "Dispatched";

				case Archived:
					return "Archived";

				default:
					return order.Status.ToString();
			}
		}

		public virtual DateTime? GetDispatchDate(IOrder order, DateTime ifSubmittedAt, out bool estimated)
		{
			DateTime? maxDispatchEst = null; //Default DateTime is {01/01/0001 00:00:00}

			if (!order.Jobs.All(j => j.Facility.HasValue))
			{
				estimated = false;
				return maxDispatchEst;
			}

			estimated = true;

			if (!order.PriceOfJobs.HasValue ||
			  order.Jobs.Any(j => j.ArtworkStatus != ArtworkStatusOption.None
									|| j.ProofStatus == JobProofStatus.OnHold
									|| !j.Facility.HasValue
									|| !j.IsArtworkValidForSubmit()
									))
			{
				estimated = false;
				return maxDispatchEst;
			}

			foreach (var j in order.Jobs)
			{
				DateTime? DispatchEst = null;
				if (j.RequiredByDate == null)
				{
					var sql =
						@"Select dbo.GetDispatchEst3(:JobOptionID,:PrintType,:PaperSizeID,:StockID,:Qty,:Cello,:Folder,:LetterPress,:Magnets,:OptionalExtras, :Facility, :SubmittedDate)";

					#region call stored proc

					var cello = j.FinalFrontCelloglaze + "/" + j.FinalBackCelloglaze;

					cello = cello.Replace("AntiScuff", ""); // anti scuff doesnot affect dispatch date

					var folder = !j.IsMagazine() && j.FoldedSize != null ? "Y" : "N";
					var letterpress = "N";

					var NumberOptionalExtras = 0;


					if((j.DieCutType != CutOptions.None))
					{
						if (j.IsBusinessCard())
						{
							NumberOptionalExtras++;
						} 
						else
						{
							letterpress = (j.DieCutType > CutOptions.None) ? "Y" : "N";
						}
					}
					
					var magnet = j.NumberOfMagnets > 0 ? "Y" : "N";

					IQuery query = Session.CreateSQLQuery(sql);
					var jobOptionId = j.Template.Id;
				

					query.SetInt32("JobOptionID", jobOptionId);
					query.SetString("PrintType", j.PrintType.ToString());
					query.SetInt32("PaperSizeID", j.FinishedSize.PaperSize.Id);
					query.SetInt32("StockID", j.Stock.Id);
					query.SetInt32("Qty", j.Quantity);
					query.SetString("Cello", cello);
					query.SetString("Folder", folder);
					query.SetString("LetterPress", letterpress);
					query.SetString("Magnets", magnet);

					if (j.Perforating) NumberOptionalExtras += 1;
					if (j.Scoring) NumberOptionalExtras += 1;
					if (j.HoleDrilling != HoleDrilling.None) NumberOptionalExtras += 1;
					if (j.BindingOption != null && j.BindingOption.Name != "Saddle Stitch") NumberOptionalExtras += 1;
					if (j.RoundOption != RoundOption.None) NumberOptionalExtras += 1;

					
					if( j.FinalFrontCelloglaze == SpotUVFrontMattFront && j.FinalBackCelloglaze == None)
						NumberOptionalExtras += 8;

					if(j.FinalFrontCelloglaze == SpotUV && j.FinalBackCelloglaze == Matt)
						NumberOptionalExtras += 8;

					if (j.FinalFrontCelloglaze == SpotUV)
						NumberOptionalExtras += 1;

					if (j.FinalFrontCelloglaze == Emboss)
						NumberOptionalExtras +=1;

					if (j.FinalFrontCelloglaze == Foil)
						NumberOptionalExtras += 5;

					if (j.FinalFrontCelloglaze == EmbossFoil)
						NumberOptionalExtras += 5;

					query.SetInt32("OptionalExtras", NumberOptionalExtras);

					query.SetString("Facility", j.Facility.Value.ToString());


					var fsa = ifSubmittedAt.ToString("yyyy-MM-dd HH:mm:ss.fff");

					query.SetDateTime("SubmittedDate", ifSubmittedAt);

					#endregion call stored proc

					DispatchEst = query.UniqueResult<DateTime?>();
					
					string rsa = "";
					try
					{

						Log.Information($@"
							Select * from DespatchEstimate
									where
									((jobOptionID = {j.Template.Id})
										AND(PrintType = '{j.PrintType}')
										AND(PaperSizeID = {j.FinishedSize.PaperSize.Id} OR PaperSizeID = 0)
										AND(StockID = {j.Stock.Id} OR StockID = 0)
										AND(QtyMin <= {j.Quantity}) AND(QtyMax >= {j.Quantity})
										AND(Cello = '{cello}' or Cello = 'Y')
										AND(Folded = '{folder}')
										AND(LetterPress = '{letterpress}')
										AND(Facility = '{j.Facility.Value}' or Facility = 'Y')
									);
						");

						 rsa = $"Select dbo.GetDispatchEst3({j.Template.Id},'{j.PrintType}',{j.FinishedSize.PaperSize.Id},{j.Stock.Id},{j.Quantity},'{cello}','{folder}','{letterpress}','{magnet}',{NumberOptionalExtras},'{j.Facility.Value}', '{fsa}')";

						Log.Information($" {rsa}   =>    {DispatchEst}");
					}
					catch (Exception ee) { }

					if (DispatchEst != null)
						DispatchEst = DispatchEst.Value.Date + Utils3.PickupTime(j.Facility ?? FG, order.Courier);

					if (!estimated)
						estimated = true;
				}
				else
				{
					DispatchEst = j.RequiredByDate;
				}

				if (maxDispatchEst == null || DispatchEst > maxDispatchEst && DispatchEst != null)
					maxDispatchEst = DispatchEst;
			}
			return maxDispatchEst;
		}
		
		
		public virtual DateTime? GetDispatchDate2(IOrder order, DateTime ifSubmittedAt, out bool estimated)
		{
			DateTime? maxDispatchEst = null; //Default DateTime is {01/01/0001 00:00:00}
			estimated = true;
			

			foreach (var j in order.Jobs)
			{
				DateTime? DispatchEst = null;
				if (j.RequiredByDate == null)
				{
					var sql =
						@"Select dbo.GetDispatchEst3(:JobOptionID,:PrintType,:PaperSizeID,:StockID,:Qty,:Cello,:Folder,:LetterPress,:Magnets,:OptionalExtras, :Facility, :SubmittedDate)";

					#region call stored proc

					var cello = j.FinalFrontCelloglaze + "/" + j.FinalBackCelloglaze;
					cello = cello.Replace("AntiScuff", ""); // anti scuff doesnot affect dispatch date

					var folder = !j.IsMagazine() && j.FoldedSize != null ? "Y" : "N";
					var letterpress = "N";

					var NumberOptionalExtras = 0;


					if ((j.DieCutType != CutOptions.None))
					{
						if (j.IsBusinessCard())
						{
							NumberOptionalExtras++;
						}
						else
						{
							letterpress = (j.DieCutType > CutOptions.None) ? "Y" : "N";
						}
					}

					var magnet = j.NumberOfMagnets > 0 ? "Y" : "N";

					IQuery query = Session.CreateSQLQuery(sql);
					var jobOptionId = j.Template.Id;

					query.SetInt32("JobOptionID", jobOptionId);
					query.SetString("PrintType", j.PrintType.ToString());
					query.SetInt32("PaperSizeID", j.FinishedSize.PaperSize.Id);
					query.SetInt32("StockID", j.Stock.Id);
					query.SetInt32("Qty", j.Quantity);
					query.SetString("Cello", cello);
					query.SetString("Folder", folder);
					query.SetString("LetterPress", letterpress);
					query.SetString("Magnets", magnet);
					if (j.Perforating) NumberOptionalExtras += 1;
					if (j.Scoring) NumberOptionalExtras += 1;
					if (j.HoleDrilling != HoleDrilling.None) NumberOptionalExtras += 1;
					if (j.BindingOption != null && j.BindingOption.Name != "Saddle Stitch") NumberOptionalExtras += 1;
					if (j.RoundOption != RoundOption.None) NumberOptionalExtras += 1;


					if (j.FinalFrontCelloglaze == SpotUVFrontMattFront && j.FinalBackCelloglaze == None)
						NumberOptionalExtras += 8;

					if (j.FinalFrontCelloglaze == SpotUV && j.FinalBackCelloglaze == Matt)
						NumberOptionalExtras += 8;

					if (j.FinalFrontCelloglaze == SpotUV)
						NumberOptionalExtras += 1;

					if (j.FinalFrontCelloglaze == Emboss)
						NumberOptionalExtras += 1;

					if (j.FinalFrontCelloglaze == Foil)
						NumberOptionalExtras += 5;

					if (j.FinalFrontCelloglaze == EmbossFoil)
						NumberOptionalExtras += 5;

					query.SetInt32("OptionalExtras", NumberOptionalExtras);

					query.SetString("Facility", j.Facility.Value.ToString());

					query.SetDateTime("SubmittedDate", ifSubmittedAt);

					#endregion call stored proc

					DispatchEst = query.UniqueResult<DateTime?>();
					var fsa = ifSubmittedAt.ToString("yyyy-MM-dd HH:mm:ss.fff");

					string rsa = "";
					try
					{

						Log.Information($@"
							Select * from DespatchEstimate
									where
									((jobOptionID = {j.Template.Id})
										AND(PrintType = '{j.PrintType}')
										AND(PaperSizeID = {j.FinishedSize.PaperSize.Id} OR PaperSizeID = 0)
										AND(StockID = {j.Stock.Id} OR StockID = 0)
										AND(QtyMin <= {j.Quantity}) AND(QtyMax >= {j.Quantity})
										AND(Cello = '{cello}' or Cello = 'Y')
										AND(Folded = '{folder}')
										AND(LetterPress = '{letterpress}')
										AND(Facility = '{j.Facility.Value}' or Facility = 'Y')
									);
						");

						rsa = $"Select dbo.GetDispatchEst3({j.Template.Id},'{j.PrintType}',{j.FinishedSize.PaperSize.Id},{j.Stock.Id},{j.Quantity},'{cello}','{folder}','{letterpress}','{magnet}',{NumberOptionalExtras},'{j.Facility.Value}', '{fsa}')";

						Log.Information($" {rsa}   =>    {DispatchEst}");
					}
					catch (Exception ee) { }

					if (DispatchEst != null)
						DispatchEst = DispatchEst.Value.Date + Utils3.PickupTime(j.Facility ?? FG, order.Courier);

					if (!estimated)
						estimated = true;
				}
				else
				{
					DispatchEst = j.RequiredByDate;
				}

				if (maxDispatchEst == null || DispatchEst > maxDispatchEst && DispatchEst != null)
					maxDispatchEst = DispatchEst;
			}
			return maxDispatchEst;
		}

		public DateTime? UpdateDispatchDate(IOrder order, DateTime ifSubmittedAt)
		{
			var estimated = true;
			order.DispatchEst = GetDispatchDate(order, ifSubmittedAt, out estimated);
			return order.DispatchEst;
		}

		public void ReturnOrder(IOrder order)
		{
			order.Return((IStaff)CurrentUser);
			UpdateDispatchDate(order, DateTime.Now);
			Save(order);

			foreach (var j in order.Jobs)
			{
				if (j.QuoteNeedApprove && j.TrackProgress) emailApp.SendNotification(j, ContentType.JobQuoteRequestAttention);

				if (j.ReadyArtworkApproval == JobApprovalOptions.NeedsApproval && j.TrackProgress)
					emailApp.SendNotification(j, ContentType.JobArtWorkApproval);

				if (j.HasReject && j.TrackProgress) emailApp.SendNotification(j, ContentType.JobReject);
			}
		}

		public PrintEngine PrintEngine { set; get; }

		public LepArtworkScriptFiles LepArtworkScriptFiles
		{
			get { return _lepArtworkScriptFiles; }
		}

		#endregion IOrderApplication Members

		public string ScatteredLogic_AutoPopulatePromotion(IOrder order, IUser currentUser)
		{
			var result = "";
			if (order == null)
				return "";

			// if its a quote then no promotion
			if (order.IsQuote)
			{
				order.Promotion = null;
				order.PromotionJobPriceBenefit = 0;
				return result;
			}

			if (!order.Price.HasValue) return result;
			if (!(order.Jobs.Any() && /*order.Enable &&*/ order.Status == OrderStatusOptions.Open && order.CanUpdate(currentUser)))
				return result;

			if (order.Promotion?.PromotionCode != null)
				return result;

			var promoCodeTxt = "";

			List<CustomerOffer> customerOffers = null;

			customerOffers = promotionApp.ValidPromotionsForCustomer(order.Customer.Username);

			//string tmpPCode = null;
			if (customerOffers != null && customerOffers.Count != 0)
			{
				var offerlist1 =
					"GUAG20,R3M10,R3M15,R3M18,R3M20,WB103M,WB153M,WB203M,WB253M"
						.Split(',');

				foreach (var co in customerOffers)
				{
					string offerCode = co.Promotion.PromotionCode;

					if (offerlist1.Contains(offerCode))
					{
						promoCodeTxt = offerCode;
					}
					else
					{
						if (offerCode.Length > 7)
							switch (offerCode.Substring(0, 8))
							{
								case "My Rewar":
									promoCodeTxt = offerCode;
									break;

								case "GUAG103M":
									promoCodeTxt = offerCode;
									break;

								case "GUAG1110":
									promoCodeTxt = offerCode;
									break;

								case "GUAG1190":
									promoCodeTxt = offerCode;
									break;

								case "GUAG253M":
									promoCodeTxt = offerCode;
									break;

								case "BROCH2030":
									promoCodeTxt = offerCode;
									break;
							} //end of switch
					} //end of co.OfferCode = "GUAG20"
				}
			} //end of customerOffers != null
			  //if after the autopopulation, the promocode is blank, don't bother 'applying' the promobutton
			  // the promoBenifitTr TR should be visible to both Staff and Customer if there is a valid promotion

			if (!string.IsNullOrEmpty(promoCodeTxt))
			{
				//promotionCheckBtn_Command (null, null);
				var sb = new StringBuilder();
				var ignoreOrderStatus = (CurrentUser is IStaff && ((IStaff)CurrentUser).Role == Role.Administrator)
					|| (CurrentUser is IStaff && ((IStaff)CurrentUser).Role == Role.SuperAdministrator);
				
				promotionApp.ApplyPromotionToOrder(promoCodeTxt, order, sb, ignoreOrderStatus);
				result = sb.ToString();
			}

			return result;
		}

		public void ScatteredLogic_PreSaveOrder_ModifyPackDetailAndApplyFCode(IOrder order, IUser currentUser)
		{

			if (!order.HasSplitDelivery)
			{
				// this bit moved to order.cs Courier setter
				if (order.Courier.IsPickup)
				{
					order.PackDetail.IsCustom = false;
					//order.PackDetail.Price = 0;
					order.PickUpCharge = 0;
				}
				else if (order.Courier.IsNone)
				{
					order.Courier = CourierType.None;
					order.PackDetail.FGCourier = CourierType.None;
					order.PackDetail.PMCourier = CourierType.None;
					order.PickUpCharge = 0;
					order.PackDetail.Price = null;
				}
				else if (!order.Courier.IsNone && !order.PackDetail.IsCustom)
				{
					order.PickUpCharge = 0;
					//decimal rate = (decimal)order.PackDetail.Price;
					////order.PackDetail.Price =  FreightApplication.GetCustomerAdjustedFreightPrice( order.Customer.Id, rate );
					//order.FreightPriceCode = order.Customer.FreightPriceCode;
					//decimal margin = freightApp.GetCustomerFreightMarginFromCode(order.FreightPriceCode);
					//rate = rate + (rate * (margin / 100));
					//order.PackDetail.Price = rate;
				}				
			}


		}

		public void ScatteredLogic_PreSaveOrder_SendEmail(IOrder order, IUser currentUser)
		{
			var mailprepay = false;
			foreach (var j in order.Jobs)
				if (j.Status == PreflightDone &&
					!j.MailedPrePayment &&
					(order.Customer.PaymentTerms == PrePay ||
					 order.Customer.PaymentTerms == OnHold) &&
					order.PaymentStatus == AwaitingPayment && j.TrackProgress)
				{
					j.MailedPrePayment = true;
					mailprepay = true;
				}

			if (mailprepay) emailApp.SendNotification(order, ContentType.OrderPrePayment);
		}

		public bool AttachConnote(IOrderConNote connote)
		{
			var order = GetOrder(connote.OrderId);
			connote.Order = order;
			order.ConNotes.Add(connote);
			base.Save(order);

			if ((!order.PackDetail.FGCourier.IsNone && !order.PackDetail.FGCourier.IsPickup) ||
					   (!order.PackDetail.PMCourier.IsNone && !order.PackDetail.PMCourier.IsPickup))
			{
				Log.Information("Sending Courier Email for Order " + order.Id.ToString());

				//emailApp.SendCourierMail(order); // send the email
				emailApp.SendNotification(order, ContentType.CourierDispatch);
				foreach (var cn in order.ConNotes)
				{
					cn.IsEmailGenerated = 1; // mark that orders connotes as email sent
				}
			}
			base.Save(order);
			return true;
		}

		public Facility? GetProductionFacilityByPostCode(string postcode)
		{
			//Set default to Forest Glen
			Facility? facility = Facility.FG;

			try
			{
				if (String.IsNullOrEmpty(postcode)) return facility;
				var cri = Session.CreateCriteria<IPostCode>();
				cri.Add(Restrictions.Eq("Code", postcode));
				cri.SetCacheable(true);
				var code = cri.UniqueResult<IPostCode>();

				if (code != null)
				{
					facility = code?.Facility;
				}
				else
				{
					// lets try with the first 3 digits
					var cri2 = Session.CreateCriteria<IPostCode>();
					cri2.Add(Restrictions.Like("Code", postcode.Substr(0, 3), MatchMode.Start));
					var code2 = cri2.UniqueResult<IPostCode>();
					if (code2 != null)
						facility = code2.Facility;
				}
			}
			catch (Exception ex)
			{
				var m = ex.Message;
			}

			return facility;
		}

		
	}
}
