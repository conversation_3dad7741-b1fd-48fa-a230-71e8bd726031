[2025-06-17 17:54:37.613 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 17:54:37.639 +10:00 INF] Initializing FastReport...
[2025-06-17 17:54:37.710 +10:00 INF] FastReport initialized successfully
[2025-06-17 17:54:38.109 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 17:54:39.446 +10:00 INF] Starting LEP Invoicer at "2025-06-17T17:54:39.4460583+10:00"
[2025-06-17 17:54:39.450 +10:00 INF] Initializing database service...
[2025-06-17 17:54:39.453 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 17:54:39.577 +10:00 INF] Database connection established successfully
[2025-06-17 17:54:39.578 +10:00 INF] Database service initialized successfully
[2025-06-17 17:54:39.581 +10:00 INF] Checking for pending work...
[2025-06-17 17:54:39.584 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 17:54:40.544 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 17:54:40.547 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 17:54:40.564 +10:00 INF] Found 0 credits to invoice
[2025-06-17 17:54:40.566 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 17:54:40.569 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 17:54:40.577 +10:00 INF] No pending work found
[2025-06-17 17:54:40.590 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 17:54:40.591 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 17:54:40.705 +10:00 INF] LEP Invoicer completed successfully in 1259ms. No work to process.
[2025-06-17 17:54:40.712 +10:00 INF] Database connection disposed
[2025-06-17 17:54:40.713 +10:00 INF] LEP Invoicer completed with result: 0
