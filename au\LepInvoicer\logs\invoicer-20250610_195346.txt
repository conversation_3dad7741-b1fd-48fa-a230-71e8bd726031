[2025-06-10 19:53:46.221 +10:00 INF] Starting LEP Invoicer application
[2025-06-10 19:53:47.100 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-10 19:53:49.472 +10:00 INF] Starting LEP Invoicer at "2025-06-10T19:53:49.4715318+10:00"
[2025-06-10 19:53:49.477 +10:00 INF] Initializing database service...
[2025-06-10 19:53:49.504 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-10 19:53:49.677 +10:00 INF] Database connection established successfully
[2025-06-10 19:53:49.679 +10:00 INF] Database service initialized successfully
[2025-06-10 19:53:49.683 +10:00 INF] Checking for pending work...
[2025-06-10 19:53:49.688 +10:00 INF] Getting 500 orders to invoice
[2025-06-10 19:53:50.565 +10:00 INF] Found 2 orders to invoice
[2025-06-10 19:53:50.569 +10:00 INF] Getting 100 credits to invoice
[2025-06-10 19:53:50.621 +10:00 INF] Found 0 credits to invoice
[2025-06-10 19:53:50.627 +10:00 INF] Getting 100 refunds to invoice
[2025-06-10 19:53:50.643 +10:00 INF] Found 0 refunds to invoice
[2025-06-10 19:53:50.646 +10:00 INF] Found pending work: 2 orders
[2025-06-10 19:53:50.649 +10:00 INF] Initializing MYOB and other services...
[2025-06-10 19:53:50.654 +10:00 INF] Initializing MYOB service...
[2025-06-10 19:53:50.657 +10:00 INF] Initializing MYOB with OAuth authentication
[2025-06-10 19:53:50.661 +10:00 INF] Starting OAuth authentication flow
[2025-06-10 19:53:50.769 +10:00 INF] Using existing OAuth tokens
[2025-06-10 19:53:50.771 +10:00 INF] Initializing MYOB services with OAuth keystore
[2025-06-10 19:53:50.777 +10:00 INF] MYOB services initialized successfully
[2025-06-10 19:53:50.785 +10:00 INF] OAuth keystore set for API calls
[2025-06-10 19:53:50.807 +10:00 INF] Getting company files from MYOB
[2025-06-10 19:53:51.164 +10:00 INF] MYOB service initialized with OAuth credentials. Using company file: LEP Colour Printers Pty Ltd
[2025-06-10 19:53:51.179 +10:00 INF] Current cache stats: {"AccountsCount":269,"TaxCodesCount":18,"CustomersCount":393,"LastCacheLoad":"2025-06-10T19:31:07.5727667+10:00","CacheAge":"00:22:43.5981817","IsExpired":false,"CacheDirectory":"C:\\Users\\<USER>\\AppData\\Local\\LepInvoicer\\cache"}
[2025-06-10 19:53:51.220 +10:00 INF] Using cached MYOB data: {"AccountsCount":269,"TaxCodesCount":18,"CustomersCount":393,"LastCacheLoad":"2025-06-10T19:31:07.5727667+10:00","CacheAge":"00:22:43.5981817","IsExpired":false,"CacheDirectory":"C:\\Users\\<USER>\\AppData\\Local\\LepInvoicer\\cache"}
[2025-06-10 19:53:52.654 +10:00 INF] Using cached GST tax code
[2025-06-10 19:53:52.658 +10:00 INF] Using cached freight account
[2025-06-10 19:53:52.661 +10:00 INF] Using cached discounts account
[2025-06-10 19:53:52.664 +10:00 INF] MYOB cache initialization complete: {"AccountsCount":269,"TaxCodesCount":18,"CustomersCount":393,"LastCacheLoad":"2025-06-10T19:31:07.5727667+10:00","CacheAge":"00:22:45.0913267","IsExpired":false,"CacheDirectory":"C:\\Users\\<USER>\\AppData\\Local\\LepInvoicer\\cache"}
[2025-06-10 19:53:52.681 +10:00 INF] MYOB service initialized successfully with company file: LEP Colour Printers Pty Ltd
[2025-06-10 19:53:52.685 +10:00 INF] All services initialized successfully
[2025-06-10 19:53:52.690 +10:00 INF] Processing order invoices...
[2025-06-10 19:53:52.693 +10:00 INF] Getting 500 orders to invoice
[2025-06-10 19:53:53.016 +10:00 INF] Found 2 orders to invoice
[2025-06-10 19:53:53.020 +10:00 INF] Found 2 orders to process
[2025-06-10 19:53:53.027 +10:00 INF] Getting order 1417006
[2025-06-10 19:53:53.365 +10:00 INF] Processing order 1417006 with 1 jobs, total: ¤0.00
[2025-06-10 19:53:53.371 +10:00 WRN] Order 1417006 has zero price, marking as invoiced
[2025-06-10 19:53:53.378 +10:00 INF] Getting order 1416838
[2025-06-10 19:53:53.390 +10:00 INF] Processing order 1416838 with 1 jobs, total: ¤0.00
[2025-06-10 19:53:53.409 +10:00 WRN] Order 1416838 has zero price, marking as invoiced
[2025-06-10 19:53:53.411 +10:00 INF] Order processing completed. Processed: 2, Success: 0, Failed: 2
[2025-06-10 19:53:53.419 +10:00 INF] Processing credit invoices...
[2025-06-10 19:53:53.442 +10:00 INF] Getting 100 credits to invoice
[2025-06-10 19:53:53.448 +10:00 INF] Found 0 credits to invoice
[2025-06-10 19:53:53.456 +10:00 INF] Processing refund invoices...
[2025-06-10 19:53:53.460 +10:00 INF] Getting 100 refunds to invoice
[2025-06-10 19:53:53.485 +10:00 INF] Found 0 refunds to invoice
[2025-06-10 19:53:53.490 +10:00 INF] Cleaning up invoicer logs...
[2025-06-10 19:53:53.574 +10:00 INF] LEP Invoicer completed successfully in 4102ms. Orders: 2, Credits: 0, Refunds: 0
[2025-06-10 19:53:53.586 +10:00 INF] Database connection disposed
[2025-06-10 19:53:53.588 +10:00 INF] LEP Invoicer completed with result: 0
