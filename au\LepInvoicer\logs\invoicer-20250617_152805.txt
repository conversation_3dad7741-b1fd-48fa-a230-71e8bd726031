[2025-06-17 15:28:05.634 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 15:28:05.665 +10:00 INF] Initializing FastReport...
[2025-06-17 15:28:05.753 +10:00 INF] FastReport initialized successfully
[2025-06-17 15:28:06.231 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 15:28:07.631 +10:00 INF] Starting LEP Invoicer at "2025-06-17T15:28:07.6316250+10:00"
[2025-06-17 15:28:07.637 +10:00 INF] Initializing database service...
[2025-06-17 15:28:07.641 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 15:28:07.762 +10:00 INF] Database connection established successfully
[2025-06-17 15:28:07.764 +10:00 INF] Database service initialized successfully
[2025-06-17 15:28:07.769 +10:00 INF] Checking for pending work...
[2025-06-17 15:28:07.773 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 15:28:08.802 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 15:28:08.806 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 15:28:08.820 +10:00 INF] Found 0 credits to invoice
[2025-06-17 15:28:08.822 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 15:28:08.828 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 15:28:08.829 +10:00 INF] No pending work found
[2025-06-17 15:28:08.831 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 15:28:08.832 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 15:28:08.916 +10:00 INF] LEP Invoicer completed successfully in 1284ms. No work to process.
[2025-06-17 15:28:08.923 +10:00 INF] Database connection disposed
[2025-06-17 15:28:08.925 +10:00 INF] LEP Invoicer completed with result: 0
