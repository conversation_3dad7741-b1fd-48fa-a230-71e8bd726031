[2025-06-17 18:29:31.661 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 18:29:31.689 +10:00 INF] Initializing FastReport...
[2025-06-17 18:29:31.775 +10:00 INF] FastReport initialized successfully
[2025-06-17 18:29:32.176 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 18:29:33.411 +10:00 INF] Starting LEP Invoicer at "2025-06-17T18:29:33.4107955+10:00"
[2025-06-17 18:29:33.414 +10:00 INF] Initializing database service...
[2025-06-17 18:29:33.429 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 18:29:33.545 +10:00 INF] Database connection established successfully
[2025-06-17 18:29:33.546 +10:00 INF] Database service initialized successfully
[2025-06-17 18:29:33.549 +10:00 INF] Checking for pending work...
[2025-06-17 18:29:33.552 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 18:29:34.405 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 18:29:34.408 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 18:29:34.420 +10:00 INF] Found 0 credits to invoice
[2025-06-17 18:29:34.422 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 18:29:34.425 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 18:29:34.427 +10:00 INF] No pending work found
[2025-06-17 18:29:34.428 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 18:29:34.429 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 18:29:34.502 +10:00 INF] LEP Invoicer completed successfully in 1091ms. No work to process.
[2025-06-17 18:29:34.508 +10:00 INF] Database connection disposed
[2025-06-17 18:29:34.510 +10:00 INF] LEP Invoicer completed with result: 0
