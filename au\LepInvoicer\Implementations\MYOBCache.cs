using System.Text.Json;
using Microsoft.Extensions.Logging;
using MYOB.AccountRight.SDK.Contracts.Version2.GeneralLedger;
using MYOB.AccountRight.SDK.Contracts.Version2.Contact;
using MYOB.AccountRight.SDK.Contracts.Version2;

namespace LepInvoicer.Implementations;

/// <summary>
/// Cache for MYOB lookups to avoid repeated API calls
/// </summary>
public class MYOBCache
{
    private readonly ILogger<MYOBCache> _logger;
    private readonly string _cacheDirectory;
    private readonly TimeSpan _cacheExpiry;

    // Cache file names
    private const string AccountsCacheFile = "myob_accounts_cache.json";
    private const string TaxCodesCacheFile = "myob_taxcodes_cache.json";
    private const string CustomersCacheFile = "myob_customers_cache.json";

    // In-memory cache
    private Dictionary<string, AccountLink> _accountsCache = new();
    private Dictionary<string, TaxCodeLink> _taxCodesCache = new();
    private Dictionary<string, CustomerLink> _customersCache = new();
    private DateTime _lastCacheLoad = DateTime.MinValue;

    public MYOBCache(ILogger<MYOBCache> logger, string applicationDirectory, TimeSpan? cacheExpiry = null)
    {
        _logger = logger;
        _cacheDirectory = Path.Combine(applicationDirectory, "cache");
        _cacheExpiry = cacheExpiry ?? TimeSpan.FromDays(7); // Default 7 days

        // Ensure cache directory exists
        Directory.CreateDirectory(_cacheDirectory);
        
        // Load existing cache
        LoadCacheFromDisk();
    }

    /// <summary>
    /// Get account link by name, with caching
    /// </summary>
    public AccountLink? GetAccount(string accountName)
    {
        RefreshCacheIfNeeded();
        return _accountsCache.TryGetValue(accountName, out var account) ? account : null;
    }

    /// <summary>
    /// Get tax code link by name, with caching
    /// </summary>
    public TaxCodeLink? GetTaxCode(string taxCodeName)
    {
        RefreshCacheIfNeeded();
        return _taxCodesCache.TryGetValue(taxCodeName, out var taxCode) ? taxCode : null;
    }

    /// <summary>
    /// Get customer link by name, with caching
    /// </summary>
    public CustomerLink? GetCustomer(string customerName)
    {
        RefreshCacheIfNeeded();
        return _customersCache.TryGetValue(customerName, out var customer) ? customer : null;
    }

    /// <summary>
    /// Update accounts cache
    /// </summary>
    public void UpdateAccountsCache(IEnumerable<Account> accounts)
    {
        _accountsCache.Clear();
        foreach (var account in accounts)
        {
            if (!string.IsNullOrEmpty(account.Name))
            {
                _accountsCache[account.Name] = new AccountLink { UID = account.UID };
            }
        }
        
        SaveCacheToDisk(AccountsCacheFile, _accountsCache);
        _lastCacheLoad = DateTime.Now; // Update cache timestamp
        _logger.LogInformation("Updated accounts cache with {Count} accounts", _accountsCache.Count);
    }

    /// <summary>
    /// Update tax codes cache
    /// </summary>
    public void UpdateTaxCodesCache(IEnumerable<TaxCode> taxCodes)
    {
        _taxCodesCache.Clear();
        foreach (var taxCode in taxCodes)
        {
            if (!string.IsNullOrEmpty(taxCode.Code))
            {
                _taxCodesCache[taxCode.Code] = new TaxCodeLink { UID = taxCode.UID };
            }
        }
        
        SaveCacheToDisk(TaxCodesCacheFile, _taxCodesCache);
        _lastCacheLoad = DateTime.Now; // Update cache timestamp
        _logger.LogInformation("Updated tax codes cache with {Count} tax codes", _taxCodesCache.Count);
    }

    /// <summary>
    /// Update customers cache
    /// </summary>
    public void UpdateCustomersCache(IEnumerable<Customer> customers)
    {
        _customersCache.Clear();
        foreach (var customer in customers)
        {
            if (!string.IsNullOrEmpty(customer.CompanyName))
            {
                _customersCache[customer.CompanyName] = new CustomerLink { UID = customer.UID };
            }
        }
        
        SaveCacheToDisk(CustomersCacheFile, _customersCache);
        _lastCacheLoad = DateTime.Now; // Update cache timestamp
        _logger.LogInformation("Updated customers cache with {Count} customers", _customersCache.Count);
    }

    /// <summary>
    /// Clear all caches and force refresh
    /// </summary>
    public void ClearCache()
    {
        _accountsCache.Clear();
        _taxCodesCache.Clear();
        _customersCache.Clear();
        
        // Delete cache files
        DeleteCacheFile(AccountsCacheFile);
        DeleteCacheFile(TaxCodesCacheFile);
        DeleteCacheFile(CustomersCacheFile);
        
        _lastCacheLoad = DateTime.MinValue;
        _logger.LogInformation("Cleared all MYOB caches");
    }

    /// <summary>
    /// Check if cache needs refreshing based on expiry
    /// </summary>
    public bool IsCacheExpired()
    {
        return DateTime.Now - _lastCacheLoad > _cacheExpiry;
    }

    private void RefreshCacheIfNeeded()
    {
        if (IsCacheExpired())
        {
            _logger.LogInformation("MYOB cache expired, will refresh on next MYOB operation");
        }
    }

    private void LoadCacheFromDisk()
    {
        try
        {
            _accountsCache = LoadCacheFromDisk<Dictionary<string, AccountLink>>(AccountsCacheFile) ?? new();
            _taxCodesCache = LoadCacheFromDisk<Dictionary<string, TaxCodeLink>>(TaxCodesCacheFile) ?? new();
            _customersCache = LoadCacheFromDisk<Dictionary<string, CustomerLink>>(CustomersCacheFile) ?? new();
            
            _lastCacheLoad = GetOldestCacheFileDate();
            
            _logger.LogInformation("Loaded MYOB cache: {Accounts} accounts, {TaxCodes} tax codes, {Customers} customers", 
                _accountsCache.Count, _taxCodesCache.Count, _customersCache.Count);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to load MYOB cache from disk, will refresh from API");
            ClearCache();
        }
    }

    private T? LoadCacheFromDisk<T>(string fileName) where T : class
    {
        var filePath = Path.Combine(_cacheDirectory, fileName);
        if (!File.Exists(filePath))
            return null;

        try
        {
            var json = File.ReadAllText(filePath);
            return System.Text.Json.JsonSerializer.Deserialize<T>(json);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to deserialize cache file {FileName}", fileName);
            return null;
        }
    }

    private void SaveCacheToDisk<T>(string fileName, T data)
    {
        var filePath = Path.Combine(_cacheDirectory, fileName);
        try
        {
            _logger.LogDebug("Saving cache to {FilePath}", filePath);
            var json = System.Text.Json.JsonSerializer.Serialize(data, new JsonSerializerOptions { WriteIndented = true });
            File.WriteAllText(filePath, json);
            _logger.LogDebug("Successfully saved cache file {FileName} ({FileSize} bytes)", fileName, json.Length);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to save cache file {FileName} to {FilePath}", fileName, filePath);
        }
    }

    private void DeleteCacheFile(string fileName)
    {
        var filePath = Path.Combine(_cacheDirectory, fileName);
        try
        {
            if (File.Exists(filePath))
                File.Delete(filePath);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to delete cache file {FileName}", fileName);
        }
    }

    private DateTime GetOldestCacheFileDate()
    {
        var files = new[] { AccountsCacheFile, TaxCodesCacheFile, CustomersCacheFile };
        var oldestDate = DateTime.Now;

        foreach (var file in files)
        {
            var filePath = Path.Combine(_cacheDirectory, file);
            if (File.Exists(filePath))
            {
                var fileDate = File.GetLastWriteTime(filePath);
                if (fileDate < oldestDate)
                    oldestDate = fileDate;
            }
        }

        return oldestDate;
    }

    /// <summary>
    /// Get cache statistics for debugging
    /// </summary>
    public object GetCacheStats()
    {
        return new
        {
            AccountsCount = _accountsCache.Count,
            TaxCodesCount = _taxCodesCache.Count,
            CustomersCount = _customersCache.Count,
            LastCacheLoad = _lastCacheLoad,
            CacheAge = DateTime.Now - _lastCacheLoad,
            IsExpired = IsCacheExpired(),
            CacheDirectory = _cacheDirectory
        };
    }
}
