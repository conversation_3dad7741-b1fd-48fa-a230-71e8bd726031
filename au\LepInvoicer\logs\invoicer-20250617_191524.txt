[2025-06-17 19:15:24.586 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 19:15:24.630 +10:00 INF] Initializing FastReport...
[2025-06-17 19:15:24.713 +10:00 INF] FastReport initialized successfully
[2025-06-17 19:15:25.127 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 19:15:26.489 +10:00 INF] Starting LEP Invoicer at "2025-06-17T19:15:26.4890165+10:00"
[2025-06-17 19:15:26.492 +10:00 INF] Initializing database service...
[2025-06-17 19:15:26.495 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 19:15:26.606 +10:00 INF] Database connection established successfully
[2025-06-17 19:15:26.608 +10:00 INF] Database service initialized successfully
[2025-06-17 19:15:26.611 +10:00 INF] Checking for pending work...
[2025-06-17 19:15:26.613 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 19:15:27.481 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 19:15:27.483 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 19:15:27.496 +10:00 INF] Found 0 credits to invoice
[2025-06-17 19:15:27.498 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 19:15:27.501 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 19:15:27.503 +10:00 INF] No pending work found
[2025-06-17 19:15:27.504 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 19:15:27.506 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 19:15:27.580 +10:00 INF] LEP Invoicer completed successfully in 1091ms. No work to process.
[2025-06-17 19:15:27.586 +10:00 INF] Database connection disposed
[2025-06-17 19:15:27.588 +10:00 INF] LEP Invoicer completed with result: 0
