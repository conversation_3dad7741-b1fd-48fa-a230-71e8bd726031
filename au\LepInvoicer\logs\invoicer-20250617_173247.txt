[2025-06-17 17:32:47.980 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 17:32:48.008 +10:00 INF] Initializing FastReport...
[2025-06-17 17:32:48.081 +10:00 INF] FastReport initialized successfully
[2025-06-17 17:32:48.552 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 17:32:49.856 +10:00 INF] Starting LEP Invoicer at "2025-06-17T17:32:49.8557251+10:00"
[2025-06-17 17:32:49.860 +10:00 INF] Initializing database service...
[2025-06-17 17:32:49.864 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 17:32:49.964 +10:00 INF] Database connection established successfully
[2025-06-17 17:32:49.971 +10:00 INF] Database service initialized successfully
[2025-06-17 17:32:49.977 +10:00 INF] Checking for pending work...
[2025-06-17 17:32:49.980 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 17:32:50.912 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 17:32:50.915 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 17:32:50.928 +10:00 INF] Found 0 credits to invoice
[2025-06-17 17:32:50.929 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 17:32:50.934 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 17:32:50.935 +10:00 INF] No pending work found
[2025-06-17 17:32:50.938 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 17:32:50.939 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 17:32:51.014 +10:00 INF] LEP Invoicer completed successfully in 1158ms. No work to process.
[2025-06-17 17:32:51.021 +10:00 INF] Database connection disposed
[2025-06-17 17:32:51.022 +10:00 INF] LEP Invoicer completed with result: 0
