using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.IO;
using System.Linq;
using System.Runtime.Serialization.Formatters.Binary;
using System.Text.RegularExpressions;

namespace lep.extensionmethods
{
    public static class ExtensionMethods
    {
        #region Public Methods

        // public static T DeepCopy<T>(this T o)
        // {
        //     using (var memoryStream = new MemoryStream())
        //     {
        //         var binaryFormatter = new BinaryFormatter();
        //         binaryFormatter.Serialize(memoryStream, o);
        //         memoryStream.Position = 0;
        //         return (T)binaryFormatter.Deserialize(memoryStream);
        //     }
        // }

        public static void DrawRoundRect(this Graphics g, Pen p, Rectangle r, float radius)
        {
            var gp = new GraphicsPath();
            float x = r.X;
            float y = r.Y;
            float width = r.Width;
            float height = r.Height;
            gp.AddLine(x + radius, y, x + width - radius * 2, y); // Line
            gp.AddArc(x + width - radius * 2, y, radius * 2, radius * 2, 270, 90); // Corner
            gp.AddLine(x + width, y + radius, x + width, y + height - radius * 2); // Line
            gp.AddArc(x + width - radius * 2, y + height - radius * 2, radius * 2, radius * 2, 0, 90); // Corner
            gp.AddLine(x + width - radius * 2, y + height, x + radius, y + height); // Line
            gp.AddArc(x, y + height - radius * 2, radius * 2, radius * 2, 90, 90); // Corner
            gp.AddLine(x, y + height - radius * 2, x, y + radius); // Line
            gp.AddArc(x, y, radius * 2, radius * 2, 180, 90); // Corner
            gp.CloseFigure();

            g.DrawPath(p, gp);
            gp.Dispose();
        }

        public static void DrawSticker(this Graphics g, Rectangle r)
        {
            var radius = 10;
            var penBlack3 = new Pen(Color.Black, 3);
            var penRed3 = new Pen(Color.Red, 3);

            g.DrawRectangle(penRed3, r);
            g.FillRectangle(Brushes.Red, r);

            var gp = new GraphicsPath();
            float x = r.X;
            float y = r.Y;
            float width = r.Width;
            float height = r.Height;
            gp.AddLine(x + radius, y, x + width - radius * 2, y); // Line
            gp.AddArc(x + width - radius * 2, y, radius * 2, radius * 2, 270, 90); // Corner
            gp.AddLine(x + width, y + radius, x + width, y + height - radius * 2); // Line
            gp.AddArc(x + width - radius * 2, y + height - radius * 2, radius * 2, radius * 2, 0, 90); // Corner
            gp.AddLine(x + width - radius * 2, y + height, x + radius, y + height); // Line
            gp.AddArc(x, y + height - radius * 2, radius * 2, radius * 2, 90, 90); // Corner
            gp.AddLine(x, y + height - radius * 2, x, y + radius); // Line
            gp.AddArc(x, y, radius * 2, radius * 2, 180, 90); // Corner
            gp.CloseFigure();

            g.DrawPath(penBlack3, gp);
            g.FillPath(Brushes.White, gp);
            gp.Dispose();
            ////
        }

        public static long Epoch(this DateTime t)
        {
            return (int)(t.ToUniversalTime() - new DateTime(1970, 1, 1)).TotalSeconds;
        }

        public static void ShrinkBy(this Rectangle r, int amt)
        {
            r.X = r.Top + amt;
            r.Y = r.Left + amt;

            r.Width -= amt;
            r.Height -= amt;
        }

        public static long Size(this DirectoryInfo dir)
        {
            long totalSize = 0;
            if (dir.Exists)
            {
                var files = Directory.GetFiles(dir.FullName, "*", SearchOption.AllDirectories);
                foreach (var file in files)
                {
                    var f = new FileInfo(file);
                    totalSize += f.Length;
                }
            }
            return totalSize;
        }

        #endregion Public Methods

        public static bool Is<T>(this T given, params T[] candidates) where T : IComparable
        {
            return candidates.Any(_ => _.CompareTo(given) == 0);
        }

        public static bool IsNot<T>(this T given, params T[] candidates) where T : IComparable
        {
            return !candidates.Any(_ => _.CompareTo(given) == 0);
        }

		public static string NullIf(this string given)
		{
			if (String.IsNullOrWhiteSpace(given)) { return null; }
			return given;
		}


		static Regex rgx = new Regex("[^a-zA-Z0-9]");
		public static string Sanitize(this string given)
		{
			if (string.IsNullOrEmpty(given)) return "";
			var str = rgx.Replace(given, " ");
			return str;
		}

	}
}
