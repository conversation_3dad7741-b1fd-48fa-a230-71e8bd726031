[2025-06-18 10:06:50.768 +10:00 INF] Starting LEP Invoicer application
[2025-06-18 10:06:50.796 +10:00 INF] Initializing FastReport...
[2025-06-18 10:06:50.880 +10:00 INF] FastReport initialized successfully
[2025-06-18 10:06:51.263 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-18 10:06:52.569 +10:00 INF] Starting LEP Invoicer at "2025-06-18T10:06:52.5683439+10:00"
[2025-06-18 10:06:52.573 +10:00 INF] Initializing database service...
[2025-06-18 10:06:52.576 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-18 10:06:52.697 +10:00 INF] Database connection established successfully
[2025-06-18 10:06:52.699 +10:00 INF] Database service initialized successfully
[2025-06-18 10:06:52.702 +10:00 INF] Checking for pending work...
[2025-06-18 10:06:52.708 +10:00 INF] Getting 500 orders to invoice
[2025-06-18 10:06:53.605 +10:00 INF] Found 0 orders to invoice (filtered 16 candidates)
[2025-06-18 10:06:53.610 +10:00 INF] Getting 100 credits to invoice
[2025-06-18 10:06:53.624 +10:00 INF] Found 0 credits to invoice
[2025-06-18 10:06:53.627 +10:00 INF] Getting 100 refunds to invoice
[2025-06-18 10:06:53.633 +10:00 INF] Found 0 refunds to invoice
[2025-06-18 10:06:53.640 +10:00 INF] No pending work found
[2025-06-18 10:06:53.643 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-18 10:06:53.649 +10:00 INF] Cleaning up invoicer logs...
[2025-06-18 10:06:53.750 +10:00 INF] LEP Invoicer completed successfully in 1181ms. No work to process.
[2025-06-18 10:06:53.764 +10:00 INF] Database connection disposed
[2025-06-18 10:06:53.767 +10:00 INF] LEP Invoicer completed with result: 0
