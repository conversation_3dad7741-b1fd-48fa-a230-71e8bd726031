[2025-06-17 18:24:04.248 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 18:24:04.278 +10:00 INF] Initializing FastReport...
[2025-06-17 18:24:04.369 +10:00 INF] FastReport initialized successfully
[2025-06-17 18:24:04.878 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 18:24:06.321 +10:00 INF] Starting LEP Invoicer at "2025-06-17T18:24:06.3211984+10:00"
[2025-06-17 18:24:06.325 +10:00 INF] Initializing database service...
[2025-06-17 18:24:06.328 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 18:24:06.430 +10:00 INF] Database connection established successfully
[2025-06-17 18:24:06.431 +10:00 INF] Database service initialized successfully
[2025-06-17 18:24:06.434 +10:00 INF] Checking for pending work...
[2025-06-17 18:24:06.437 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 18:24:07.407 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 18:24:07.410 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 18:24:07.423 +10:00 INF] Found 0 credits to invoice
[2025-06-17 18:24:07.426 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 18:24:07.430 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 18:24:07.432 +10:00 INF] No pending work found
[2025-06-17 18:24:07.433 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 18:24:07.435 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 18:24:07.511 +10:00 INF] LEP Invoicer completed successfully in 1190ms. No work to process.
[2025-06-17 18:24:07.518 +10:00 INF] Database connection disposed
[2025-06-17 18:24:07.520 +10:00 INF] LEP Invoicer completed with result: 0
