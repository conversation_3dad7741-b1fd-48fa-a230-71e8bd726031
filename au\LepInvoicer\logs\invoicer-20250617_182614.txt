[2025-06-17 18:26:14.686 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 18:26:14.713 +10:00 INF] Initializing FastReport...
[2025-06-17 18:26:14.787 +10:00 INF] FastReport initialized successfully
[2025-06-17 18:26:15.205 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 18:26:16.499 +10:00 INF] Starting LEP Invoicer at "2025-06-17T18:26:16.4994084+10:00"
[2025-06-17 18:26:16.510 +10:00 INF] Initializing database service...
[2025-06-17 18:26:16.520 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 18:26:16.624 +10:00 INF] Database connection established successfully
[2025-06-17 18:26:16.626 +10:00 INF] Database service initialized successfully
[2025-06-17 18:26:16.630 +10:00 INF] Checking for pending work...
[2025-06-17 18:26:16.636 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 18:26:17.572 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 18:26:17.575 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 18:26:17.588 +10:00 INF] Found 0 credits to invoice
[2025-06-17 18:26:17.590 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 18:26:17.595 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 18:26:17.596 +10:00 INF] No pending work found
[2025-06-17 18:26:17.597 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 18:26:17.599 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 18:26:17.677 +10:00 INF] LEP Invoicer completed successfully in 1177ms. No work to process.
[2025-06-17 18:26:17.683 +10:00 INF] Database connection disposed
[2025-06-17 18:26:17.685 +10:00 INF] LEP Invoicer completed with result: 0
