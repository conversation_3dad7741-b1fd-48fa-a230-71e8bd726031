[2025-06-17 17:16:29.486 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 17:16:29.513 +10:00 INF] Initializing FastReport...
[2025-06-17 17:16:29.583 +10:00 INF] FastReport initialized successfully
[2025-06-17 17:16:29.934 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 17:16:31.165 +10:00 INF] Starting LEP Invoicer at "2025-06-17T17:16:31.1652630+10:00"
[2025-06-17 17:16:31.169 +10:00 INF] Initializing database service...
[2025-06-17 17:16:31.172 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 17:16:31.275 +10:00 INF] Database connection established successfully
[2025-06-17 17:16:31.277 +10:00 INF] Database service initialized successfully
[2025-06-17 17:16:31.280 +10:00 INF] Checking for pending work...
[2025-06-17 17:16:31.284 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 17:16:32.166 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 17:16:32.168 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 17:16:32.181 +10:00 INF] Found 0 credits to invoice
[2025-06-17 17:16:32.191 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 17:16:32.196 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 17:16:32.197 +10:00 INF] No pending work found
[2025-06-17 17:16:32.199 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 17:16:32.200 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 17:16:32.294 +10:00 INF] LEP Invoicer completed successfully in 1128ms. No work to process.
[2025-06-17 17:16:32.300 +10:00 INF] Database connection disposed
[2025-06-17 17:16:32.304 +10:00 INF] LEP Invoicer completed with result: 0
