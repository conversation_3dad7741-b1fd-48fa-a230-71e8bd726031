[2025-06-18 09:11:37.807 +10:00 INF] Starting LEP Invoicer application
[2025-06-18 09:11:37.836 +10:00 INF] Initializing FastReport...
[2025-06-18 09:11:37.922 +10:00 INF] FastReport initialized successfully
[2025-06-18 09:11:38.488 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-18 09:11:39.831 +10:00 INF] Starting LEP Invoicer at "2025-06-18T09:11:39.8311505+10:00"
[2025-06-18 09:11:39.835 +10:00 INF] Initializing database service...
[2025-06-18 09:11:39.838 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-18 09:11:39.957 +10:00 INF] Database connection established successfully
[2025-06-18 09:11:39.958 +10:00 INF] Database service initialized successfully
[2025-06-18 09:11:39.961 +10:00 INF] Checking for pending work...
[2025-06-18 09:11:39.965 +10:00 INF] Getting 500 orders to invoice
[2025-06-18 09:11:40.863 +10:00 INF] Found 0 orders to invoice (filtered 16 candidates)
[2025-06-18 09:11:40.868 +10:00 INF] Getting 100 credits to invoice
[2025-06-18 09:11:40.881 +10:00 INF] Found 0 credits to invoice
[2025-06-18 09:11:40.885 +10:00 INF] Getting 100 refunds to invoice
[2025-06-18 09:11:40.891 +10:00 INF] Found 0 refunds to invoice
[2025-06-18 09:11:40.905 +10:00 INF] No pending work found
[2025-06-18 09:11:40.908 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-18 09:11:40.909 +10:00 INF] Cleaning up invoicer logs...
[2025-06-18 09:11:41.034 +10:00 INF] LEP Invoicer completed successfully in 1203ms. No work to process.
[2025-06-18 09:11:41.040 +10:00 INF] Database connection disposed
[2025-06-18 09:11:41.042 +10:00 INF] LEP Invoicer completed with result: 0
