[2025-06-17 16:45:58.733 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 16:45:58.772 +10:00 INF] Initializing FastReport...
[2025-06-17 16:45:58.902 +10:00 INF] FastReport initialized successfully
[2025-06-17 16:45:59.481 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 16:46:01.037 +10:00 INF] Starting LEP Invoicer at "2025-06-17T16:46:01.0367536+10:00"
[2025-06-17 16:46:01.042 +10:00 INF] Initializing database service...
[2025-06-17 16:46:01.045 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 16:46:01.179 +10:00 INF] Database connection established successfully
[2025-06-17 16:46:01.182 +10:00 INF] Database service initialized successfully
[2025-06-17 16:46:01.186 +10:00 INF] Checking for pending work...
[2025-06-17 16:46:01.199 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 16:46:03.123 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 16:46:03.127 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 16:46:03.157 +10:00 INF] Found 0 credits to invoice
[2025-06-17 16:46:03.163 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 16:46:03.181 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 16:46:03.183 +10:00 INF] No pending work found
[2025-06-17 16:46:03.187 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 16:46:03.189 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 16:46:03.443 +10:00 INF] LEP Invoicer completed successfully in 2406ms. No work to process.
[2025-06-17 16:46:03.471 +10:00 INF] Database connection disposed
[2025-06-17 16:46:03.473 +10:00 INF] LEP Invoicer completed with result: 0
