[2025-06-18 08:47:14.591 +10:00 INF] Starting LEP Invoicer application
[2025-06-18 08:47:14.623 +10:00 INF] Initializing FastReport...
[2025-06-18 08:47:14.703 +10:00 INF] FastReport initialized successfully
[2025-06-18 08:47:15.159 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-18 08:47:16.684 +10:00 INF] Starting LEP Invoicer at "2025-06-18T08:47:16.6845886+10:00"
[2025-06-18 08:47:16.709 +10:00 INF] Initializing database service...
[2025-06-18 08:47:16.728 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-18 08:47:16.841 +10:00 INF] Database connection established successfully
[2025-06-18 08:47:16.842 +10:00 INF] Database service initialized successfully
[2025-06-18 08:47:16.845 +10:00 INF] Checking for pending work...
[2025-06-18 08:47:16.868 +10:00 INF] Getting 500 orders to invoice
[2025-06-18 08:47:17.794 +10:00 INF] Found 0 orders to invoice (filtered 16 candidates)
[2025-06-18 08:47:17.798 +10:00 INF] Getting 100 credits to invoice
[2025-06-18 08:47:17.823 +10:00 INF] Found 0 credits to invoice
[2025-06-18 08:47:17.826 +10:00 INF] Getting 100 refunds to invoice
[2025-06-18 08:47:17.829 +10:00 INF] Found 0 refunds to invoice
[2025-06-18 08:47:17.831 +10:00 INF] No pending work found
[2025-06-18 08:47:17.833 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-18 08:47:17.836 +10:00 INF] Cleaning up invoicer logs...
[2025-06-18 08:47:17.923 +10:00 INF] LEP Invoicer completed successfully in 1238ms. No work to process.
[2025-06-18 08:47:17.929 +10:00 INF] Database connection disposed
[2025-06-18 08:47:17.930 +10:00 INF] LEP Invoicer completed with result: 0
