[2025-06-17 19:40:30.604 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 19:40:30.632 +10:00 INF] Initializing FastReport...
[2025-06-17 19:40:30.709 +10:00 INF] FastReport initialized successfully
[2025-06-17 19:40:31.178 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 19:40:32.445 +10:00 INF] Starting LEP Invoicer at "2025-06-17T19:40:32.4455381+10:00"
[2025-06-17 19:40:32.449 +10:00 INF] Initializing database service...
[2025-06-17 19:40:32.452 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 19:40:32.551 +10:00 INF] Database connection established successfully
[2025-06-17 19:40:32.553 +10:00 INF] Database service initialized successfully
[2025-06-17 19:40:32.555 +10:00 INF] Checking for pending work...
[2025-06-17 19:40:32.561 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 19:40:33.432 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 19:40:33.434 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 19:40:33.452 +10:00 INF] Found 0 credits to invoice
[2025-06-17 19:40:33.455 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 19:40:33.458 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 19:40:33.460 +10:00 INF] No pending work found
[2025-06-17 19:40:33.462 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 19:40:33.477 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 19:40:33.571 +10:00 INF] LEP Invoicer completed successfully in 1122ms. No work to process.
[2025-06-17 19:40:33.578 +10:00 INF] Database connection disposed
[2025-06-17 19:40:33.580 +10:00 INF] LEP Invoicer completed with result: 0
