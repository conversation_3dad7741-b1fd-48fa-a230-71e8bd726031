[2025-06-17 19:57:57.616 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 19:57:57.651 +10:00 INF] Initializing FastReport...
[2025-06-17 19:57:57.723 +10:00 INF] FastReport initialized successfully
[2025-06-17 19:57:58.159 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 19:57:59.410 +10:00 INF] Starting LEP Invoicer at "2025-06-17T19:57:59.4100917+10:00"
[2025-06-17 19:57:59.413 +10:00 INF] Initializing database service...
[2025-06-17 19:57:59.416 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 19:57:59.512 +10:00 INF] Database connection established successfully
[2025-06-17 19:57:59.513 +10:00 INF] Database service initialized successfully
[2025-06-17 19:57:59.516 +10:00 INF] Checking for pending work...
[2025-06-17 19:57:59.519 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 19:58:00.447 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 19:58:00.450 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 19:58:00.462 +10:00 INF] Found 0 credits to invoice
[2025-06-17 19:58:00.465 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 19:58:00.470 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 19:58:00.471 +10:00 INF] No pending work found
[2025-06-17 19:58:00.473 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 19:58:00.474 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 19:58:00.559 +10:00 INF] LEP Invoicer completed successfully in 1149ms. No work to process.
[2025-06-17 19:58:00.566 +10:00 INF] Database connection disposed
[2025-06-17 19:58:00.568 +10:00 INF] LEP Invoicer completed with result: 0
