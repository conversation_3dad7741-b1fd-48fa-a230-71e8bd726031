[2025-06-17 16:47:05.676 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 16:47:05.703 +10:00 INF] Initializing FastReport...
[2025-06-17 16:47:05.776 +10:00 INF] FastReport initialized successfully
[2025-06-17 16:47:06.190 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 16:47:07.493 +10:00 INF] Starting LEP Invoicer at "2025-06-17T16:47:07.4936288+10:00"
[2025-06-17 16:47:07.497 +10:00 INF] Initializing database service...
[2025-06-17 16:47:07.502 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 16:47:07.625 +10:00 INF] Database connection established successfully
[2025-06-17 16:47:07.626 +10:00 INF] Database service initialized successfully
[2025-06-17 16:47:07.629 +10:00 INF] Checking for pending work...
[2025-06-17 16:47:07.632 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 16:47:08.568 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 16:47:08.571 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 16:47:08.586 +10:00 INF] Found 0 credits to invoice
[2025-06-17 16:47:08.588 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 16:47:08.592 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 16:47:08.593 +10:00 INF] No pending work found
[2025-06-17 16:47:08.594 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 16:47:08.596 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 16:47:08.671 +10:00 INF] LEP Invoicer completed successfully in 1177ms. No work to process.
[2025-06-17 16:47:08.689 +10:00 INF] Database connection disposed
[2025-06-17 16:47:08.691 +10:00 INF] LEP Invoicer completed with result: 0
