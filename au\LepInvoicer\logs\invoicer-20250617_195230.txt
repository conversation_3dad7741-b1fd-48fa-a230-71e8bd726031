[2025-06-17 19:52:30.586 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 19:52:30.620 +10:00 INF] Initializing FastReport...
[2025-06-17 19:52:30.691 +10:00 INF] FastReport initialized successfully
[2025-06-17 19:52:31.103 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 19:52:32.403 +10:00 INF] Starting LEP Invoicer at "2025-06-17T19:52:32.4026822+10:00"
[2025-06-17 19:52:32.406 +10:00 INF] Initializing database service...
[2025-06-17 19:52:32.409 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 19:52:32.508 +10:00 INF] Database connection established successfully
[2025-06-17 19:52:32.511 +10:00 INF] Database service initialized successfully
[2025-06-17 19:52:32.514 +10:00 INF] Checking for pending work...
[2025-06-17 19:52:32.519 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 19:52:33.454 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 19:52:33.456 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 19:52:33.468 +10:00 INF] Found 0 credits to invoice
[2025-06-17 19:52:33.470 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 19:52:33.474 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 19:52:33.476 +10:00 INF] No pending work found
[2025-06-17 19:52:33.477 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 19:52:33.478 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 19:52:33.561 +10:00 INF] LEP Invoicer completed successfully in 1158ms. No work to process.
[2025-06-17 19:52:33.574 +10:00 INF] Database connection disposed
[2025-06-17 19:52:33.579 +10:00 INF] LEP Invoicer completed with result: 0
