[2025-06-17 18:13:10.472 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 18:13:10.499 +10:00 INF] Initializing FastReport...
[2025-06-17 18:13:10.571 +10:00 INF] FastReport initialized successfully
[2025-06-17 18:13:10.997 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 18:13:12.313 +10:00 INF] Starting LEP Invoicer at "2025-06-17T18:13:12.3132864+10:00"
[2025-06-17 18:13:12.320 +10:00 INF] Initializing database service...
[2025-06-17 18:13:12.323 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 18:13:12.448 +10:00 INF] Database connection established successfully
[2025-06-17 18:13:12.450 +10:00 INF] Database service initialized successfully
[2025-06-17 18:13:12.454 +10:00 INF] Checking for pending work...
[2025-06-17 18:13:12.457 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 18:13:13.361 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 18:13:13.363 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 18:13:13.376 +10:00 INF] Found 0 credits to invoice
[2025-06-17 18:13:13.378 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 18:13:13.382 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 18:13:13.384 +10:00 INF] No pending work found
[2025-06-17 18:13:13.396 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 18:13:13.397 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 18:13:13.496 +10:00 INF] LEP Invoicer completed successfully in 1183ms. No work to process.
[2025-06-17 18:13:13.511 +10:00 INF] Database connection disposed
[2025-06-17 18:13:13.513 +10:00 INF] LEP Invoicer completed with result: 0
