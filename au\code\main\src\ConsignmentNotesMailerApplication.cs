using lep.content;
using lep.email;
using lep.order;
using lep.order.impl;
using lep.security;
using NHibernate;
using Serilog;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;

namespace lep
{
	// EXPIRED NOW.

	public class ConsignmentNotesMailerApplication : BaseApplication
	{
		// private static readonly log4net.ILog log =log4net.LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);


		public ConsignmentNotesMailerApplication(ISession sf, ISecurityApplication _securityApp, IEmailApplication emailApplication) : base(sf, _securityApp)
		{
			this.EmailApplication = emailApplication;
		}


		public IEmailApplication EmailApplication { get; set; }

		//public void BringInSupplyMasterConnnotes()
		//{
		//	try
		//	{
		//		var smConnStr = LepGlobal.Instance.SupplyMasterConnectionString;
		//		var smConnotes = @"
		//				SELECT
		//					CAST (CONNOTE.REFERENCE_NO  as int)                    AS OrderId,
		//					COALESCE (CONNOTE_DETAILS_REF.ALLOCA, CONNOTE.CONS_NO) AS ConNote,
		//					CASE CONNOTE.SENDER_CODE
		//						WHEN 'LEP' THEN 'FG'
		//						WHEN 'LEPVIC' THEN 'PM'
		//					END               AS DispatchFacility,
		//					CONNOTE.CARR_NAME AS CarrierName,
		//					CONNOTE.SERV_NAME AS CarrierService,
		//					CONNOTE.CONS_KEY  AS PKey
		//				FROM    CONNOTE LEFT OUTER JOIN
		//						CONNOTE_DETAILS_REF ON CONNOTE.CONS_KEY = CONNOTE_DETAILS_REF.CONS_KEY
		//				WHERE
		//					(CONNOTE.MANIFEST_NO <> 0) AND
		//					(CONNOTE.LEP_ConnoteGenerated = 'N') AND
		//					(ISNUMERIC(CONNOTE.REFERENCE_NO) = 1)
		//				ORDER BY CONNOTE.REFERENCE_NO
		//			";

		//		var pKeys = new List<int>();
		//		var now = DateTime.Now;
		//		int c = 0;

		//		using (var smConn = new SqlConnection(smConnStr))
		//		{
		//			smConn.Open();
		//			using (var command = new SqlCommand(smConnotes, smConn))
		//			using (var reader = command.ExecuteReader())
		//			{
		//				while (reader.Read())
		//				{
		//					var orderId = reader.GetInt32(0);
		//					var connote = reader.GetString(1);
		//					var dispatchFacility = reader.GetString(2);
		//					var carrierName = reader.GetString(3);
		//					var carrierService = reader.GetString(4);
		//					var pKey = reader.GetInt32(5);

		//					var oo = Session.Get<IOrder>(orderId);
		//					if (oo == null)
		//					{
		//						//Log.Error($"BringInSupplyMasterConnnotes  {orderId} does not exist");
		//						continue;
		//					}

		//					Session.SaveOrUpdate(new OrderConNote()
		//					{
		//						Order = new Order { Id = orderId },
		//						OrderId = orderId,
		//						ConNote = connote,
		//						DispatchFacility = dispatchFacility == "PM" ? job.Facility.PM : job.Facility.FG,
		//						CarrierName = carrierName,
		//						CarrierService = carrierService,
		//						DateCreated = now,
		//						DateModified = now,
		//						IsEmailGenerated = 0,
		//					});
		//					pKeys.Add(pKey);
		//					Log.Information($"SM -> LEP:  {orderId} {connote} {carrierName} {carrierService}");
		//					c++;
		//				}
		//			}

		//			if (c > 0)
		//			{
		//				Log.Information($"imported {c} connotes from supply master\n\n\n");
		//				var updateSMcmd = smConn.CreateCommand();
		//				var pkeysStr = string.Join(",", pKeys.Select(k => k.ToString()).ToArray());
		//				updateSMcmd.CommandText = $"UPDATE CONNOTE SET LEP_CONNOTEGENERATED='Y' WHERE CONS_KEY in ({pkeysStr})";
		//				updateSMcmd.ExecuteNonQuery();
		//			}

		//			smConn.Close();
		//		}
		//	}
		//	catch (Exception ex)
		//	{
		//		Log.Error(ex.Message, ex);
		//	}
		//}

		public void CronTask()
		{
			var sql = "select distinct orderId from ConNote where IsEmailGenerated = 0 and DateCreated > DATEADD(day, -1, getdate())";
			var ordersIds = Session.CreateSQLQuery(sql).List<int>().Take(50);
			var v = ordersIds.Count();
			if (v == 0)
			{
				return;
			}

			Log.Information($"Found {v} orders to send connote email");
			foreach (var oid in ordersIds)
			{
				//RunWithTransaction(Session, () => {
				try
				{
					var order = Session.Get<IOrder>(oid);
					Session.Refresh(order);
					//if ((!order.PackDetail.FGCourier.IsNone && !order.PackDetail.FGCourier.IsPickup) ||
					//(!order.PackDetail.PMCourier.IsNone && !order.PackDetail.PMCourier.IsPickup)) {
					Log.Information("Sending Courier Email for Order " + oid.ToString());
					//EmailApplication.SendCourierMail(order); // send the email
					EmailApplication.SendNotification(order, ContentType.CourierDispatch);
					foreach (var cn in order.ConNotes)
					{
						cn.IsEmailGenerated = 1; // mark that orders connotes as email sent
					}
					Save(order);

					//}
				}
				catch (Exception ex)
				{
					Log.Error(ex, ex.Message);
					throw;
				}

				//});
			}
		}
	}
}
