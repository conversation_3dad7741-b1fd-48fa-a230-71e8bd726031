using lep.address;
using lep.address.impl;
using lep.contact;
using lep.courier;
using lep.printPortal;

using System;
using System.Collections.Generic;

namespace lep.user
{


	public interface ICustomerContact
	{
		String Name { get; set; }
		String Email { get; set; }
		bool ReceiveMarketingEmails { get; set; }
		String Mobile { get; set; }
		String Phone { get; set; }
		ICustomerUser Customer { get; set;}
	}

	public interface ICustomerNote
	{
		int Id { get; set; }
		ICustomerUser Customer { get; set; }
		String NoteText { get; set; }
		String CreatedBy { get; set; }
		DateTime CreatedOn { get; set; }
	}

	public interface ICustomerUser : IUser
	{
		String CustomerNr { get; }
		String Name { get; set; }
		string BusinessType { get; set; }
		String ABN { get; set; }
		String MYOB { get; set; }
		Boolean HasSystemAccess { get; set; }
		Boolean PostalIsBilling { get; set; }
		IPhysicalAddress BillingAddress { get; set; }
		IPhysicalAddress PostalAddress { get; set; }
		IContact Contact1 { get; }
		//IContact Contact2 { get; set; }

		ListOfContacts Contacts { get; set; }
		string ContactsJsonStr { get; set; }

		DateTime? LastOrderDate { get; set; }
		int CreditLimit { get; set; }
		String MYOBBalance { get; set; }
		decimal? MYOBPastDue { get; set; }
		NotificationType NotificationType { get; set; }
		PaymentTermsOptions PaymentTerms { get; set; }
		CourierType PreferredCourier { get; set; }

		// if true then customer will see the send samples tick box
		bool AllowedSamples { get; set; }

		// if true on new order send samples will be ticked by default
		bool SendSamples { get; set; }

		string AccountEmail { get; set; }
		string OtherEmail { get; set; }
		bool IsChargedGST { get; set; }
		bool IsEnrolledInIVR { get; set; }
		string Notes { get; set; }
		string ProductionNotes { get; set; }
		SiteLocation SiteLocation { get; set; }
		string ProductPriceCode { get; set; }
		string FreightPriceCode { get; set; }
		decimal LEPOnlineBalance { get; set; }
		decimal AvaiableBalance { get; set; }
		decimal MAT { get; set; }
		decimal MAT3ma { get; set; }
		//ListOfDeliveryDetails FavouriteDeliveryDetails { get; set; }

		IList<DeliveryDetails> FavouriteDeliveryDetails { get; set; }

		Boolean QTP { get; set; }
		String CustomerStatus { get; set; }
		DateTime? ReturnedLapseDate { get; set; }
		String FranchiseCode { get; set; }
		String SalesConsultant { get; set; }
		DateTime? FirstOrderDate { get; set; }
		DateTime? SampleKitSentOn { get; set; }
		String Potential { get; set; }
		List<int> DeniedTemplates { get; set; }

		List<int> CombinedDeniedTemplates();

		// white label fields
		bool IsPrintPortalEnabled { get; set; }

		//bool IsAacEnabled { get; set; }
		//string CustomCssURL { get; set; }
		//string PayPalClientId { get; set; }
		//string StripePublishableKey { get; set; }
		//string StripeRestrictedChargeKey { get; set; }
		//List<string> DeniedTemplates { get; set; }
		//PricingModel? PricingModel { get; set; }
		//decimal WhiteLabelGlobalMarkup { get; set; }
		//List<CategoryMarkup> CategoryMarkups { get; set; }
		//List<PriceRangeMarkup> PriceRangeMarkups { get; set; }
		PrintPortalSettings PrintPortalSettings { get; set; }

		ICustomerUser ParentCustomer { get; set; }
		Guid Gid { get; set; }

		bool Archived { get; set; }

		string MyobUid { get; set; }
	}
}
