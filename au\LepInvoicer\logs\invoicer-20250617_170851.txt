[2025-06-17 17:08:51.725 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 17:08:51.752 +10:00 INF] Initializing FastReport...
[2025-06-17 17:08:51.824 +10:00 INF] FastReport initialized successfully
[2025-06-17 17:08:52.236 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 17:08:53.493 +10:00 INF] Starting LEP Invoicer at "2025-06-17T17:08:53.4931627+10:00"
[2025-06-17 17:08:53.496 +10:00 INF] Initializing database service...
[2025-06-17 17:08:53.499 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 17:08:53.596 +10:00 INF] Database connection established successfully
[2025-06-17 17:08:53.597 +10:00 INF] Database service initialized successfully
[2025-06-17 17:08:53.599 +10:00 INF] Checking for pending work...
[2025-06-17 17:08:53.602 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 17:08:54.443 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 17:08:54.445 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 17:08:54.458 +10:00 INF] Found 0 credits to invoice
[2025-06-17 17:08:54.460 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 17:08:54.467 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 17:08:54.468 +10:00 INF] No pending work found
[2025-06-17 17:08:54.470 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 17:08:54.471 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 17:08:54.549 +10:00 INF] LEP Invoicer completed successfully in 1056ms. No work to process.
[2025-06-17 17:08:54.555 +10:00 INF] Database connection disposed
[2025-06-17 17:08:54.557 +10:00 INF] LEP Invoicer completed with result: 0
