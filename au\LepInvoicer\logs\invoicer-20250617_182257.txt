[2025-06-17 18:22:57.770 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 18:22:57.798 +10:00 INF] Initializing FastReport...
[2025-06-17 18:22:57.874 +10:00 INF] FastReport initialized successfully
[2025-06-17 18:22:58.323 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 18:22:59.577 +10:00 INF] Starting LEP Invoicer at "2025-06-17T18:22:59.5767268+10:00"
[2025-06-17 18:22:59.580 +10:00 INF] Initializing database service...
[2025-06-17 18:22:59.583 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 18:22:59.683 +10:00 INF] Database connection established successfully
[2025-06-17 18:22:59.685 +10:00 INF] Database service initialized successfully
[2025-06-17 18:22:59.687 +10:00 INF] Checking for pending work...
[2025-06-17 18:22:59.691 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 18:23:00.577 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 18:23:00.581 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 18:23:00.593 +10:00 INF] Found 0 credits to invoice
[2025-06-17 18:23:00.595 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 18:23:00.600 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 18:23:00.601 +10:00 INF] No pending work found
[2025-06-17 18:23:00.602 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 18:23:00.604 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 18:23:00.684 +10:00 INF] LEP Invoicer completed successfully in 1108ms. No work to process.
[2025-06-17 18:23:00.692 +10:00 INF] Database connection disposed
[2025-06-17 18:23:00.694 +10:00 INF] LEP Invoicer completed with result: 0
