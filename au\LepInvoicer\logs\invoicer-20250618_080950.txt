[2025-06-18 08:09:50.516 +10:00 INF] Starting LEP Invoicer application
[2025-06-18 08:09:50.544 +10:00 INF] Initializing FastReport...
[2025-06-18 08:09:50.616 +10:00 INF] FastReport initialized successfully
[2025-06-18 08:09:51.013 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-18 08:09:52.459 +10:00 INF] Starting LEP Invoicer at "2025-06-18T08:09:52.4588557+10:00"
[2025-06-18 08:09:52.463 +10:00 INF] Initializing database service...
[2025-06-18 08:09:52.466 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-18 08:09:52.589 +10:00 INF] Database connection established successfully
[2025-06-18 08:09:52.590 +10:00 INF] Database service initialized successfully
[2025-06-18 08:09:52.594 +10:00 INF] Checking for pending work...
[2025-06-18 08:09:52.597 +10:00 INF] Getting 500 orders to invoice
[2025-06-18 08:09:53.658 +10:00 INF] Found 0 orders to invoice (filtered 16 candidates)
[2025-06-18 08:09:53.661 +10:00 INF] Getting 100 credits to invoice
[2025-06-18 08:09:53.674 +10:00 INF] Found 0 credits to invoice
[2025-06-18 08:09:53.679 +10:00 INF] Getting 100 refunds to invoice
[2025-06-18 08:09:53.685 +10:00 INF] Found 0 refunds to invoice
[2025-06-18 08:09:53.688 +10:00 INF] No pending work found
[2025-06-18 08:09:53.689 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-18 08:09:53.693 +10:00 INF] Cleaning up invoicer logs...
[2025-06-18 08:09:53.802 +10:00 INF] LEP Invoicer completed successfully in 1343ms. No work to process.
[2025-06-18 08:09:53.810 +10:00 INF] Database connection disposed
[2025-06-18 08:09:53.812 +10:00 INF] LEP Invoicer completed with result: 0
