[2025-06-17 18:08:48.571 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 18:08:48.598 +10:00 INF] Initializing FastReport...
[2025-06-17 18:08:48.673 +10:00 INF] FastReport initialized successfully
[2025-06-17 18:08:49.066 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 18:08:50.301 +10:00 INF] Starting LEP Invoicer at "2025-06-17T18:08:50.3015570+10:00"
[2025-06-17 18:08:50.305 +10:00 INF] Initializing database service...
[2025-06-17 18:08:50.308 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 18:08:50.406 +10:00 INF] Database connection established successfully
[2025-06-17 18:08:50.407 +10:00 INF] Database service initialized successfully
[2025-06-17 18:08:50.410 +10:00 INF] Checking for pending work...
[2025-06-17 18:08:50.414 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 18:08:51.257 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 18:08:51.262 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 18:08:51.287 +10:00 INF] Found 0 credits to invoice
[2025-06-17 18:08:51.289 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 18:08:51.293 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 18:08:51.296 +10:00 INF] No pending work found
[2025-06-17 18:08:51.297 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 18:08:51.299 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 18:08:51.392 +10:00 INF] LEP Invoicer completed successfully in 1090ms. No work to process.
[2025-06-17 18:08:51.399 +10:00 INF] Database connection disposed
[2025-06-17 18:08:51.402 +10:00 INF] LEP Invoicer completed with result: 0
