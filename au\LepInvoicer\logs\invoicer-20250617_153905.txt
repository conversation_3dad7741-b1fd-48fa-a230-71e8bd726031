[2025-06-17 15:39:05.688 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 15:39:05.724 +10:00 INF] Initializing FastReport...
[2025-06-17 15:39:05.797 +10:00 INF] FastReport initialized successfully
[2025-06-17 15:39:06.249 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 15:39:07.613 +10:00 INF] Starting LEP Invoicer at "2025-06-17T15:39:07.6132664+10:00"
[2025-06-17 15:39:07.617 +10:00 INF] Initializing database service...
[2025-06-17 15:39:07.619 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 15:39:07.724 +10:00 INF] Database connection established successfully
[2025-06-17 15:39:07.726 +10:00 INF] Database service initialized successfully
[2025-06-17 15:39:07.732 +10:00 INF] Checking for pending work...
[2025-06-17 15:39:07.735 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 15:39:08.661 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 15:39:08.674 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 15:39:08.694 +10:00 INF] Found 0 credits to invoice
[2025-06-17 15:39:08.696 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 15:39:08.700 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 15:39:08.711 +10:00 INF] No pending work found
[2025-06-17 15:39:08.718 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 15:39:08.720 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 15:39:08.805 +10:00 INF] LEP Invoicer completed successfully in 1191ms. No work to process.
[2025-06-17 15:39:08.812 +10:00 INF] Database connection disposed
[2025-06-17 15:39:08.814 +10:00 INF] LEP Invoicer completed with result: 0
