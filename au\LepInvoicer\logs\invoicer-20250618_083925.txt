[2025-06-18 08:39:25.613 +10:00 INF] Starting LEP Invoicer application
[2025-06-18 08:39:25.645 +10:00 INF] Initializing FastReport...
[2025-06-18 08:39:25.719 +10:00 INF] FastReport initialized successfully
[2025-06-18 08:39:26.156 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-18 08:39:27.442 +10:00 INF] Starting LEP Invoicer at "2025-06-18T08:39:27.4425192+10:00"
[2025-06-18 08:39:27.446 +10:00 INF] Initializing database service...
[2025-06-18 08:39:27.449 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-18 08:39:27.547 +10:00 INF] Database connection established successfully
[2025-06-18 08:39:27.548 +10:00 INF] Database service initialized successfully
[2025-06-18 08:39:27.551 +10:00 INF] Checking for pending work...
[2025-06-18 08:39:27.554 +10:00 INF] Getting 500 orders to invoice
[2025-06-18 08:39:28.557 +10:00 INF] Found 0 orders to invoice (filtered 16 candidates)
[2025-06-18 08:39:28.560 +10:00 INF] Getting 100 credits to invoice
[2025-06-18 08:39:28.578 +10:00 INF] Found 0 credits to invoice
[2025-06-18 08:39:28.580 +10:00 INF] Getting 100 refunds to invoice
[2025-06-18 08:39:28.584 +10:00 INF] Found 0 refunds to invoice
[2025-06-18 08:39:28.585 +10:00 INF] No pending work found
[2025-06-18 08:39:28.590 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-18 08:39:28.591 +10:00 INF] Cleaning up invoicer logs...
[2025-06-18 08:39:28.666 +10:00 INF] LEP Invoicer completed successfully in 1224ms. No work to process.
[2025-06-18 08:39:28.673 +10:00 INF] Database connection disposed
[2025-06-18 08:39:28.675 +10:00 INF] LEP Invoicer completed with result: 0
