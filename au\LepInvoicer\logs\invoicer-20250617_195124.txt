[2025-06-17 19:51:24.622 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 19:51:24.652 +10:00 INF] Initializing FastReport...
[2025-06-17 19:51:24.726 +10:00 INF] FastReport initialized successfully
[2025-06-17 19:51:25.173 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 19:51:26.546 +10:00 INF] Starting LEP Invoicer at "2025-06-17T19:51:26.5463906+10:00"
[2025-06-17 19:51:26.550 +10:00 INF] Initializing database service...
[2025-06-17 19:51:26.553 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 19:51:26.657 +10:00 INF] Database connection established successfully
[2025-06-17 19:51:26.658 +10:00 INF] Database service initialized successfully
[2025-06-17 19:51:26.661 +10:00 INF] Checking for pending work...
[2025-06-17 19:51:26.665 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 19:51:27.547 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 19:51:27.550 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 19:51:27.563 +10:00 INF] Found 0 credits to invoice
[2025-06-17 19:51:27.565 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 19:51:27.571 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 19:51:27.572 +10:00 INF] No pending work found
[2025-06-17 19:51:27.574 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 19:51:27.576 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 19:51:27.660 +10:00 INF] LEP Invoicer completed successfully in 1114ms. No work to process.
[2025-06-17 19:51:27.667 +10:00 INF] Database connection disposed
[2025-06-17 19:51:27.670 +10:00 INF] LEP Invoicer completed with result: 0
