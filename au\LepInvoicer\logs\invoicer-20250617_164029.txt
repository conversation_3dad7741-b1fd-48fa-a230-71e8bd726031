[2025-06-17 16:40:29.591 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 16:40:29.619 +10:00 INF] Initializing FastReport...
[2025-06-17 16:40:29.689 +10:00 INF] FastReport initialized successfully
[2025-06-17 16:40:30.118 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 16:40:31.542 +10:00 INF] Starting LEP Invoicer at "2025-06-17T16:40:31.5418405+10:00"
[2025-06-17 16:40:31.545 +10:00 INF] Initializing database service...
[2025-06-17 16:40:31.548 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 16:40:31.726 +10:00 INF] Database connection established successfully
[2025-06-17 16:40:31.728 +10:00 INF] Database service initialized successfully
[2025-06-17 16:40:31.734 +10:00 INF] Checking for pending work...
[2025-06-17 16:40:31.740 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 16:40:32.996 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 16:40:33.009 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 16:40:33.025 +10:00 INF] Found 0 credits to invoice
[2025-06-17 16:40:33.027 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 16:40:33.033 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 16:40:33.036 +10:00 INF] No pending work found
[2025-06-17 16:40:33.037 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 16:40:33.039 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 16:40:33.169 +10:00 INF] LEP Invoicer completed successfully in 1627ms. No work to process.
[2025-06-17 16:40:33.176 +10:00 INF] Database connection disposed
[2025-06-17 16:40:33.177 +10:00 INF] LEP Invoicer completed with result: 0
