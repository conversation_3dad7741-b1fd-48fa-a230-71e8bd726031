[2025-06-18 10:46:43.235 +10:00 INF] Starting LEP Invoicer application
[2025-06-18 10:46:43.270 +10:00 INF] Initializing FastReport...
[2025-06-18 10:46:43.387 +10:00 INF] FastReport initialized successfully
[2025-06-18 10:46:44.133 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-18 10:46:46.116 +10:00 INF] Starting LEP Invoicer at "2025-06-18T10:46:46.1159816+10:00"
[2025-06-18 10:46:46.120 +10:00 INF] Initializing database service...
[2025-06-18 10:46:46.124 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-18 10:46:46.262 +10:00 INF] Database connection established successfully
[2025-06-18 10:46:46.263 +10:00 INF] Database service initialized successfully
[2025-06-18 10:46:46.266 +10:00 INF] Checking for pending work...
[2025-06-18 10:46:46.270 +10:00 INF] Getting 500 orders to invoice
[2025-06-18 10:46:46.575 +10:00 ERR] Failed to mark zero-priced orders as invoiced
NHibernate.QueryException: could not resolve property: PriceOfJobs of: lep.order.IOrder [.Select[lep.order.IOrder, lep, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null,System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e](.Where[lep.order.IOrder, lep, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null](.Where[lep.order.IOrder, lep, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null](.Where[lep.order.IOrder, lep, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null](.Where[lep.order.IOrder, lep, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null](.Where[lep.order.IOrder, lep, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null](NHibernate.Linq.NhQueryable`1[lep.order.IOrder], Quote((o, ) => (Not(p1<System.Collections.Generic.List`1[System.String]>.Contains(o.Customer.Name, )))), ), Quote((o, ) => (OrElse(String.op_Equality(o.Invoiced2, NULL), AndAlso(AndAlso(String.op_Inequality(o.Invoiced2, p3<System.String>), String.op_Inequality(o.Invoiced2, p4<System.String>)), String.op_Inequality(o.Invoiced2, p5<System.String>))))), ), Quote((o, ) => (AndAlso(DateTime.op_Inequality(o.FinishDate, NULL), NotEqual(o.FinishDate.Value.Year, p7<System.Int32>)))), ), Quote((o, ) => (DateTime.op_GreaterThanOrEqual(o.FinishDate.Value.Date, p8<System.DateTime>))), ), Quote((o, ) => (OrElse(Not(o.PriceOfJobs.HasValue), Decimal.op_Equality(o.PriceOfJobs.Value, p9<System.Decimal>)))), ), Quote((o, ) => (o.Id)), )]
   at NHibernate.Persister.Entity.AbstractPropertyMapping.ToType(String propertyName)
   at NHibernate.Persister.Entity.AbstractEntityPersister.ToType(String propertyName)
   at NHibernate.Hql.Ast.ANTLR.Tree.FromElementType.GetPropertyType(String propertyName, String propertyPath)
   at NHibernate.Hql.Ast.ANTLR.Tree.FromElement.GetPropertyType(String propertyName, String propertyPath)
   at NHibernate.Hql.Ast.ANTLR.Tree.DotNode.GetDataType()
   at NHibernate.Hql.Ast.ANTLR.Tree.DotNode.PrepareLhs()
   at NHibernate.Hql.Ast.ANTLR.Tree.DotNode.Resolve(Boolean generateJoin, Boolean implicitJoin, String classAlias, IASTNode parent)
   at NHibernate.Hql.Ast.ANTLR.Tree.FromReferenceNode.Resolve(Boolean generateJoin, Boolean implicitJoin, String classAlias)
   at NHibernate.Hql.Ast.ANTLR.Tree.FromReferenceNode.Resolve(Boolean generateJoin, Boolean implicitJoin)
   at NHibernate.Hql.Ast.ANTLR.HqlSqlWalker.Resolve(IASTNode node)
   at NHibernate.Hql.Ast.ANTLR.HqlSqlWalker.expr()
   at NHibernate.Hql.Ast.ANTLR.HqlSqlWalker.exprOrSubquery()
   at NHibernate.Hql.Ast.ANTLR.HqlSqlWalker.comparisonExpr()
   at NHibernate.Hql.Ast.ANTLR.HqlSqlWalker.logicalExpr()
   at NHibernate.Hql.Ast.ANTLR.HqlSqlWalker.logicalExpr()
   at NHibernate.Hql.Ast.ANTLR.HqlSqlWalker.logicalExpr()
   at NHibernate.Hql.Ast.ANTLR.HqlSqlWalker.logicalExpr()
   at NHibernate.Hql.Ast.ANTLR.HqlSqlWalker.whereClause()
   at NHibernate.Hql.Ast.ANTLR.HqlSqlWalker.unionedQuery()
   at NHibernate.Hql.Ast.ANTLR.HqlSqlWalker.query()
   at NHibernate.Hql.Ast.ANTLR.HqlSqlWalker.selectStatement()
   at NHibernate.Hql.Ast.ANTLR.HqlSqlWalker.statement()
   at NHibernate.Hql.Ast.ANTLR.HqlSqlTranslator.Translate()
   at NHibernate.Hql.Ast.ANTLR.QueryTranslatorImpl.Analyze(String collectionRole)
   at NHibernate.Hql.Ast.ANTLR.QueryTranslatorImpl.DoCompile(IDictionary`2 replacements, Boolean shallow, String collectionRole)
   at NHibernate.Hql.Ast.ANTLR.QueryTranslatorImpl.Compile(IDictionary`2 replacements, Boolean shallow)
   at NHibernate.Hql.Ast.ANTLR.ASTQueryTranslatorFactory.CreateQueryTranslators(IQueryExpression queryExpression, IASTNode ast, String queryIdentifier, String collectionRole, Boolean shallow, IDictionary`2 filters, ISessionFactoryImplementor factory)
   at NHibernate.Hql.Ast.ANTLR.ASTQueryTranslatorFactory.CreateQueryTranslators(IQueryExpression queryExpression, String collectionRole, Boolean shallow, IDictionary`2 filters, ISessionFactoryImplementor factory)
   at NHibernate.Engine.Query.QueryExpressionPlan.CreateTranslators(IQueryExpression queryExpression, String collectionRole, Boolean shallow, IDictionary`2 enabledFilters, ISessionFactoryImplementor factory)
   at NHibernate.Engine.Query.QueryExpressionPlan..ctor(IQueryExpression queryExpression, Boolean shallow, IDictionary`2 enabledFilters, ISessionFactoryImplementor factory)
   at NHibernate.Engine.Query.QueryPlanCache.GetHQLQueryPlan(IQueryExpression queryExpression, Boolean shallow, IDictionary`2 enabledFilters)
   at NHibernate.Impl.AbstractSessionImpl.GetHQLQueryPlan(IQueryExpression queryExpression, Boolean shallow)
   at NHibernate.Impl.AbstractSessionImpl.CreateQuery(IQueryExpression queryExpression)
   at NHibernate.Linq.DefaultQueryProvider.PrepareQuery(Expression expression, IQuery& query)
   at NHibernate.Linq.DefaultQueryProvider.ExecuteList[TResult](Expression expression)
   at NHibernate.Linq.NhQueryable`1.System.Collections.Generic.IEnumerable<T>.GetEnumerator()
   at System.Collections.Generic.List`1..ctor(IEnumerable`1 collection)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at LepInvoicer.Implementations.DatabaseService.MarkZeroPricedOrdersAsInvoiced() in C:\LepSF\au\LepInvoicer\Implementations\DatabaseService.cs:line 237
[2025-06-18 10:46:46.726 +10:00 ERR] Failed to get orders to invoice
NHibernate.QueryException: could not resolve property: PriceOfJobs of: lep.order.IOrder [.Select[lep.order.IOrder, lep, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null,System.Collections.Generic.KeyValuePair`2[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e](.Take[lep.order.IOrder, lep, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null](.OrderByDescending[lep.order.IOrder, lep, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null,System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e](.Where[lep.order.IOrder, lep, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null](.Where[lep.order.IOrder, lep, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null](.Where[lep.order.IOrder, lep, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null](.Where[lep.order.IOrder, lep, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null](.Where[lep.order.IOrder, lep, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null](NHibernate.Linq.NhQueryable`1[lep.order.IOrder], Quote((o, ) => (Not(p1<System.Collections.Generic.List`1[System.String]>.Contains(o.Customer.Name, )))), ), Quote((o, ) => (OrElse(String.op_Equality(o.Invoiced2, NULL), AndAlso(AndAlso(String.op_Inequality(o.Invoiced2, p3<System.String>), String.op_Inequality(o.Invoiced2, p4<System.String>)), String.op_Inequality(o.Invoiced2, p5<System.String>))))), ), Quote((o, ) => (AndAlso(DateTime.op_Inequality(o.FinishDate, NULL), NotEqual(o.FinishDate.Value.Year, p7<System.Int32>)))), ), Quote((o, ) => (DateTime.op_GreaterThanOrEqual(o.FinishDate.Value.Date, p8<System.DateTime>))), ), Quote((o, ) => (AndAlso(o.PriceOfJobs.HasValue, Decimal.op_GreaterThan(o.PriceOfJobs.Value, p9<System.Decimal>)))), ), Quote((o, ) => (o.Id)), ), p10<System.Int32>, ), Quote((o, ) => (new System.Collections.Generic.KeyValuePair`2[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e(o.Id, o.Customer.Username, ))), )]
   at NHibernate.Persister.Entity.AbstractPropertyMapping.ToType(String propertyName)
   at NHibernate.Persister.Entity.AbstractEntityPersister.ToType(String propertyName)
   at NHibernate.Hql.Ast.ANTLR.Tree.FromElementType.GetPropertyType(String propertyName, String propertyPath)
   at NHibernate.Hql.Ast.ANTLR.Tree.FromElement.GetPropertyType(String propertyName, String propertyPath)
   at NHibernate.Hql.Ast.ANTLR.Tree.DotNode.GetDataType()
   at NHibernate.Hql.Ast.ANTLR.Tree.DotNode.PrepareLhs()
   at NHibernate.Hql.Ast.ANTLR.Tree.DotNode.Resolve(Boolean generateJoin, Boolean implicitJoin, String classAlias, IASTNode parent)
   at NHibernate.Hql.Ast.ANTLR.Tree.FromReferenceNode.Resolve(Boolean generateJoin, Boolean implicitJoin, String classAlias)
   at NHibernate.Hql.Ast.ANTLR.Tree.FromReferenceNode.Resolve(Boolean generateJoin, Boolean implicitJoin)
   at NHibernate.Hql.Ast.ANTLR.HqlSqlWalker.Resolve(IASTNode node)
   at NHibernate.Hql.Ast.ANTLR.HqlSqlWalker.expr()
   at NHibernate.Hql.Ast.ANTLR.HqlSqlWalker.exprOrSubquery()
   at NHibernate.Hql.Ast.ANTLR.HqlSqlWalker.comparisonExpr()
   at NHibernate.Hql.Ast.ANTLR.HqlSqlWalker.logicalExpr()
   at NHibernate.Hql.Ast.ANTLR.HqlSqlWalker.logicalExpr()
   at NHibernate.Hql.Ast.ANTLR.HqlSqlWalker.logicalExpr()
   at NHibernate.Hql.Ast.ANTLR.HqlSqlWalker.whereClause()
   at NHibernate.Hql.Ast.ANTLR.HqlSqlWalker.unionedQuery()
   at NHibernate.Hql.Ast.ANTLR.HqlSqlWalker.query()
   at NHibernate.Hql.Ast.ANTLR.HqlSqlWalker.selectStatement()
   at NHibernate.Hql.Ast.ANTLR.HqlSqlWalker.statement()
   at NHibernate.Hql.Ast.ANTLR.HqlSqlTranslator.Translate()
   at NHibernate.Hql.Ast.ANTLR.QueryTranslatorImpl.Analyze(String collectionRole)
   at NHibernate.Hql.Ast.ANTLR.QueryTranslatorImpl.DoCompile(IDictionary`2 replacements, Boolean shallow, String collectionRole)
   at NHibernate.Hql.Ast.ANTLR.QueryTranslatorImpl.Compile(IDictionary`2 replacements, Boolean shallow)
   at NHibernate.Hql.Ast.ANTLR.ASTQueryTranslatorFactory.CreateQueryTranslators(IQueryExpression queryExpression, IASTNode ast, String queryIdentifier, String collectionRole, Boolean shallow, IDictionary`2 filters, ISessionFactoryImplementor factory)
   at NHibernate.Hql.Ast.ANTLR.ASTQueryTranslatorFactory.CreateQueryTranslators(IQueryExpression queryExpression, String collectionRole, Boolean shallow, IDictionary`2 filters, ISessionFactoryImplementor factory)
   at NHibernate.Engine.Query.QueryExpressionPlan.CreateTranslators(IQueryExpression queryExpression, String collectionRole, Boolean shallow, IDictionary`2 enabledFilters, ISessionFactoryImplementor factory)
   at NHibernate.Engine.Query.QueryExpressionPlan..ctor(IQueryExpression queryExpression, Boolean shallow, IDictionary`2 enabledFilters, ISessionFactoryImplementor factory)
   at NHibernate.Engine.Query.QueryPlanCache.GetHQLQueryPlan(IQueryExpression queryExpression, Boolean shallow, IDictionary`2 enabledFilters)
   at NHibernate.Impl.AbstractSessionImpl.GetHQLQueryPlan(IQueryExpression queryExpression, Boolean shallow)
   at NHibernate.Impl.AbstractSessionImpl.CreateQuery(IQueryExpression queryExpression)
   at NHibernate.Linq.DefaultQueryProvider.PrepareQuery(Expression expression, IQuery& query)
   at NHibernate.Linq.DefaultQueryProvider.ExecuteList[TResult](Expression expression)
   at NHibernate.Linq.NhQueryable`1.System.Collections.Generic.IEnumerable<T>.GetEnumerator()
   at System.Collections.Generic.List`1..ctor(IEnumerable`1 collection)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at LepInvoicer.Implementations.DatabaseService.GetOrdersToInvoice(Int32 batchSize) in C:\LepSF\au\LepInvoicer\Implementations\DatabaseService.cs:line 65
[2025-06-18 10:46:46.768 +10:00 ERR] LEP Invoicer failed after 652ms
NHibernate.QueryException: could not resolve property: PriceOfJobs of: lep.order.IOrder [.Select[lep.order.IOrder, lep, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null,System.Collections.Generic.KeyValuePair`2[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e](.Take[lep.order.IOrder, lep, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null](.OrderByDescending[lep.order.IOrder, lep, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null,System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e](.Where[lep.order.IOrder, lep, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null](.Where[lep.order.IOrder, lep, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null](.Where[lep.order.IOrder, lep, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null](.Where[lep.order.IOrder, lep, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null](.Where[lep.order.IOrder, lep, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null](NHibernate.Linq.NhQueryable`1[lep.order.IOrder], Quote((o, ) => (Not(p1<System.Collections.Generic.List`1[System.String]>.Contains(o.Customer.Name, )))), ), Quote((o, ) => (OrElse(String.op_Equality(o.Invoiced2, NULL), AndAlso(AndAlso(String.op_Inequality(o.Invoiced2, p3<System.String>), String.op_Inequality(o.Invoiced2, p4<System.String>)), String.op_Inequality(o.Invoiced2, p5<System.String>))))), ), Quote((o, ) => (AndAlso(DateTime.op_Inequality(o.FinishDate, NULL), NotEqual(o.FinishDate.Value.Year, p7<System.Int32>)))), ), Quote((o, ) => (DateTime.op_GreaterThanOrEqual(o.FinishDate.Value.Date, p8<System.DateTime>))), ), Quote((o, ) => (AndAlso(o.PriceOfJobs.HasValue, Decimal.op_GreaterThan(o.PriceOfJobs.Value, p9<System.Decimal>)))), ), Quote((o, ) => (o.Id)), ), p10<System.Int32>, ), Quote((o, ) => (new System.Collections.Generic.KeyValuePair`2[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e(o.Id, o.Customer.Username, ))), )]
   at NHibernate.Persister.Entity.AbstractPropertyMapping.ToType(String propertyName)
   at NHibernate.Persister.Entity.AbstractEntityPersister.ToType(String propertyName)
   at NHibernate.Hql.Ast.ANTLR.Tree.FromElementType.GetPropertyType(String propertyName, String propertyPath)
   at NHibernate.Hql.Ast.ANTLR.Tree.FromElement.GetPropertyType(String propertyName, String propertyPath)
   at NHibernate.Hql.Ast.ANTLR.Tree.DotNode.GetDataType()
   at NHibernate.Hql.Ast.ANTLR.Tree.DotNode.PrepareLhs()
   at NHibernate.Hql.Ast.ANTLR.Tree.DotNode.Resolve(Boolean generateJoin, Boolean implicitJoin, String classAlias, IASTNode parent)
   at NHibernate.Hql.Ast.ANTLR.Tree.FromReferenceNode.Resolve(Boolean generateJoin, Boolean implicitJoin, String classAlias)
   at NHibernate.Hql.Ast.ANTLR.Tree.FromReferenceNode.Resolve(Boolean generateJoin, Boolean implicitJoin)
   at NHibernate.Hql.Ast.ANTLR.HqlSqlWalker.Resolve(IASTNode node)
   at NHibernate.Hql.Ast.ANTLR.HqlSqlWalker.expr()
   at NHibernate.Hql.Ast.ANTLR.HqlSqlWalker.exprOrSubquery()
   at NHibernate.Hql.Ast.ANTLR.HqlSqlWalker.comparisonExpr()
   at NHibernate.Hql.Ast.ANTLR.HqlSqlWalker.logicalExpr()
   at NHibernate.Hql.Ast.ANTLR.HqlSqlWalker.logicalExpr()
   at NHibernate.Hql.Ast.ANTLR.HqlSqlWalker.logicalExpr()
   at NHibernate.Hql.Ast.ANTLR.HqlSqlWalker.whereClause()
   at NHibernate.Hql.Ast.ANTLR.HqlSqlWalker.unionedQuery()
   at NHibernate.Hql.Ast.ANTLR.HqlSqlWalker.query()
   at NHibernate.Hql.Ast.ANTLR.HqlSqlWalker.selectStatement()
   at NHibernate.Hql.Ast.ANTLR.HqlSqlWalker.statement()
   at NHibernate.Hql.Ast.ANTLR.HqlSqlTranslator.Translate()
   at NHibernate.Hql.Ast.ANTLR.QueryTranslatorImpl.Analyze(String collectionRole)
   at NHibernate.Hql.Ast.ANTLR.QueryTranslatorImpl.DoCompile(IDictionary`2 replacements, Boolean shallow, String collectionRole)
   at NHibernate.Hql.Ast.ANTLR.QueryTranslatorImpl.Compile(IDictionary`2 replacements, Boolean shallow)
   at NHibernate.Hql.Ast.ANTLR.ASTQueryTranslatorFactory.CreateQueryTranslators(IQueryExpression queryExpression, IASTNode ast, String queryIdentifier, String collectionRole, Boolean shallow, IDictionary`2 filters, ISessionFactoryImplementor factory)
   at NHibernate.Hql.Ast.ANTLR.ASTQueryTranslatorFactory.CreateQueryTranslators(IQueryExpression queryExpression, String collectionRole, Boolean shallow, IDictionary`2 filters, ISessionFactoryImplementor factory)
   at NHibernate.Engine.Query.QueryExpressionPlan.CreateTranslators(IQueryExpression queryExpression, String collectionRole, Boolean shallow, IDictionary`2 enabledFilters, ISessionFactoryImplementor factory)
   at NHibernate.Engine.Query.QueryExpressionPlan..ctor(IQueryExpression queryExpression, Boolean shallow, IDictionary`2 enabledFilters, ISessionFactoryImplementor factory)
   at NHibernate.Engine.Query.QueryPlanCache.GetHQLQueryPlan(IQueryExpression queryExpression, Boolean shallow, IDictionary`2 enabledFilters)
   at NHibernate.Impl.AbstractSessionImpl.GetHQLQueryPlan(IQueryExpression queryExpression, Boolean shallow)
   at NHibernate.Impl.AbstractSessionImpl.CreateQuery(IQueryExpression queryExpression)
   at NHibernate.Linq.DefaultQueryProvider.PrepareQuery(Expression expression, IQuery& query)
   at NHibernate.Linq.DefaultQueryProvider.ExecuteList[TResult](Expression expression)
   at NHibernate.Linq.NhQueryable`1.System.Collections.Generic.IEnumerable<T>.GetEnumerator()
   at System.Collections.Generic.List`1..ctor(IEnumerable`1 collection)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at LepInvoicer.Implementations.DatabaseService.GetOrdersToInvoice(Int32 batchSize) in C:\LepSF\au\LepInvoicer\Implementations\DatabaseService.cs:line 65
   at LepInvoicer.Implementations.InvoicerService.CheckForPendingWork() in C:\LepSF\au\LepInvoicer\Implementations\InvoicerService.cs:line 116
   at LepInvoicer.Implementations.InvoicerService.RunInvoicer() in C:\LepSF\au\LepInvoicer\Implementations\InvoicerService.cs:line 55
[2025-06-18 10:46:46.824 +10:00 INF] Database connection disposed
[2025-06-18 10:46:46.827 +10:00 INF] LEP Invoicer completed with result: 1
