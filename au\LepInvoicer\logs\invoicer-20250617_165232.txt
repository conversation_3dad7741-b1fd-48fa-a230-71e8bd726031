[2025-06-17 16:52:32.624 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 16:52:32.656 +10:00 INF] Initializing FastReport...
[2025-06-17 16:52:32.751 +10:00 INF] FastReport initialized successfully
[2025-06-17 16:52:33.140 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 16:52:34.446 +10:00 INF] Starting LEP Invoicer at "2025-06-17T16:52:34.4464679+10:00"
[2025-06-17 16:52:34.450 +10:00 INF] Initializing database service...
[2025-06-17 16:52:34.454 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 16:52:34.562 +10:00 INF] Database connection established successfully
[2025-06-17 16:52:34.563 +10:00 INF] Database service initialized successfully
[2025-06-17 16:52:34.567 +10:00 INF] Checking for pending work...
[2025-06-17 16:52:34.570 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 16:52:35.495 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 16:52:35.497 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 16:52:35.509 +10:00 INF] Found 0 credits to invoice
[2025-06-17 16:52:35.511 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 16:52:35.519 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 16:52:35.520 +10:00 INF] No pending work found
[2025-06-17 16:52:35.522 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 16:52:35.523 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 16:52:35.595 +10:00 INF] LEP Invoicer completed successfully in 1149ms. No work to process.
[2025-06-17 16:52:35.613 +10:00 INF] Database connection disposed
[2025-06-17 16:52:35.617 +10:00 INF] LEP Invoicer completed with result: 0
