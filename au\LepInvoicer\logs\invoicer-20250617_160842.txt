[2025-06-17 16:08:42.650 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 16:08:42.679 +10:00 INF] Initializing FastReport...
[2025-06-17 16:08:42.754 +10:00 INF] FastReport initialized successfully
[2025-06-17 16:08:43.283 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 16:08:44.630 +10:00 INF] Starting LEP Invoicer at "2025-06-17T16:08:44.6301128+10:00"
[2025-06-17 16:08:44.634 +10:00 INF] Initializing database service...
[2025-06-17 16:08:44.637 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 16:08:44.762 +10:00 INF] Database connection established successfully
[2025-06-17 16:08:44.763 +10:00 INF] Database service initialized successfully
[2025-06-17 16:08:44.766 +10:00 INF] Checking for pending work...
[2025-06-17 16:08:44.769 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 16:08:45.746 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 16:08:45.748 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 16:08:45.770 +10:00 INF] Found 0 credits to invoice
[2025-06-17 16:08:45.776 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 16:08:45.780 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 16:08:45.782 +10:00 INF] No pending work found
[2025-06-17 16:08:45.784 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 16:08:45.785 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 16:08:45.898 +10:00 INF] LEP Invoicer completed successfully in 1268ms. No work to process.
[2025-06-17 16:08:45.905 +10:00 INF] Database connection disposed
[2025-06-17 16:08:45.909 +10:00 INF] LEP Invoicer completed with result: 0
