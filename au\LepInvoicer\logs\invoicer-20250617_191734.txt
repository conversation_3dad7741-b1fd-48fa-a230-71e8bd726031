[2025-06-17 19:17:34.617 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 19:17:34.646 +10:00 INF] Initializing FastReport...
[2025-06-17 19:17:34.718 +10:00 INF] FastReport initialized successfully
[2025-06-17 19:17:35.130 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 19:17:36.403 +10:00 INF] Starting LEP Invoicer at "2025-06-17T19:17:36.4034127+10:00"
[2025-06-17 19:17:36.407 +10:00 INF] Initializing database service...
[2025-06-17 19:17:36.410 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 19:17:36.508 +10:00 INF] Database connection established successfully
[2025-06-17 19:17:36.509 +10:00 INF] Database service initialized successfully
[2025-06-17 19:17:36.513 +10:00 INF] Checking for pending work...
[2025-06-17 19:17:36.516 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 19:17:37.392 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 19:17:37.395 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 19:17:37.407 +10:00 INF] Found 0 credits to invoice
[2025-06-17 19:17:37.409 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 19:17:37.412 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 19:17:37.414 +10:00 INF] No pending work found
[2025-06-17 19:17:37.415 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 19:17:37.416 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 19:17:37.495 +10:00 INF] LEP Invoicer completed successfully in 1092ms. No work to process.
[2025-06-17 19:17:37.502 +10:00 INF] Database connection disposed
[2025-06-17 19:17:37.503 +10:00 INF] LEP Invoicer completed with result: 0
