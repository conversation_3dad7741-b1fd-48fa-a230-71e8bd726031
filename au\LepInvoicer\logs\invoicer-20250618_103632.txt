[2025-06-18 10:36:32.570 +10:00 INF] Starting LEP Invoicer application
[2025-06-18 10:36:32.602 +10:00 INF] Initializing FastReport...
[2025-06-18 10:36:32.680 +10:00 INF] FastReport initialized successfully
[2025-06-18 10:36:33.074 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-18 10:36:34.353 +10:00 INF] Starting LEP Invoicer at "2025-06-18T10:36:34.3529241+10:00"
[2025-06-18 10:36:34.356 +10:00 INF] Initializing database service...
[2025-06-18 10:36:34.359 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-18 10:36:34.457 +10:00 INF] Database connection established successfully
[2025-06-18 10:36:34.458 +10:00 INF] Database service initialized successfully
[2025-06-18 10:36:34.460 +10:00 INF] Checking for pending work...
[2025-06-18 10:36:34.463 +10:00 INF] Getting 500 orders to invoice
[2025-06-18 10:36:35.302 +10:00 INF] Found 0 orders to invoice (filtered 17 candidates)
[2025-06-18 10:36:35.306 +10:00 INF] Getting 100 credits to invoice
[2025-06-18 10:36:35.319 +10:00 INF] Found 0 credits to invoice
[2025-06-18 10:36:35.321 +10:00 INF] Getting 100 refunds to invoice
[2025-06-18 10:36:35.325 +10:00 INF] Found 0 refunds to invoice
[2025-06-18 10:36:35.327 +10:00 INF] No pending work found
[2025-06-18 10:36:35.335 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-18 10:36:35.336 +10:00 INF] Cleaning up invoicer logs...
[2025-06-18 10:36:35.410 +10:00 INF] LEP Invoicer completed successfully in 1057ms. No work to process.
[2025-06-18 10:36:35.419 +10:00 INF] Database connection disposed
[2025-06-18 10:36:35.422 +10:00 INF] LEP Invoicer completed with result: 0
