[2025-06-18 09:52:04.723 +10:00 INF] Starting LEP Invoicer application
[2025-06-18 09:52:04.754 +10:00 INF] Initializing FastReport...
[2025-06-18 09:52:04.830 +10:00 INF] FastReport initialized successfully
[2025-06-18 09:52:05.270 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-18 09:52:06.576 +10:00 INF] Starting LEP Invoicer at "2025-06-18T09:52:06.5757083+10:00"
[2025-06-18 09:52:06.584 +10:00 INF] Initializing database service...
[2025-06-18 09:52:06.595 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-18 09:52:06.713 +10:00 INF] Database connection established successfully
[2025-06-18 09:52:06.714 +10:00 INF] Database service initialized successfully
[2025-06-18 09:52:06.716 +10:00 INF] Checking for pending work...
[2025-06-18 09:52:06.720 +10:00 INF] Getting 500 orders to invoice
[2025-06-18 09:52:07.694 +10:00 INF] Found 1 orders to invoice (filtered 17 candidates)
[2025-06-18 09:52:07.697 +10:00 INF] Getting 100 credits to invoice
[2025-06-18 09:52:07.713 +10:00 INF] Found 0 credits to invoice
[2025-06-18 09:52:07.716 +10:00 INF] Getting 100 refunds to invoice
[2025-06-18 09:52:07.720 +10:00 INF] Found 0 refunds to invoice
[2025-06-18 09:52:07.721 +10:00 INF] Found pending work: 1 orders
[2025-06-18 09:52:07.723 +10:00 INF] Initializing MYOB and other services...
[2025-06-18 09:52:07.727 +10:00 INF] Initializing MYOB service...
[2025-06-18 09:52:07.729 +10:00 INF] Initializing MYOB with OAuth authentication
[2025-06-18 09:52:07.732 +10:00 INF] Starting OAuth authentication flow
[2025-06-18 09:52:07.742 +10:00 INF] Using existing OAuth tokens
[2025-06-18 09:52:07.745 +10:00 INF] Initializing MYOB services with OAuth keystore
[2025-06-18 09:52:07.748 +10:00 INF] MYOB services initialized successfully
[2025-06-18 09:52:07.749 +10:00 INF] OAuth keystore set for API calls
[2025-06-18 09:52:07.750 +10:00 INF] Getting company files from MYOB
[2025-06-18 09:52:18.900 +10:00 INF] MYOB service initialized with OAuth credentials. Using company file: LEP Colour Printers Pty Ltd
[2025-06-18 09:52:22.948 +10:00 INF] MYOB service initialized successfully with company file: LEP Colour Printers Pty Ltd
[2025-06-18 09:52:22.950 +10:00 INF] All services initialized successfully
[2025-06-18 09:52:22.953 +10:00 INF] Processing order invoices...
[2025-06-18 09:52:22.954 +10:00 INF] Getting 500 orders to invoice
[2025-06-18 09:52:23.277 +10:00 INF] Found 1 orders to invoice (filtered 17 candidates)
[2025-06-18 09:52:23.279 +10:00 INF] Found 1 orders to process
[2025-06-18 09:52:23.281 +10:00 INF] Getting order 1418288
[2025-06-18 09:52:23.312 +10:00 INF] Processing order 1418288 with 3 jobs, total: ¤114.75
[2025-06-18 09:52:23.318 +10:00 INF] Creating order invoice for order 1418288
[2025-06-18 09:52:24.820 +10:00 INF] Successfully created MYOB invoice O1418288 for order 1418288
[2025-06-18 09:52:24.822 +10:00 INF] Successfully created MYOB invoice for order 1418288
[2025-06-18 09:52:24.826 +10:00 INF] Generating PDF invoice for order 1418288 at \\dfs01\resource\invoices\2025/Jun/18\O1418288.pdf
[2025-06-18 09:52:26.825 +10:00 INF] Successfully generated PDF for order 1418288
[2025-06-18 09:52:26.853 +10:00 WRN] No email address found for order 1418288
[2025-06-18 09:52:26.858 +10:00 INF] Successfully processed order 1418288 (MYOB + PDF)
[2025-06-18 09:52:26.862 +10:00 INF] Order processing completed. Processed: 1, Success: 1, Failed: 0
[2025-06-18 09:52:26.867 +10:00 INF] Processing credit invoices...
[2025-06-18 09:52:26.868 +10:00 INF] Getting 100 credits to invoice
[2025-06-18 09:52:26.871 +10:00 INF] Found 0 credits to invoice
[2025-06-18 09:52:26.877 +10:00 INF] Processing refund invoices...
[2025-06-18 09:52:26.878 +10:00 INF] Getting 100 refunds to invoice
[2025-06-18 09:52:26.886 +10:00 INF] Found 0 refunds to invoice
[2025-06-18 09:52:26.888 +10:00 INF] Cleaning up invoicer logs...
[2025-06-18 09:52:26.981 +10:00 INF] LEP Invoicer completed successfully in 20405ms. Orders: 1, Credits: 0, Refunds: 0
[2025-06-18 09:52:26.990 +10:00 INF] Database connection disposed
[2025-06-18 09:52:26.999 +10:00 INF] LEP Invoicer completed with result: 0
