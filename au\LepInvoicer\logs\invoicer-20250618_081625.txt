[2025-06-18 08:16:25.493 +10:00 INF] Starting LEP Invoicer application
[2025-06-18 08:16:25.523 +10:00 INF] Initializing FastReport...
[2025-06-18 08:16:25.592 +10:00 INF] FastReport initialized successfully
[2025-06-18 08:16:26.013 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-18 08:16:27.261 +10:00 INF] Starting LEP Invoicer at "2025-06-18T08:16:27.2609593+10:00"
[2025-06-18 08:16:27.264 +10:00 INF] Initializing database service...
[2025-06-18 08:16:27.267 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-18 08:16:27.373 +10:00 INF] Database connection established successfully
[2025-06-18 08:16:27.375 +10:00 INF] Database service initialized successfully
[2025-06-18 08:16:27.377 +10:00 INF] Checking for pending work...
[2025-06-18 08:16:27.380 +10:00 INF] Getting 500 orders to invoice
[2025-06-18 08:16:28.306 +10:00 INF] Found 0 orders to invoice (filtered 16 candidates)
[2025-06-18 08:16:28.308 +10:00 INF] Getting 100 credits to invoice
[2025-06-18 08:16:28.321 +10:00 INF] Found 0 credits to invoice
[2025-06-18 08:16:28.322 +10:00 INF] Getting 100 refunds to invoice
[2025-06-18 08:16:28.327 +10:00 INF] Found 0 refunds to invoice
[2025-06-18 08:16:28.328 +10:00 INF] No pending work found
[2025-06-18 08:16:28.330 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-18 08:16:28.331 +10:00 INF] Cleaning up invoicer logs...
[2025-06-18 08:16:28.410 +10:00 INF] LEP Invoicer completed successfully in 1149ms. No work to process.
[2025-06-18 08:16:28.417 +10:00 INF] Database connection disposed
[2025-06-18 08:16:28.419 +10:00 INF] LEP Invoicer completed with result: 0
