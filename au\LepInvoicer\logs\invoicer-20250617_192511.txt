[2025-06-17 19:25:11.640 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 19:25:11.676 +10:00 INF] Initializing FastReport...
[2025-06-17 19:25:11.778 +10:00 INF] FastReport initialized successfully
[2025-06-17 19:25:12.155 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 19:25:13.499 +10:00 INF] Starting LEP Invoicer at "2025-06-17T19:25:13.4989266+10:00"
[2025-06-17 19:25:13.502 +10:00 INF] Initializing database service...
[2025-06-17 19:25:13.505 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 19:25:13.602 +10:00 INF] Database connection established successfully
[2025-06-17 19:25:13.603 +10:00 INF] Database service initialized successfully
[2025-06-17 19:25:13.606 +10:00 INF] Checking for pending work...
[2025-06-17 19:25:13.609 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 19:25:14.513 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 19:25:14.525 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 19:25:14.539 +10:00 INF] Found 0 credits to invoice
[2025-06-17 19:25:14.541 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 19:25:14.545 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 19:25:14.552 +10:00 INF] No pending work found
[2025-06-17 19:25:14.553 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 19:25:14.555 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 19:25:14.669 +10:00 INF] LEP Invoicer completed successfully in 1170ms. No work to process.
[2025-06-17 19:25:14.677 +10:00 INF] Database connection disposed
[2025-06-17 19:25:14.681 +10:00 INF] LEP Invoicer completed with result: 0
