[2025-06-17 19:13:13.775 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 19:13:13.803 +10:00 INF] Initializing FastReport...
[2025-06-17 19:13:13.877 +10:00 INF] FastReport initialized successfully
[2025-06-17 19:13:14.310 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 19:13:15.581 +10:00 INF] Starting LEP Invoicer at "2025-06-17T19:13:15.5811453+10:00"
[2025-06-17 19:13:15.585 +10:00 INF] Initializing database service...
[2025-06-17 19:13:15.587 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 19:13:15.688 +10:00 INF] Database connection established successfully
[2025-06-17 19:13:15.690 +10:00 INF] Database service initialized successfully
[2025-06-17 19:13:15.692 +10:00 INF] Checking for pending work...
[2025-06-17 19:13:15.695 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 19:13:16.602 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 19:13:16.605 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 19:13:16.617 +10:00 INF] Found 0 credits to invoice
[2025-06-17 19:13:16.619 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 19:13:16.622 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 19:13:16.631 +10:00 INF] No pending work found
[2025-06-17 19:13:16.634 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 19:13:16.635 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 19:13:16.732 +10:00 INF] LEP Invoicer completed successfully in 1150ms. No work to process.
[2025-06-17 19:13:16.738 +10:00 INF] Database connection disposed
[2025-06-17 19:13:16.740 +10:00 INF] LEP Invoicer completed with result: 0
