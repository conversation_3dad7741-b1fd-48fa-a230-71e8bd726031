[2025-06-17 20:06:42.621 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 20:06:42.652 +10:00 INF] Initializing FastReport...
[2025-06-17 20:06:42.734 +10:00 INF] FastReport initialized successfully
[2025-06-17 20:06:43.231 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 20:06:44.642 +10:00 INF] Starting LEP Invoicer at "2025-06-17T20:06:44.6418219+10:00"
[2025-06-17 20:06:44.645 +10:00 INF] Initializing database service...
[2025-06-17 20:06:44.648 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 20:06:44.747 +10:00 INF] Database connection established successfully
[2025-06-17 20:06:44.748 +10:00 INF] Database service initialized successfully
[2025-06-17 20:06:44.752 +10:00 INF] Checking for pending work...
[2025-06-17 20:06:44.755 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 20:06:45.711 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 20:06:45.714 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 20:06:45.726 +10:00 INF] Found 0 credits to invoice
[2025-06-17 20:06:45.729 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 20:06:45.734 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 20:06:45.736 +10:00 INF] No pending work found
[2025-06-17 20:06:45.737 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 20:06:45.739 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 20:06:45.824 +10:00 INF] LEP Invoicer completed successfully in 1182ms. No work to process.
[2025-06-17 20:06:45.835 +10:00 INF] Database connection disposed
[2025-06-17 20:06:45.837 +10:00 INF] LEP Invoicer completed with result: 0
