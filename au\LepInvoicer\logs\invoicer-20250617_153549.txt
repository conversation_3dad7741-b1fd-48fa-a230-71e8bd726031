[2025-06-17 15:35:49.527 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 15:35:49.561 +10:00 INF] Initializing FastReport...
[2025-06-17 15:35:49.639 +10:00 INF] FastReport initialized successfully
[2025-06-17 15:35:50.060 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 15:35:51.340 +10:00 INF] Starting LEP Invoicer at "2025-06-17T15:35:51.3397489+10:00"
[2025-06-17 15:35:51.355 +10:00 INF] Initializing database service...
[2025-06-17 15:35:51.359 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 15:35:51.469 +10:00 INF] Database connection established successfully
[2025-06-17 15:35:51.470 +10:00 INF] Database service initialized successfully
[2025-06-17 15:35:51.473 +10:00 INF] Checking for pending work...
[2025-06-17 15:35:51.476 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 15:35:52.396 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 15:35:52.407 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 15:35:52.420 +10:00 INF] Found 0 credits to invoice
[2025-06-17 15:35:52.422 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 15:35:52.426 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 15:35:52.428 +10:00 INF] No pending work found
[2025-06-17 15:35:52.429 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 15:35:52.431 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 15:35:52.512 +10:00 INF] LEP Invoicer completed successfully in 1172ms. No work to process.
[2025-06-17 15:35:52.518 +10:00 INF] Database connection disposed
[2025-06-17 15:35:52.520 +10:00 INF] LEP Invoicer completed with result: 0
