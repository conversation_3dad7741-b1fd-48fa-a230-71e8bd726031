[2025-06-17 17:04:31.563 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 17:04:31.590 +10:00 INF] Initializing FastReport...
[2025-06-17 17:04:31.660 +10:00 INF] FastReport initialized successfully
[2025-06-17 17:04:32.079 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 17:04:33.274 +10:00 INF] Starting LEP Invoicer at "2025-06-17T17:04:33.2737816+10:00"
[2025-06-17 17:04:33.278 +10:00 INF] Initializing database service...
[2025-06-17 17:04:33.281 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 17:04:33.385 +10:00 INF] Database connection established successfully
[2025-06-17 17:04:33.386 +10:00 INF] Database service initialized successfully
[2025-06-17 17:04:33.389 +10:00 INF] Checking for pending work...
[2025-06-17 17:04:33.391 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 17:04:34.267 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 17:04:34.274 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 17:04:34.297 +10:00 INF] Found 0 credits to invoice
[2025-06-17 17:04:34.299 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 17:04:34.306 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 17:04:34.309 +10:00 INF] No pending work found
[2025-06-17 17:04:34.311 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 17:04:34.313 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 17:04:34.392 +10:00 INF] LEP Invoicer completed successfully in 1118ms. No work to process.
[2025-06-17 17:04:34.399 +10:00 INF] Database connection disposed
[2025-06-17 17:04:34.400 +10:00 INF] LEP Invoicer completed with result: 0
