[2025-06-10 19:58:10.857 +10:00 INF] Starting LEP Invoicer application
[2025-06-10 19:58:11.771 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-10 19:58:14.049 +10:00 INF] Starting LEP Invoicer at "2025-06-10T19:58:14.0487660+10:00"
[2025-06-10 19:58:14.054 +10:00 INF] Initializing database service...
[2025-06-10 19:58:14.059 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-10 19:58:14.229 +10:00 INF] Database connection established successfully
[2025-06-10 19:58:14.231 +10:00 INF] Database service initialized successfully
[2025-06-10 19:58:14.234 +10:00 INF] Checking for pending work...
[2025-06-10 19:58:14.240 +10:00 INF] Getting 500 orders to invoice
[2025-06-10 19:58:15.626 +10:00 INF] Found 0 orders to invoice (filtered 2 candidates)
[2025-06-10 19:58:15.633 +10:00 INF] Getting 100 credits to invoice
[2025-06-10 19:58:15.689 +10:00 INF] Found 0 credits to invoice
[2025-06-10 19:58:15.694 +10:00 INF] Getting 100 refunds to invoice
[2025-06-10 19:58:15.709 +10:00 INF] Found 0 refunds to invoice
[2025-06-10 19:58:15.712 +10:00 INF] No pending work found
[2025-06-10 19:58:15.714 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-10 19:58:15.716 +10:00 INF] Cleaning up invoicer logs...
[2025-06-10 19:58:15.797 +10:00 INF] LEP Invoicer completed successfully in 1749ms. No work to process.
[2025-06-10 19:58:15.809 +10:00 INF] Database connection disposed
[2025-06-10 19:58:15.812 +10:00 INF] LEP Invoicer completed with result: 0
