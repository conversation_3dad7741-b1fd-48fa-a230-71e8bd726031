[2025-06-17 18:42:39.602 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 18:42:39.630 +10:00 INF] Initializing FastReport...
[2025-06-17 18:42:39.703 +10:00 INF] FastReport initialized successfully
[2025-06-17 18:42:40.112 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 18:42:41.388 +10:00 INF] Starting LEP Invoicer at "2025-06-17T18:42:41.3877513+10:00"
[2025-06-17 18:42:41.391 +10:00 INF] Initializing database service...
[2025-06-17 18:42:41.394 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 18:42:41.497 +10:00 INF] Database connection established successfully
[2025-06-17 18:42:41.499 +10:00 INF] Database service initialized successfully
[2025-06-17 18:42:41.502 +10:00 INF] Checking for pending work...
[2025-06-17 18:42:41.508 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 18:42:42.371 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 18:42:42.373 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 18:42:42.390 +10:00 INF] Found 0 credits to invoice
[2025-06-17 18:42:42.392 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 18:42:42.408 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 18:42:42.413 +10:00 INF] No pending work found
[2025-06-17 18:42:42.415 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 18:42:42.416 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 18:42:42.499 +10:00 INF] LEP Invoicer completed successfully in 1111ms. No work to process.
[2025-06-17 18:42:42.506 +10:00 INF] Database connection disposed
[2025-06-17 18:42:42.514 +10:00 INF] LEP Invoicer completed with result: 0
