[2025-06-17 17:37:10.783 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 17:37:10.814 +10:00 INF] Initializing FastReport...
[2025-06-17 17:37:10.887 +10:00 INF] FastReport initialized successfully
[2025-06-17 17:37:11.348 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 17:37:12.738 +10:00 INF] Starting LEP Invoicer at "2025-06-17T17:37:12.7377829+10:00"
[2025-06-17 17:37:12.741 +10:00 INF] Initializing database service...
[2025-06-17 17:37:12.744 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 17:37:12.841 +10:00 INF] Database connection established successfully
[2025-06-17 17:37:12.842 +10:00 INF] Database service initialized successfully
[2025-06-17 17:37:12.845 +10:00 INF] Checking for pending work...
[2025-06-17 17:37:12.848 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 17:37:13.842 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 17:37:13.845 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 17:37:13.860 +10:00 INF] Found 0 credits to invoice
[2025-06-17 17:37:13.865 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 17:37:13.869 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 17:37:13.870 +10:00 INF] No pending work found
[2025-06-17 17:37:13.873 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 17:37:13.874 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 17:37:13.955 +10:00 INF] LEP Invoicer completed successfully in 1217ms. No work to process.
[2025-06-17 17:37:13.967 +10:00 INF] Database connection disposed
[2025-06-17 17:37:13.969 +10:00 INF] LEP Invoicer completed with result: 0
