[2025-06-18 08:29:56.657 +10:00 INF] Starting LEP Invoicer application
[2025-06-18 08:29:56.691 +10:00 INF] Initializing FastReport...
[2025-06-18 08:29:56.782 +10:00 INF] FastReport initialized successfully
[2025-06-18 08:29:57.210 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-18 08:29:58.550 +10:00 INF] Starting LEP Invoicer at "2025-06-18T08:29:58.5503916+10:00"
[2025-06-18 08:29:58.554 +10:00 INF] Initializing database service...
[2025-06-18 08:29:58.556 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-18 08:29:58.655 +10:00 INF] Database connection established successfully
[2025-06-18 08:29:58.657 +10:00 INF] Database service initialized successfully
[2025-06-18 08:29:58.660 +10:00 INF] Checking for pending work...
[2025-06-18 08:29:58.663 +10:00 INF] Getting 500 orders to invoice
[2025-06-18 08:29:59.559 +10:00 INF] Found 0 orders to invoice (filtered 16 candidates)
[2025-06-18 08:29:59.562 +10:00 INF] Getting 100 credits to invoice
[2025-06-18 08:29:59.576 +10:00 INF] Found 0 credits to invoice
[2025-06-18 08:29:59.577 +10:00 INF] Getting 100 refunds to invoice
[2025-06-18 08:29:59.583 +10:00 INF] Found 0 refunds to invoice
[2025-06-18 08:29:59.584 +10:00 INF] No pending work found
[2025-06-18 08:29:59.586 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-18 08:29:59.587 +10:00 INF] Cleaning up invoicer logs...
[2025-06-18 08:29:59.663 +10:00 INF] LEP Invoicer completed successfully in 1113ms. No work to process.
[2025-06-18 08:29:59.670 +10:00 INF] Database connection disposed
[2025-06-18 08:29:59.672 +10:00 INF] LEP Invoicer completed with result: 0
