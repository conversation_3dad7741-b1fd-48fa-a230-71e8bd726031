#region

using lep.address;
using lep.address.impl;
using lep.contact;
using lep.contact.impl;
using lep.courier;
using lep.printPortal;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;

#endregion

namespace lep.user.impl
{
	[Serializable]
	[DebuggerDisplay("{Id} -{Username}")]
	public class CustomerUser : BaseUser, ICustomerUser
	{
		public CustomerUser()
		{
			ProductPriceCode = "P0";
			FreightPriceCode = "F0";
			AccountEmail = "";
			OtherEmail = "";
			AllowedSamples = true;
		}

		#region ICustomerUser Members
		public virtual string CustomerNr => $"{Id:D6}";
		public virtual Guid Gid { get; set; }
		public virtual string Name { get; set; }
		public virtual string BusinessType { get; set; }
		public virtual string ABN { get; set; }
		public virtual string MYOB { get; set; }
		public virtual bool HasSystemAccess { get; set; } = false;
		public virtual bool PostalIsBilling { get; set; }
		public virtual NotificationType NotificationType { get; set; }
		public virtual IPhysicalAddress BillingAddress { get; set; } = new PhysicalAddress();
		public virtual IPhysicalAddress PostalAddress { get; set; } = new PhysicalAddress();

		//public virtual ListOfDeliveryDetails FavouriteDeliveryDetails { get; set; } = new ListOfDeliveryDetails();
		public virtual  IList<DeliveryDetails> FavouriteDeliveryDetails { get; set; } = new List<DeliveryDetails>();
	
		public virtual IContact Contact1 => Contacts?.FirstOrDefault() ?? new Contact();
		public virtual ListOfContacts Contacts { get; set; } = new ListOfContacts();
		
		// only to make searches work
		public virtual string ContactsJsonStr { get; set; }

		public virtual DateTime? LastOrderDate { get; set; }

		public DateTime? LastLoginDate { get; set; }
		public DateTime? LastOrderDispatchDate { get; set; }

		public virtual int CreditLimit { get; set; } = 0;
		public virtual string MYOBBalance { get; set; }
		public virtual decimal? MYOBPastDue { get; set; }
		public virtual PaymentTermsOptions PaymentTerms { get; set; }
		public virtual CourierType PreferredCourier { get; set; }
		public virtual bool AllowedSamples { get; set; }
		public virtual bool SendSamples { get; set; }

		public virtual string AccountEmail { get; set; }
		public virtual string OtherEmail { get; set; }
		public virtual bool IsChargedGST { get; set; }
		public virtual bool IsEnrolledInIVR { get; set; }
		public virtual string Notes { get; set; }
		public virtual string ProductionNotes { get; set; }
		public virtual SiteLocation SiteLocation { get; set; }
		public virtual string ProductPriceCode { get; set; }
		public virtual string FreightPriceCode { get; set; }

		// view only fields
		public virtual decimal LEPOnlineBalance { get; set; }

		public virtual decimal AvaiableBalance { get; set; }
		public virtual decimal MAT { get; set; }
		public virtual decimal MAT3ma { get; set; }
		public virtual Boolean QTP { get; set; }
		public virtual String CustomerStatus { get; set; }
		public virtual DateTime? ReturnedLapseDate { get; set; }
		public virtual String FranchiseCode { get; set; }
		public virtual String SalesConsultant { get; set; }
		public virtual DateTime? FirstOrderDate { get; set; }
		public virtual DateTime? SampleKitSentOn { get; set; }
		public virtual String Potential { get; set; }
		#endregion

		public virtual List<int> DeniedTemplates { get; set; } = new List<int>();

		public virtual ICustomerUser ParentCustomer { get; set; }

		public virtual bool IsPrintPortalEnabled { get; set; }
		public virtual PrintPortalSettings PrintPortalSettings { get; set; } = new PrintPortalSettings();

		public virtual bool Archived { get; set; }

		public List<int> CombinedDeniedTemplates()
		{
			var dTIds = new List<int>();
			if (PrintPortalSettings != null && PrintPortalSettings.DeniedTemplates != null && PrintPortalSettings.DeniedTemplates.Any())
			{
				dTIds.AddRange(PrintPortalSettings.DeniedTemplates);
			}
			if (DeniedTemplates != null && DeniedTemplates.Any())
			{
				dTIds.AddRange(DeniedTemplates);
			}
			dTIds = dTIds.Distinct().ToList();
			return dTIds;
		}

		public virtual string MyobUid { get; set; }

	}

	public class CustomerContact
	{
		public virtual int Id { get; set; }
		public virtual ICustomerUser Customer { get; set; }
		public virtual String Name { get; set; }
		public virtual String Email { get; set; }
		public virtual bool ReceiveMarketingEmails { get; set; }
		public virtual String Mobile { get; set; }
		public virtual String Phone { get; set; }
	}


	public class CustomerNote
	{
		public virtual int Id { get; set; }
		public virtual ICustomerUser Customer { get; set; }
		public virtual String NoteText { get; set; }
		public virtual String CreatedBy { get; set; }
		public virtual DateTime CreatedOn { get; set; }
		public virtual bool IsDocument { get; set; }
		public virtual string MimeType { get; set; }
		public virtual string FileName { get; set; }
		public virtual long   FileSize { get; set; }
		public virtual string DocumentBody { get; set; } // this is not mapped in NHibernate as we dont want to load it unless its a download request
	}
}
