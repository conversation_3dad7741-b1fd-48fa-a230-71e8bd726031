[2025-06-17 17:34:58.539 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 17:34:58.569 +10:00 INF] Initializing FastReport...
[2025-06-17 17:34:58.653 +10:00 INF] FastReport initialized successfully
[2025-06-17 17:34:59.051 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 17:35:00.407 +10:00 INF] Starting LEP Invoicer at "2025-06-17T17:35:00.4064513+10:00"
[2025-06-17 17:35:00.411 +10:00 INF] Initializing database service...
[2025-06-17 17:35:00.414 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 17:35:00.518 +10:00 INF] Database connection established successfully
[2025-06-17 17:35:00.519 +10:00 INF] Database service initialized successfully
[2025-06-17 17:35:00.522 +10:00 INF] Checking for pending work...
[2025-06-17 17:35:00.525 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 17:35:01.567 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 17:35:01.574 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 17:35:01.598 +10:00 INF] Found 0 credits to invoice
[2025-06-17 17:35:01.601 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 17:35:01.605 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 17:35:01.607 +10:00 INF] No pending work found
[2025-06-17 17:35:01.614 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 17:35:01.616 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 17:35:01.731 +10:00 INF] LEP Invoicer completed successfully in 1324ms. No work to process.
[2025-06-17 17:35:01.751 +10:00 INF] Database connection disposed
[2025-06-17 17:35:01.753 +10:00 INF] LEP Invoicer completed with result: 0
