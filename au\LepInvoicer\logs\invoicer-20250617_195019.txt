[2025-06-17 19:50:19.565 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 19:50:19.601 +10:00 INF] Initializing FastReport...
[2025-06-17 19:50:19.732 +10:00 INF] FastReport initialized successfully
[2025-06-17 19:50:20.193 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 19:50:21.465 +10:00 INF] Starting LEP Invoicer at "2025-06-17T19:50:21.4647982+10:00"
[2025-06-17 19:50:21.469 +10:00 INF] Initializing database service...
[2025-06-17 19:50:21.473 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 19:50:21.582 +10:00 INF] Database connection established successfully
[2025-06-17 19:50:21.583 +10:00 INF] Database service initialized successfully
[2025-06-17 19:50:21.602 +10:00 INF] Checking for pending work...
[2025-06-17 19:50:21.611 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 19:50:22.497 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 19:50:22.499 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 19:50:22.521 +10:00 INF] Found 0 credits to invoice
[2025-06-17 19:50:22.523 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 19:50:22.527 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 19:50:22.535 +10:00 INF] No pending work found
[2025-06-17 19:50:22.538 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 19:50:22.539 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 19:50:22.620 +10:00 INF] LEP Invoicer completed successfully in 1155ms. No work to process.
[2025-06-17 19:50:22.627 +10:00 INF] Database connection disposed
[2025-06-17 19:50:22.628 +10:00 INF] LEP Invoicer completed with result: 0
