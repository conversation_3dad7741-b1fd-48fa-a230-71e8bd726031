[2025-06-17 17:39:21.588 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 17:39:21.616 +10:00 INF] Initializing FastReport...
[2025-06-17 17:39:21.705 +10:00 INF] FastReport initialized successfully
[2025-06-17 17:39:22.108 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 17:39:23.539 +10:00 INF] Starting LEP Invoicer at "2025-06-17T17:39:23.5386318+10:00"
[2025-06-17 17:39:23.542 +10:00 INF] Initializing database service...
[2025-06-17 17:39:23.545 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 17:39:23.662 +10:00 INF] Database connection established successfully
[2025-06-17 17:39:23.664 +10:00 INF] Database service initialized successfully
[2025-06-17 17:39:23.667 +10:00 INF] Checking for pending work...
[2025-06-17 17:39:23.670 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 17:39:24.581 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 17:39:24.583 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 17:39:24.596 +10:00 INF] Found 0 credits to invoice
[2025-06-17 17:39:24.598 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 17:39:24.606 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 17:39:24.607 +10:00 INF] No pending work found
[2025-06-17 17:39:24.608 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 17:39:24.610 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 17:39:24.694 +10:00 INF] LEP Invoicer completed successfully in 1155ms. No work to process.
[2025-06-17 17:39:24.700 +10:00 INF] Database connection disposed
[2025-06-17 17:39:24.702 +10:00 INF] LEP Invoicer completed with result: 0
