[2025-06-18 10:43:14.112 +10:00 INF] Starting LEP Invoicer application
[2025-06-18 10:43:14.143 +10:00 INF] Initializing FastReport...
[2025-06-18 10:43:14.229 +10:00 INF] FastReport initialized successfully
[2025-06-18 10:43:14.830 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-18 10:43:16.279 +10:00 INF] Starting LEP Invoicer at "2025-06-18T10:43:16.2790560+10:00"
[2025-06-18 10:43:16.282 +10:00 INF] Initializing database service...
[2025-06-18 10:43:16.285 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-18 10:43:16.401 +10:00 INF] Database connection established successfully
[2025-06-18 10:43:16.402 +10:00 INF] Database service initialized successfully
[2025-06-18 10:43:16.406 +10:00 INF] Checking for pending work...
[2025-06-18 10:43:16.409 +10:00 INF] Getting 500 orders to invoice
[2025-06-18 10:43:17.453 +10:00 INF] Found 0 orders to invoice (filtered 17 candidates)
[2025-06-18 10:43:17.456 +10:00 INF] Getting 100 credits to invoice
[2025-06-18 10:43:17.469 +10:00 INF] Found 0 credits to invoice
[2025-06-18 10:43:17.471 +10:00 INF] Getting 100 refunds to invoice
[2025-06-18 10:43:17.474 +10:00 INF] Found 0 refunds to invoice
[2025-06-18 10:43:17.476 +10:00 INF] No pending work found
[2025-06-18 10:43:17.477 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-18 10:43:17.478 +10:00 INF] Cleaning up invoicer logs...
[2025-06-18 10:43:17.560 +10:00 INF] LEP Invoicer completed successfully in 1281ms. No work to process.
[2025-06-18 10:43:17.567 +10:00 INF] Database connection disposed
[2025-06-18 10:43:17.569 +10:00 INF] LEP Invoicer completed with result: 0
