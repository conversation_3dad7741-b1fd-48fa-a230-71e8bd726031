[2025-06-18 09:41:43.562 +10:00 INF] Starting LEP Invoicer application
[2025-06-18 09:41:43.592 +10:00 INF] Initializing FastReport...
[2025-06-18 09:41:43.663 +10:00 INF] FastReport initialized successfully
[2025-06-18 09:41:44.091 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-18 09:41:45.482 +10:00 INF] Starting LEP Invoicer at "2025-06-18T09:41:45.4824097+10:00"
[2025-06-18 09:41:45.486 +10:00 INF] Initializing database service...
[2025-06-18 09:41:45.489 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-18 09:41:45.589 +10:00 INF] Database connection established successfully
[2025-06-18 09:41:45.591 +10:00 INF] Database service initialized successfully
[2025-06-18 09:41:45.595 +10:00 INF] Checking for pending work...
[2025-06-18 09:41:45.598 +10:00 INF] Getting 500 orders to invoice
[2025-06-18 09:41:46.511 +10:00 INF] Found 0 orders to invoice (filtered 16 candidates)
[2025-06-18 09:41:46.514 +10:00 INF] Getting 100 credits to invoice
[2025-06-18 09:41:46.536 +10:00 INF] Found 0 credits to invoice
[2025-06-18 09:41:46.538 +10:00 INF] Getting 100 refunds to invoice
[2025-06-18 09:41:46.545 +10:00 INF] Found 0 refunds to invoice
[2025-06-18 09:41:46.547 +10:00 INF] No pending work found
[2025-06-18 09:41:46.550 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-18 09:41:46.553 +10:00 INF] Cleaning up invoicer logs...
[2025-06-18 09:41:46.660 +10:00 INF] LEP Invoicer completed successfully in 1178ms. No work to process.
[2025-06-18 09:41:46.667 +10:00 INF] Database connection disposed
[2025-06-18 09:41:46.669 +10:00 INF] LEP Invoicer completed with result: 0
