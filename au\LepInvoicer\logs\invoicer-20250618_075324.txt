[2025-06-18 07:53:24.584 +10:00 INF] Starting LEP Invoicer application
[2025-06-18 07:53:24.617 +10:00 INF] Initializing FastReport...
[2025-06-18 07:53:24.692 +10:00 INF] FastReport initialized successfully
[2025-06-18 07:53:25.145 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-18 07:53:26.354 +10:00 INF] Starting LEP Invoicer at "2025-06-18T07:53:26.3538888+10:00"
[2025-06-18 07:53:26.357 +10:00 INF] Initializing database service...
[2025-06-18 07:53:26.360 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-18 07:53:26.458 +10:00 INF] Database connection established successfully
[2025-06-18 07:53:26.459 +10:00 INF] Database service initialized successfully
[2025-06-18 07:53:26.462 +10:00 INF] Checking for pending work...
[2025-06-18 07:53:26.465 +10:00 INF] Getting 500 orders to invoice
[2025-06-18 07:53:27.367 +10:00 INF] Found 0 orders to invoice (filtered 16 candidates)
[2025-06-18 07:53:27.370 +10:00 INF] Getting 100 credits to invoice
[2025-06-18 07:53:27.383 +10:00 INF] Found 0 credits to invoice
[2025-06-18 07:53:27.385 +10:00 INF] Getting 100 refunds to invoice
[2025-06-18 07:53:27.390 +10:00 INF] Found 0 refunds to invoice
[2025-06-18 07:53:27.392 +10:00 INF] No pending work found
[2025-06-18 07:53:27.395 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-18 07:53:27.396 +10:00 INF] Cleaning up invoicer logs...
[2025-06-18 07:53:27.490 +10:00 INF] LEP Invoicer completed successfully in 1136ms. No work to process.
[2025-06-18 07:53:27.496 +10:00 INF] Database connection disposed
[2025-06-18 07:53:27.498 +10:00 INF] LEP Invoicer completed with result: 0
