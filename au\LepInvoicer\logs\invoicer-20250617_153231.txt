[2025-06-17 15:32:31.794 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 15:32:31.834 +10:00 INF] Initializing FastReport...
[2025-06-17 15:32:31.918 +10:00 INF] FastReport initialized successfully
[2025-06-17 15:32:32.464 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 15:32:33.929 +10:00 INF] Starting LEP Invoicer at "2025-06-17T15:32:33.9287275+10:00"
[2025-06-17 15:32:33.933 +10:00 INF] Initializing database service...
[2025-06-17 15:32:33.936 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 15:32:34.054 +10:00 INF] Database connection established successfully
[2025-06-17 15:32:34.055 +10:00 INF] Database service initialized successfully
[2025-06-17 15:32:34.059 +10:00 INF] Checking for pending work...
[2025-06-17 15:32:34.068 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 15:32:35.129 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 15:32:35.132 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 15:32:35.145 +10:00 INF] Found 0 credits to invoice
[2025-06-17 15:32:35.148 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 15:32:35.158 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 15:32:35.162 +10:00 INF] No pending work found
[2025-06-17 15:32:35.164 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 15:32:35.165 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 15:32:35.255 +10:00 INF] LEP Invoicer completed successfully in 1326ms. No work to process.
[2025-06-17 15:32:35.261 +10:00 INF] Database connection disposed
[2025-06-17 15:32:35.263 +10:00 INF] LEP Invoicer completed with result: 0
