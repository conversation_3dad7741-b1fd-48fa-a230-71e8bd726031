[2025-06-17 18:05:33.586 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 18:05:33.613 +10:00 INF] Initializing FastReport...
[2025-06-17 18:05:33.688 +10:00 INF] FastReport initialized successfully
[2025-06-17 18:05:34.108 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 18:05:35.480 +10:00 INF] Starting LEP Invoicer at "2025-06-17T18:05:35.4805013+10:00"
[2025-06-17 18:05:35.484 +10:00 INF] Initializing database service...
[2025-06-17 18:05:35.486 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 18:05:35.582 +10:00 INF] Database connection established successfully
[2025-06-17 18:05:35.584 +10:00 INF] Database service initialized successfully
[2025-06-17 18:05:35.586 +10:00 INF] Checking for pending work...
[2025-06-17 18:05:35.589 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 18:05:36.437 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 18:05:36.440 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 18:05:36.452 +10:00 INF] Found 0 credits to invoice
[2025-06-17 18:05:36.455 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 18:05:36.460 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 18:05:36.462 +10:00 INF] No pending work found
[2025-06-17 18:05:36.463 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 18:05:36.464 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 18:05:36.538 +10:00 INF] LEP Invoicer completed successfully in 1057ms. No work to process.
[2025-06-17 18:05:36.545 +10:00 INF] Database connection disposed
[2025-06-17 18:05:36.546 +10:00 INF] LEP Invoicer completed with result: 0
