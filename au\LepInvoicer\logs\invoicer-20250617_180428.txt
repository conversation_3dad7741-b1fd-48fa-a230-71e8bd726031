[2025-06-17 18:04:28.441 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 18:04:28.468 +10:00 INF] Initializing FastReport...
[2025-06-17 18:04:28.538 +10:00 INF] FastReport initialized successfully
[2025-06-17 18:04:28.941 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 18:04:30.216 +10:00 INF] Starting LEP Invoicer at "2025-06-17T18:04:30.2157982+10:00"
[2025-06-17 18:04:30.220 +10:00 INF] Initializing database service...
[2025-06-17 18:04:30.230 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 18:04:30.328 +10:00 INF] Database connection established successfully
[2025-06-17 18:04:30.332 +10:00 INF] Database service initialized successfully
[2025-06-17 18:04:30.338 +10:00 INF] Checking for pending work...
[2025-06-17 18:04:30.354 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 18:04:31.267 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 18:04:31.280 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 18:04:31.297 +10:00 INF] Found 0 credits to invoice
[2025-06-17 18:04:31.301 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 18:04:31.304 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 18:04:31.306 +10:00 INF] No pending work found
[2025-06-17 18:04:31.307 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 18:04:31.309 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 18:04:31.410 +10:00 INF] LEP Invoicer completed successfully in 1194ms. No work to process.
[2025-06-17 18:04:31.416 +10:00 INF] Database connection disposed
[2025-06-17 18:04:31.419 +10:00 INF] LEP Invoicer completed with result: 0
