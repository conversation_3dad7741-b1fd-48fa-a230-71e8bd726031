[2025-06-17 17:56:48.643 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 17:56:48.671 +10:00 INF] Initializing FastReport...
[2025-06-17 17:56:48.747 +10:00 INF] FastReport initialized successfully
[2025-06-17 17:56:49.160 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 17:56:50.459 +10:00 INF] Starting LEP Invoicer at "2025-06-17T17:56:50.4590579+10:00"
[2025-06-17 17:56:50.465 +10:00 INF] Initializing database service...
[2025-06-17 17:56:50.470 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 17:56:50.591 +10:00 INF] Database connection established successfully
[2025-06-17 17:56:50.592 +10:00 INF] Database service initialized successfully
[2025-06-17 17:56:50.595 +10:00 INF] Checking for pending work...
[2025-06-17 17:56:50.598 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 17:56:51.487 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 17:56:51.492 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 17:56:51.505 +10:00 INF] Found 0 credits to invoice
[2025-06-17 17:56:51.508 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 17:56:51.513 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 17:56:51.514 +10:00 INF] No pending work found
[2025-06-17 17:56:51.516 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 17:56:51.519 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 17:56:51.602 +10:00 INF] LEP Invoicer completed successfully in 1143ms. No work to process.
[2025-06-17 17:56:51.610 +10:00 INF] Database connection disposed
[2025-06-17 17:56:51.612 +10:00 INF] LEP Invoicer completed with result: 0
