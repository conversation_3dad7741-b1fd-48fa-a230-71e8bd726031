[2025-06-17 16:53:37.454 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 16:53:37.482 +10:00 INF] Initializing FastReport...
[2025-06-17 16:53:37.586 +10:00 INF] FastReport initialized successfully
[2025-06-17 16:53:38.015 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 16:53:39.312 +10:00 INF] Starting LEP Invoicer at "2025-06-17T16:53:39.3116923+10:00"
[2025-06-17 16:53:39.315 +10:00 INF] Initializing database service...
[2025-06-17 16:53:39.318 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 16:53:39.416 +10:00 INF] Database connection established successfully
[2025-06-17 16:53:39.418 +10:00 INF] Database service initialized successfully
[2025-06-17 16:53:39.420 +10:00 INF] Checking for pending work...
[2025-06-17 16:53:39.423 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 16:53:40.284 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 16:53:40.288 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 16:53:40.301 +10:00 INF] Found 0 credits to invoice
[2025-06-17 16:53:40.303 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 16:53:40.309 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 16:53:40.310 +10:00 INF] No pending work found
[2025-06-17 16:53:40.312 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 16:53:40.314 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 16:53:40.395 +10:00 INF] LEP Invoicer completed successfully in 1084ms. No work to process.
[2025-06-17 16:53:40.402 +10:00 INF] Database connection disposed
[2025-06-17 16:53:40.403 +10:00 INF] LEP Invoicer completed with result: 0
