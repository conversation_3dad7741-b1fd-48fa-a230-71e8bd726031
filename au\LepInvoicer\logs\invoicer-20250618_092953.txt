[2025-06-18 09:29:53.686 +10:00 INF] Starting LEP Invoicer application
[2025-06-18 09:29:53.715 +10:00 INF] Initializing FastReport...
[2025-06-18 09:29:53.796 +10:00 INF] FastReport initialized successfully
[2025-06-18 09:29:54.320 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-18 09:29:55.673 +10:00 INF] Starting LEP Invoicer at "2025-06-18T09:29:55.6731613+10:00"
[2025-06-18 09:29:55.676 +10:00 INF] Initializing database service...
[2025-06-18 09:29:55.679 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-18 09:29:55.786 +10:00 INF] Database connection established successfully
[2025-06-18 09:29:55.805 +10:00 INF] Database service initialized successfully
[2025-06-18 09:29:55.809 +10:00 INF] Checking for pending work...
[2025-06-18 09:29:55.812 +10:00 INF] Getting 500 orders to invoice
[2025-06-18 09:29:56.703 +10:00 INF] Found 0 orders to invoice (filtered 16 candidates)
[2025-06-18 09:29:56.706 +10:00 INF] Getting 100 credits to invoice
[2025-06-18 09:29:56.720 +10:00 INF] Found 0 credits to invoice
[2025-06-18 09:29:56.723 +10:00 INF] Getting 100 refunds to invoice
[2025-06-18 09:29:56.729 +10:00 INF] Found 0 refunds to invoice
[2025-06-18 09:29:56.730 +10:00 INF] No pending work found
[2025-06-18 09:29:56.732 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-18 09:29:56.733 +10:00 INF] Cleaning up invoicer logs...
[2025-06-18 09:29:56.820 +10:00 INF] LEP Invoicer completed successfully in 1147ms. No work to process.
[2025-06-18 09:29:56.827 +10:00 INF] Database connection disposed
[2025-06-18 09:29:56.829 +10:00 INF] LEP Invoicer completed with result: 0
