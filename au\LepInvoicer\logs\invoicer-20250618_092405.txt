[2025-06-18 09:24:05.666 +10:00 INF] Starting LEP Invoicer application
[2025-06-18 09:24:05.694 +10:00 INF] Initializing FastReport...
[2025-06-18 09:24:05.767 +10:00 INF] FastReport initialized successfully
[2025-06-18 09:24:06.194 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-18 09:24:07.510 +10:00 INF] Starting LEP Invoicer at "2025-06-18T09:24:07.5099774+10:00"
[2025-06-18 09:24:07.514 +10:00 INF] Initializing database service...
[2025-06-18 09:24:07.518 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-18 09:24:07.630 +10:00 INF] Database connection established successfully
[2025-06-18 09:24:07.631 +10:00 INF] Database service initialized successfully
[2025-06-18 09:24:07.634 +10:00 INF] Checking for pending work...
[2025-06-18 09:24:07.637 +10:00 INF] Getting 500 orders to invoice
[2025-06-18 09:24:08.548 +10:00 INF] Found 0 orders to invoice (filtered 16 candidates)
[2025-06-18 09:24:08.551 +10:00 INF] Getting 100 credits to invoice
[2025-06-18 09:24:08.563 +10:00 INF] Found 0 credits to invoice
[2025-06-18 09:24:08.565 +10:00 INF] Getting 100 refunds to invoice
[2025-06-18 09:24:08.569 +10:00 INF] Found 0 refunds to invoice
[2025-06-18 09:24:08.570 +10:00 INF] No pending work found
[2025-06-18 09:24:08.571 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-18 09:24:08.572 +10:00 INF] Cleaning up invoicer logs...
[2025-06-18 09:24:08.645 +10:00 INF] LEP Invoicer completed successfully in 1135ms. No work to process.
[2025-06-18 09:24:08.652 +10:00 INF] Database connection disposed
[2025-06-18 09:24:08.654 +10:00 INF] LEP Invoicer completed with result: 0
