[2025-06-17 16:06:29.473 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 16:06:29.510 +10:00 INF] Initializing FastReport...
[2025-06-17 16:06:29.600 +10:00 INF] FastReport initialized successfully
[2025-06-17 16:06:30.109 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 16:06:31.421 +10:00 INF] Starting LEP Invoicer at "2025-06-17T16:06:31.4208174+10:00"
[2025-06-17 16:06:31.424 +10:00 INF] Initializing database service...
[2025-06-17 16:06:31.427 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 16:06:31.531 +10:00 INF] Database connection established successfully
[2025-06-17 16:06:31.532 +10:00 INF] Database service initialized successfully
[2025-06-17 16:06:31.536 +10:00 INF] Checking for pending work...
[2025-06-17 16:06:31.539 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 16:06:32.409 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 16:06:32.412 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 16:06:32.425 +10:00 INF] Found 0 credits to invoice
[2025-06-17 16:06:32.427 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 16:06:32.432 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 16:06:32.433 +10:00 INF] No pending work found
[2025-06-17 16:06:32.434 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 16:06:32.436 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 16:06:32.529 +10:00 INF] LEP Invoicer completed successfully in 1108ms. No work to process.
[2025-06-17 16:06:32.542 +10:00 INF] Database connection disposed
[2025-06-17 16:06:32.544 +10:00 INF] LEP Invoicer completed with result: 0
