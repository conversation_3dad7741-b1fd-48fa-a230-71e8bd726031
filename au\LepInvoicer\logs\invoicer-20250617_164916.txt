[2025-06-17 16:49:16.610 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 16:49:16.636 +10:00 INF] Initializing FastReport...
[2025-06-17 16:49:16.708 +10:00 INF] FastReport initialized successfully
[2025-06-17 16:49:17.129 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 16:49:18.448 +10:00 INF] Starting LEP Invoicer at "2025-06-17T16:49:18.4476514+10:00"
[2025-06-17 16:49:18.469 +10:00 INF] Initializing database service...
[2025-06-17 16:49:18.473 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 16:49:18.573 +10:00 INF] Database connection established successfully
[2025-06-17 16:49:18.574 +10:00 INF] Database service initialized successfully
[2025-06-17 16:49:18.577 +10:00 INF] Checking for pending work...
[2025-06-17 16:49:18.579 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 16:49:19.901 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 16:49:19.904 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 16:49:19.916 +10:00 INF] Found 0 credits to invoice
[2025-06-17 16:49:19.917 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 16:49:19.921 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 16:49:19.922 +10:00 INF] No pending work found
[2025-06-17 16:49:19.924 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 16:49:19.925 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 16:49:20.001 +10:00 INF] LEP Invoicer completed successfully in 1553ms. No work to process.
[2025-06-17 16:49:20.007 +10:00 INF] Database connection disposed
[2025-06-17 16:49:20.009 +10:00 INF] LEP Invoicer completed with result: 0
