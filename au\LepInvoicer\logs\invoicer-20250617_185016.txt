[2025-06-17 18:50:16.835 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 18:50:16.873 +10:00 INF] Initializing FastReport...
[2025-06-17 18:50:16.957 +10:00 INF] FastReport initialized successfully
[2025-06-17 18:50:17.368 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 18:50:18.660 +10:00 INF] Starting LEP Invoicer at "2025-06-17T18:50:18.6603941+10:00"
[2025-06-17 18:50:18.664 +10:00 INF] Initializing database service...
[2025-06-17 18:50:18.666 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 18:50:18.764 +10:00 INF] Database connection established successfully
[2025-06-17 18:50:18.766 +10:00 INF] Database service initialized successfully
[2025-06-17 18:50:18.769 +10:00 INF] Checking for pending work...
[2025-06-17 18:50:18.772 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 18:50:19.654 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 18:50:19.657 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 18:50:19.669 +10:00 INF] Found 0 credits to invoice
[2025-06-17 18:50:19.671 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 18:50:19.674 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 18:50:19.676 +10:00 INF] No pending work found
[2025-06-17 18:50:19.678 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 18:50:19.679 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 18:50:19.782 +10:00 INF] LEP Invoicer completed successfully in 1122ms. No work to process.
[2025-06-17 18:50:19.792 +10:00 INF] Database connection disposed
[2025-06-17 18:50:19.798 +10:00 INF] LEP Invoicer completed with result: 0
