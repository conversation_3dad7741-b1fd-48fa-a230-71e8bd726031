[2025-06-17 19:19:44.493 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 19:19:44.522 +10:00 INF] Initializing FastReport...
[2025-06-17 19:19:44.594 +10:00 INF] FastReport initialized successfully
[2025-06-17 19:19:44.988 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 19:19:46.249 +10:00 INF] Starting LEP Invoicer at "2025-06-17T19:19:46.2491547+10:00"
[2025-06-17 19:19:46.253 +10:00 INF] Initializing database service...
[2025-06-17 19:19:46.257 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 19:19:46.365 +10:00 INF] Database connection established successfully
[2025-06-17 19:19:46.367 +10:00 INF] Database service initialized successfully
[2025-06-17 19:19:46.369 +10:00 INF] Checking for pending work...
[2025-06-17 19:19:46.385 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 19:19:47.298 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 19:19:47.301 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 19:19:47.313 +10:00 INF] Found 0 credits to invoice
[2025-06-17 19:19:47.315 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 19:19:47.319 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 19:19:47.320 +10:00 INF] No pending work found
[2025-06-17 19:19:47.322 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 19:19:47.323 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 19:19:47.411 +10:00 INF] LEP Invoicer completed successfully in 1162ms. No work to process.
[2025-06-17 19:19:47.423 +10:00 INF] Database connection disposed
[2025-06-17 19:19:47.424 +10:00 INF] LEP Invoicer completed with result: 0
