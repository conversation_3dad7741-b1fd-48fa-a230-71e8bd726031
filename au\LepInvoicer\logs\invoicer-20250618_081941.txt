[2025-06-18 08:19:41.600 +10:00 INF] Starting LEP Invoicer application
[2025-06-18 08:19:41.627 +10:00 INF] Initializing FastReport...
[2025-06-18 08:19:41.698 +10:00 INF] FastReport initialized successfully
[2025-06-18 08:19:42.102 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-18 08:19:43.390 +10:00 INF] Starting LEP Invoicer at "2025-06-18T08:19:43.3896764+10:00"
[2025-06-18 08:19:43.393 +10:00 INF] Initializing database service...
[2025-06-18 08:19:43.396 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-18 08:19:43.514 +10:00 INF] Database connection established successfully
[2025-06-18 08:19:43.516 +10:00 INF] Database service initialized successfully
[2025-06-18 08:19:43.530 +10:00 INF] Checking for pending work...
[2025-06-18 08:19:43.533 +10:00 INF] Getting 500 orders to invoice
[2025-06-18 08:19:44.451 +10:00 INF] Found 0 orders to invoice (filtered 16 candidates)
[2025-06-18 08:19:44.454 +10:00 INF] Getting 100 credits to invoice
[2025-06-18 08:19:44.468 +10:00 INF] Found 0 credits to invoice
[2025-06-18 08:19:44.470 +10:00 INF] Getting 100 refunds to invoice
[2025-06-18 08:19:44.484 +10:00 INF] Found 0 refunds to invoice
[2025-06-18 08:19:44.485 +10:00 INF] No pending work found
[2025-06-18 08:19:44.492 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-18 08:19:44.493 +10:00 INF] Cleaning up invoicer logs...
[2025-06-18 08:19:44.589 +10:00 INF] LEP Invoicer completed successfully in 1199ms. No work to process.
[2025-06-18 08:19:44.596 +10:00 INF] Database connection disposed
[2025-06-18 08:19:44.598 +10:00 INF] LEP Invoicer completed with result: 0
