[2025-06-17 20:16:31.619 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 20:16:31.647 +10:00 INF] Initializing FastReport...
[2025-06-17 20:16:31.727 +10:00 INF] FastReport initialized successfully
[2025-06-17 20:16:32.111 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 20:16:33.496 +10:00 INF] Starting LEP Invoicer at "2025-06-17T20:16:33.4965848+10:00"
[2025-06-17 20:16:33.500 +10:00 INF] Initializing database service...
[2025-06-17 20:16:33.503 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 20:16:33.606 +10:00 INF] Database connection established successfully
[2025-06-17 20:16:33.607 +10:00 INF] Database service initialized successfully
[2025-06-17 20:16:33.610 +10:00 INF] Checking for pending work...
[2025-06-17 20:16:33.617 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 20:16:34.485 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 20:16:34.488 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 20:16:34.501 +10:00 INF] Found 0 credits to invoice
[2025-06-17 20:16:34.514 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 20:16:34.519 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 20:16:34.530 +10:00 INF] No pending work found
[2025-06-17 20:16:34.533 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 20:16:34.534 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 20:16:34.630 +10:00 INF] LEP Invoicer completed successfully in 1133ms. No work to process.
[2025-06-17 20:16:34.636 +10:00 INF] Database connection disposed
[2025-06-17 20:16:34.638 +10:00 INF] LEP Invoicer completed with result: 0
