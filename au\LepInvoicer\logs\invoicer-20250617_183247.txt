[2025-06-17 18:32:47.719 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 18:32:47.748 +10:00 INF] Initializing FastReport...
[2025-06-17 18:32:47.856 +10:00 INF] FastReport initialized successfully
[2025-06-17 18:32:48.305 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 18:32:49.589 +10:00 INF] Starting LEP Invoicer at "2025-06-17T18:32:49.5888283+10:00"
[2025-06-17 18:32:49.592 +10:00 INF] Initializing database service...
[2025-06-17 18:32:49.595 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 18:32:49.692 +10:00 INF] Database connection established successfully
[2025-06-17 18:32:49.693 +10:00 INF] Database service initialized successfully
[2025-06-17 18:32:49.696 +10:00 INF] Checking for pending work...
[2025-06-17 18:32:49.699 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 18:32:50.568 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 18:32:50.571 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 18:32:50.583 +10:00 INF] Found 0 credits to invoice
[2025-06-17 18:32:50.585 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 18:32:50.588 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 18:32:50.591 +10:00 INF] No pending work found
[2025-06-17 18:32:50.592 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 18:32:50.595 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 18:32:50.669 +10:00 INF] LEP Invoicer completed successfully in 1081ms. No work to process.
[2025-06-17 18:32:50.676 +10:00 INF] Database connection disposed
[2025-06-17 18:32:50.678 +10:00 INF] LEP Invoicer completed with result: 0
