[2025-06-17 18:34:59.494 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 18:34:59.521 +10:00 INF] Initializing FastReport...
[2025-06-17 18:34:59.596 +10:00 INF] FastReport initialized successfully
[2025-06-17 18:34:59.996 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 18:35:01.357 +10:00 INF] Starting LEP Invoicer at "2025-06-17T18:35:01.3575258+10:00"
[2025-06-17 18:35:01.361 +10:00 INF] Initializing database service...
[2025-06-17 18:35:01.364 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 18:35:01.472 +10:00 INF] Database connection established successfully
[2025-06-17 18:35:01.474 +10:00 INF] Database service initialized successfully
[2025-06-17 18:35:01.477 +10:00 INF] Checking for pending work...
[2025-06-17 18:35:01.480 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 18:35:02.548 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 18:35:02.551 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 18:35:02.564 +10:00 INF] Found 0 credits to invoice
[2025-06-17 18:35:02.566 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 18:35:02.589 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 18:35:02.593 +10:00 INF] No pending work found
[2025-06-17 18:35:02.595 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 18:35:02.607 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 18:35:02.718 +10:00 INF] LEP Invoicer completed successfully in 1360ms. No work to process.
[2025-06-17 18:35:02.724 +10:00 INF] Database connection disposed
[2025-06-17 18:35:02.726 +10:00 INF] LEP Invoicer completed with result: 0
