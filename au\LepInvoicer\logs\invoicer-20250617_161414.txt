[2025-06-17 16:14:14.521 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 16:14:14.549 +10:00 INF] Initializing FastReport...
[2025-06-17 16:14:14.620 +10:00 INF] FastReport initialized successfully
[2025-06-17 16:14:15.045 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 16:14:16.313 +10:00 INF] Starting LEP Invoicer at "2025-06-17T16:14:16.3129406+10:00"
[2025-06-17 16:14:16.317 +10:00 INF] Initializing database service...
[2025-06-17 16:14:16.320 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 16:14:16.429 +10:00 INF] Database connection established successfully
[2025-06-17 16:14:16.445 +10:00 INF] Database service initialized successfully
[2025-06-17 16:14:16.452 +10:00 INF] Checking for pending work...
[2025-06-17 16:14:16.456 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 16:14:17.384 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 16:14:17.392 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 16:14:17.410 +10:00 INF] Found 0 credits to invoice
[2025-06-17 16:14:17.412 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 16:14:17.416 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 16:14:17.419 +10:00 INF] No pending work found
[2025-06-17 16:14:17.422 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 16:14:17.423 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 16:14:17.500 +10:00 INF] LEP Invoicer completed successfully in 1187ms. No work to process.
[2025-06-17 16:14:17.507 +10:00 INF] Database connection disposed
[2025-06-17 16:14:17.509 +10:00 INF] LEP Invoicer completed with result: 0
