[2025-06-17 16:56:52.562 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 16:56:52.609 +10:00 INF] Initializing FastReport...
[2025-06-17 16:56:52.692 +10:00 INF] FastReport initialized successfully
[2025-06-17 16:56:53.140 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 16:56:54.446 +10:00 INF] Starting LEP Invoicer at "2025-06-17T16:56:54.4457379+10:00"
[2025-06-17 16:56:54.451 +10:00 INF] Initializing database service...
[2025-06-17 16:56:54.455 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 16:56:54.561 +10:00 INF] Database connection established successfully
[2025-06-17 16:56:54.562 +10:00 INF] Database service initialized successfully
[2025-06-17 16:56:54.565 +10:00 INF] Checking for pending work...
[2025-06-17 16:56:54.568 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 16:56:55.426 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 16:56:55.428 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 16:56:55.441 +10:00 INF] Found 0 credits to invoice
[2025-06-17 16:56:55.443 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 16:56:55.446 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 16:56:55.448 +10:00 INF] No pending work found
[2025-06-17 16:56:55.450 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 16:56:55.451 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 16:56:55.546 +10:00 INF] LEP Invoicer completed successfully in 1100ms. No work to process.
[2025-06-17 16:56:55.553 +10:00 INF] Database connection disposed
[2025-06-17 16:56:55.557 +10:00 INF] LEP Invoicer completed with result: 0
