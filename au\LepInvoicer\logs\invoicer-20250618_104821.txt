[2025-06-18 10:48:21.884 +10:00 INF] Starting LEP Invoicer application
[2025-06-18 10:48:21.921 +10:00 INF] Initializing FastReport...
[2025-06-18 10:48:22.050 +10:00 INF] FastReport initialized successfully
[2025-06-18 10:48:22.870 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-18 10:48:26.839 +10:00 INF] Starting LEP Invoicer at "2025-06-18T10:48:26.8392385+10:00"
[2025-06-18 10:48:27.939 +10:00 INF] Initializing database service...
[2025-06-18 10:48:28.250 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-18 10:48:28.725 +10:00 INF] Database connection established successfully
[2025-06-18 10:48:28.729 +10:00 INF] Database service initialized successfully
[2025-06-18 10:48:28.732 +10:00 INF] Checking for pending work...
[2025-06-18 10:48:28.754 +10:00 INF] Getting 500 orders to invoice
[2025-06-18 10:48:29.732 +10:00 INF] Found 0 orders to invoice (marked 0 zero-priced as invoiced, filtered 0 candidates)
[2025-06-18 10:48:29.738 +10:00 INF] Getting 100 credits to invoice
[2025-06-18 10:48:29.754 +10:00 INF] Found 0 credits to invoice
[2025-06-18 10:48:29.757 +10:00 INF] Getting 100 refunds to invoice
[2025-06-18 10:48:29.762 +10:00 INF] Found 0 refunds to invoice
[2025-06-18 10:48:29.764 +10:00 INF] No pending work found
[2025-06-18 10:48:29.771 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-18 10:48:29.785 +10:00 INF] Cleaning up invoicer logs...
[2025-06-18 10:48:29.917 +10:00 INF] LEP Invoicer completed successfully in 3077ms. No work to process.
[2025-06-18 10:48:29.924 +10:00 INF] Database connection disposed
[2025-06-18 10:48:29.927 +10:00 INF] LEP Invoicer completed with result: 0
