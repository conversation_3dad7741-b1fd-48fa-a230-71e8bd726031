[2025-06-17 17:51:22.439 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 17:51:22.471 +10:00 INF] Initializing FastReport...
[2025-06-17 17:51:22.548 +10:00 INF] FastReport initialized successfully
[2025-06-17 17:51:22.921 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 17:51:24.191 +10:00 INF] Starting LEP Invoicer at "2025-06-17T17:51:24.1913226+10:00"
[2025-06-17 17:51:24.196 +10:00 INF] Initializing database service...
[2025-06-17 17:51:24.198 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 17:51:24.313 +10:00 INF] Database connection established successfully
[2025-06-17 17:51:24.316 +10:00 INF] Database service initialized successfully
[2025-06-17 17:51:24.319 +10:00 INF] Checking for pending work...
[2025-06-17 17:51:24.330 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 17:51:25.197 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 17:51:25.200 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 17:51:25.212 +10:00 INF] Found 0 credits to invoice
[2025-06-17 17:51:25.214 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 17:51:25.217 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 17:51:25.219 +10:00 INF] No pending work found
[2025-06-17 17:51:25.221 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 17:51:25.242 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 17:51:25.328 +10:00 INF] LEP Invoicer completed successfully in 1136ms. No work to process.
[2025-06-17 17:51:25.334 +10:00 INF] Database connection disposed
[2025-06-17 17:51:25.336 +10:00 INF] LEP Invoicer completed with result: 0
