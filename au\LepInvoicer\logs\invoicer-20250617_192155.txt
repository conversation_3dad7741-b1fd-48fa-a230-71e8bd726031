[2025-06-17 19:21:55.504 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 19:21:55.544 +10:00 INF] Initializing FastReport...
[2025-06-17 19:21:55.618 +10:00 INF] FastReport initialized successfully
[2025-06-17 19:21:56.077 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 19:21:57.310 +10:00 INF] Starting LEP Invoicer at "2025-06-17T19:21:57.3103378+10:00"
[2025-06-17 19:21:57.325 +10:00 INF] Initializing database service...
[2025-06-17 19:21:57.328 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 19:21:57.435 +10:00 INF] Database connection established successfully
[2025-06-17 19:21:57.436 +10:00 INF] Database service initialized successfully
[2025-06-17 19:21:57.439 +10:00 INF] Checking for pending work...
[2025-06-17 19:21:57.441 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 19:21:58.369 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 19:21:58.372 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 19:21:58.384 +10:00 INF] Found 0 credits to invoice
[2025-06-17 19:21:58.396 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 19:21:58.409 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 19:21:58.415 +10:00 INF] No pending work found
[2025-06-17 19:21:58.416 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 19:21:58.418 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 19:21:58.513 +10:00 INF] LEP Invoicer completed successfully in 1202ms. No work to process.
[2025-06-17 19:21:58.519 +10:00 INF] Database connection disposed
[2025-06-17 19:21:58.521 +10:00 INF] LEP Invoicer completed with result: 0
