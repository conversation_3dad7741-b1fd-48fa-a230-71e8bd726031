[2025-06-17 19:00:05.985 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 19:00:06.013 +10:00 INF] Initializing FastReport...
[2025-06-17 19:00:06.086 +10:00 INF] FastReport initialized successfully
[2025-06-17 19:00:06.616 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 19:00:07.932 +10:00 INF] Starting LEP Invoicer at "2025-06-17T19:00:07.9321822+10:00"
[2025-06-17 19:00:07.936 +10:00 INF] Initializing database service...
[2025-06-17 19:00:07.939 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 19:00:08.040 +10:00 INF] Database connection established successfully
[2025-06-17 19:00:08.041 +10:00 INF] Database service initialized successfully
[2025-06-17 19:00:08.044 +10:00 INF] Checking for pending work...
[2025-06-17 19:00:08.047 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 19:00:08.905 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 19:00:08.907 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 19:00:08.920 +10:00 INF] Found 0 credits to invoice
[2025-06-17 19:00:08.922 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 19:00:08.926 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 19:00:08.927 +10:00 INF] No pending work found
[2025-06-17 19:00:08.929 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 19:00:08.930 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 19:00:09.009 +10:00 INF] LEP Invoicer completed successfully in 1077ms. No work to process.
[2025-06-17 19:00:09.017 +10:00 INF] Database connection disposed
[2025-06-17 19:00:09.018 +10:00 INF] LEP Invoicer completed with result: 0
