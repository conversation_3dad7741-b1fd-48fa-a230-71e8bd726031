[2025-06-18 10:49:59.679 +10:00 INF] Starting LEP Invoicer application
[2025-06-18 10:49:59.713 +10:00 INF] Initializing FastReport...
[2025-06-18 10:49:59.799 +10:00 INF] FastReport initialized successfully
[2025-06-18 10:50:00.437 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-18 10:50:01.921 +10:00 INF] Starting LEP Invoicer at "2025-06-18T10:50:01.9210794+10:00"
[2025-06-18 10:50:01.925 +10:00 INF] Initializing database service...
[2025-06-18 10:50:01.929 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-18 10:50:02.052 +10:00 INF] Database connection established successfully
[2025-06-18 10:50:02.054 +10:00 INF] Database service initialized successfully
[2025-06-18 10:50:02.057 +10:00 INF] Checking for pending work...
[2025-06-18 10:50:02.064 +10:00 INF] Getting 500 orders to invoice
[2025-06-18 10:50:02.832 +10:00 INF] Found 0 orders to invoice (marked 0 zero-priced as invoiced, filtered 0 candidates)
[2025-06-18 10:50:02.844 +10:00 INF] Getting 100 credits to invoice
[2025-06-18 10:50:02.857 +10:00 INF] Found 0 credits to invoice
[2025-06-18 10:50:02.859 +10:00 INF] Getting 100 refunds to invoice
[2025-06-18 10:50:02.863 +10:00 INF] Found 0 refunds to invoice
[2025-06-18 10:50:02.864 +10:00 INF] No pending work found
[2025-06-18 10:50:02.866 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-18 10:50:02.868 +10:00 INF] Cleaning up invoicer logs...
[2025-06-18 10:50:02.970 +10:00 INF] LEP Invoicer completed successfully in 1049ms. No work to process.
[2025-06-18 10:50:02.978 +10:00 INF] Database connection disposed
[2025-06-18 10:50:02.979 +10:00 INF] LEP Invoicer completed with result: 0
