[2025-06-17 20:29:38.592 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 20:29:38.619 +10:00 INF] Initializing FastReport...
[2025-06-17 20:29:38.690 +10:00 INF] FastReport initialized successfully
[2025-06-17 20:29:39.097 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 20:29:40.458 +10:00 INF] Starting LEP Invoicer at "2025-06-17T20:29:40.4582611+10:00"
[2025-06-17 20:29:40.463 +10:00 INF] Initializing database service...
[2025-06-17 20:29:40.467 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 20:29:40.586 +10:00 INF] Database connection established successfully
[2025-06-17 20:29:40.588 +10:00 INF] Database service initialized successfully
[2025-06-17 20:29:40.602 +10:00 INF] Checking for pending work...
[2025-06-17 20:29:40.608 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 20:29:41.507 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 20:29:41.510 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 20:29:41.523 +10:00 INF] Found 0 credits to invoice
[2025-06-17 20:29:41.526 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 20:29:41.532 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 20:29:41.534 +10:00 INF] No pending work found
[2025-06-17 20:29:41.535 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 20:29:41.536 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 20:29:41.640 +10:00 INF] LEP Invoicer completed successfully in 1181ms. No work to process.
[2025-06-17 20:29:41.666 +10:00 INF] Database connection disposed
[2025-06-17 20:29:41.672 +10:00 INF] LEP Invoicer completed with result: 0
