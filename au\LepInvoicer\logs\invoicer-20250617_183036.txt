[2025-06-17 18:30:36.750 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 18:30:36.795 +10:00 INF] Initializing FastReport...
[2025-06-17 18:30:36.881 +10:00 INF] FastReport initialized successfully
[2025-06-17 18:30:37.528 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 18:30:38.763 +10:00 INF] Starting LEP Invoicer at "2025-06-17T18:30:38.7633984+10:00"
[2025-06-17 18:30:38.767 +10:00 INF] Initializing database service...
[2025-06-17 18:30:38.770 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 18:30:38.867 +10:00 INF] Database connection established successfully
[2025-06-17 18:30:38.868 +10:00 INF] Database service initialized successfully
[2025-06-17 18:30:38.871 +10:00 INF] Checking for pending work...
[2025-06-17 18:30:38.874 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 18:30:39.783 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 18:30:39.786 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 18:30:39.803 +10:00 INF] Found 0 credits to invoice
[2025-06-17 18:30:39.810 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 18:30:39.819 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 18:30:39.822 +10:00 INF] No pending work found
[2025-06-17 18:30:39.824 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 18:30:39.826 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 18:30:39.919 +10:00 INF] LEP Invoicer completed successfully in 1155ms. No work to process.
[2025-06-17 18:30:39.926 +10:00 INF] Database connection disposed
[2025-06-17 18:30:39.928 +10:00 INF] LEP Invoicer completed with result: 0
