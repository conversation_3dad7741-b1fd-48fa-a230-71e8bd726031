[2025-06-10 19:33:40.963 +10:00 INF] Starting LEP Invoicer application
[2025-06-10 19:33:42.025 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-10 19:33:44.597 +10:00 INF] Starting LEP Invoicer at "2025-06-10T19:33:44.5969310+10:00"
[2025-06-10 19:33:44.603 +10:00 INF] Initializing services...
[2025-06-10 19:33:44.610 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-10 19:33:44.793 +10:00 INF] Database connection established successfully
[2025-06-10 19:33:44.813 +10:00 INF] Initializing MYOB service...
[2025-06-10 19:33:44.830 +10:00 INF] Initializing MYOB with OAuth authentication
[2025-06-10 19:33:44.929 +10:00 INF] Starting OAuth authentication flow
[2025-06-10 19:33:45.073 +10:00 INF] Using existing OAuth tokens
[2025-06-10 19:33:45.084 +10:00 INF] Initializing MYOB services with OAuth keystore
[2025-06-10 19:33:45.091 +10:00 INF] MYOB services initialized successfully
[2025-06-10 19:33:45.094 +10:00 INF] OAuth keystore set for API calls
[2025-06-10 19:33:45.096 +10:00 INF] Getting company files from MYOB
[2025-06-10 19:33:45.507 +10:00 INF] MYOB service initialized with OAuth credentials. Using company file: LEP Colour Printers Pty Ltd
[2025-06-10 19:33:45.521 +10:00 INF] Current cache stats: {"AccountsCount":269,"TaxCodesCount":18,"CustomersCount":393,"LastCacheLoad":"2025-06-10T19:31:07.5727667+10:00","CacheAge":"00:02:37.9427903","IsExpired":false,"CacheDirectory":"C:\\Users\\<USER>\\AppData\\Local\\LepInvoicer\\cache"}
[2025-06-10 19:33:45.553 +10:00 INF] Using cached MYOB data: {"AccountsCount":269,"TaxCodesCount":18,"CustomersCount":393,"LastCacheLoad":"2025-06-10T19:31:07.5727667+10:00","CacheAge":"00:02:37.9427903","IsExpired":false,"CacheDirectory":"C:\\Users\\<USER>\\AppData\\Local\\LepInvoicer\\cache"}
[2025-06-10 19:33:46.487 +10:00 INF] Using cached GST tax code
[2025-06-10 19:33:46.490 +10:00 INF] Using cached freight account
[2025-06-10 19:33:46.491 +10:00 INF] Using cached discounts account
[2025-06-10 19:33:46.494 +10:00 INF] MYOB cache initialization complete: {"AccountsCount":269,"TaxCodesCount":18,"CustomersCount":393,"LastCacheLoad":"2025-06-10T19:31:07.5727667+10:00","CacheAge":"00:02:38.9211459","IsExpired":false,"CacheDirectory":"C:\\Users\\<USER>\\AppData\\Local\\LepInvoicer\\cache"}
[2025-06-10 19:33:46.511 +10:00 INF] MYOB service initialized successfully with company file: LEP Colour Printers Pty Ltd
[2025-06-10 19:33:46.513 +10:00 INF] Services initialized successfully
[2025-06-10 19:33:46.519 +10:00 INF] Processing order invoices...
[2025-06-10 19:33:46.524 +10:00 INF] Getting 500 orders to invoice
[2025-06-10 19:33:47.429 +10:00 INF] Found 2 orders to invoice
[2025-06-10 19:33:47.432 +10:00 INF] Found 2 orders to process
[2025-06-10 19:33:47.435 +10:00 INF] Getting order 1417006
[2025-06-10 19:33:47.828 +10:00 INF] Processing order 1417006 with 1 jobs, total: ¤0.00
[2025-06-10 19:33:47.836 +10:00 WRN] Order 1417006 has zero price, marking as invoiced
[2025-06-10 19:33:47.840 +10:00 INF] Getting order 1416838
[2025-06-10 19:33:47.850 +10:00 INF] Processing order 1416838 with 1 jobs, total: ¤0.00
[2025-06-10 19:33:47.854 +10:00 WRN] Order 1416838 has zero price, marking as invoiced
[2025-06-10 19:33:47.857 +10:00 INF] Order processing completed. Processed: 2, Success: 0, Failed: 2
[2025-06-10 19:33:47.863 +10:00 INF] Processing credit invoices...
[2025-06-10 19:33:47.867 +10:00 INF] Getting 100 credits to invoice
[2025-06-10 19:33:47.895 +10:00 INF] Found 0 credits to invoice
[2025-06-10 19:33:47.901 +10:00 INF] Processing refund invoices...
[2025-06-10 19:33:47.904 +10:00 INF] Getting 100 refunds to invoice
[2025-06-10 19:33:47.919 +10:00 INF] Found 0 refunds to invoice
[2025-06-10 19:33:47.921 +10:00 INF] Cleaning up invoicer logs...
[2025-06-10 19:33:47.994 +10:00 INF] LEP Invoicer completed successfully in 3397ms. Orders: 2, Credits: 0, Refunds: 0
[2025-06-10 19:33:48.007 +10:00 INF] Database connection disposed
[2025-06-10 19:33:48.010 +10:00 INF] LEP Invoicer completed with result: 0
