[2025-06-18 08:48:20.743 +10:00 INF] Starting LEP Invoicer application
[2025-06-18 08:48:20.770 +10:00 INF] Initializing FastReport...
[2025-06-18 08:48:20.845 +10:00 INF] FastReport initialized successfully
[2025-06-18 08:48:21.279 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-18 08:48:22.872 +10:00 INF] Starting LEP Invoicer at "2025-06-18T08:48:22.8718872+10:00"
[2025-06-18 08:48:23.448 +10:00 INF] Initializing database service...
[2025-06-18 08:48:23.808 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-18 08:48:24.485 +10:00 INF] Database connection established successfully
[2025-06-18 08:48:24.487 +10:00 INF] Database service initialized successfully
[2025-06-18 08:48:24.491 +10:00 INF] Checking for pending work...
[2025-06-18 08:48:24.500 +10:00 INF] Getting 500 orders to invoice
[2025-06-18 08:48:26.673 +10:00 INF] Found 0 orders to invoice (filtered 16 candidates)
[2025-06-18 08:48:26.676 +10:00 INF] Getting 100 credits to invoice
[2025-06-18 08:48:26.759 +10:00 INF] Found 0 credits to invoice
[2025-06-18 08:48:26.761 +10:00 INF] Getting 100 refunds to invoice
[2025-06-18 08:48:26.767 +10:00 INF] Found 0 refunds to invoice
[2025-06-18 08:48:26.770 +10:00 INF] No pending work found
[2025-06-18 08:48:26.772 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-18 08:48:26.773 +10:00 INF] Cleaning up invoicer logs...
[2025-06-18 08:48:26.900 +10:00 INF] LEP Invoicer completed successfully in 4028ms. No work to process.
[2025-06-18 08:48:26.907 +10:00 INF] Database connection disposed
[2025-06-18 08:48:26.909 +10:00 INF] LEP Invoicer completed with result: 0
