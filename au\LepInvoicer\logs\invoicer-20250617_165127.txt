[2025-06-17 16:51:27.470 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 16:51:27.497 +10:00 INF] Initializing FastReport...
[2025-06-17 16:51:27.569 +10:00 INF] FastReport initialized successfully
[2025-06-17 16:51:27.936 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 16:51:29.263 +10:00 INF] Starting LEP Invoicer at "2025-06-17T16:51:29.2626991+10:00"
[2025-06-17 16:51:29.266 +10:00 INF] Initializing database service...
[2025-06-17 16:51:29.269 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 16:51:29.371 +10:00 INF] Database connection established successfully
[2025-06-17 16:51:29.373 +10:00 INF] Database service initialized successfully
[2025-06-17 16:51:29.376 +10:00 INF] Checking for pending work...
[2025-06-17 16:51:29.379 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 16:51:30.279 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 16:51:30.291 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 16:51:30.308 +10:00 INF] Found 0 credits to invoice
[2025-06-17 16:51:30.315 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 16:51:30.319 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 16:51:30.326 +10:00 INF] No pending work found
[2025-06-17 16:51:30.328 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 16:51:30.331 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 16:51:30.412 +10:00 INF] LEP Invoicer completed successfully in 1149ms. No work to process.
[2025-06-17 16:51:30.418 +10:00 INF] Database connection disposed
[2025-06-17 16:51:30.420 +10:00 INF] LEP Invoicer completed with result: 0
