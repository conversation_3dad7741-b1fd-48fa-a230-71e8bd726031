[2025-06-17 16:55:47.516 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 16:55:47.545 +10:00 INF] Initializing FastReport...
[2025-06-17 16:55:47.624 +10:00 INF] FastReport initialized successfully
[2025-06-17 16:55:48.065 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 16:55:49.408 +10:00 INF] Starting LEP Invoicer at "2025-06-17T16:55:49.4085013+10:00"
[2025-06-17 16:55:49.412 +10:00 INF] Initializing database service...
[2025-06-17 16:55:49.426 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 16:55:49.538 +10:00 INF] Database connection established successfully
[2025-06-17 16:55:49.540 +10:00 INF] Database service initialized successfully
[2025-06-17 16:55:49.543 +10:00 INF] Checking for pending work...
[2025-06-17 16:55:49.546 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 16:55:50.442 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 16:55:50.444 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 16:55:50.458 +10:00 INF] Found 0 credits to invoice
[2025-06-17 16:55:50.460 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 16:55:50.466 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 16:55:50.467 +10:00 INF] No pending work found
[2025-06-17 16:55:50.470 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 16:55:50.474 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 16:55:50.558 +10:00 INF] LEP Invoicer completed successfully in 1150ms. No work to process.
[2025-06-17 16:55:50.567 +10:00 INF] Database connection disposed
[2025-06-17 16:55:50.570 +10:00 INF] LEP Invoicer completed with result: 0
