[2025-06-17 20:01:13.638 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 20:01:13.678 +10:00 INF] Initializing FastReport...
[2025-06-17 20:01:13.760 +10:00 INF] FastReport initialized successfully
[2025-06-17 20:01:14.211 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 20:01:15.484 +10:00 INF] Starting LEP Invoicer at "2025-06-17T20:01:15.4841755+10:00"
[2025-06-17 20:01:15.488 +10:00 INF] Initializing database service...
[2025-06-17 20:01:15.491 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 20:01:15.603 +10:00 INF] Database connection established successfully
[2025-06-17 20:01:15.607 +10:00 INF] Database service initialized successfully
[2025-06-17 20:01:15.610 +10:00 INF] Checking for pending work...
[2025-06-17 20:01:15.613 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 20:01:16.497 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 20:01:16.500 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 20:01:16.512 +10:00 INF] Found 0 credits to invoice
[2025-06-17 20:01:16.514 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 20:01:16.517 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 20:01:16.519 +10:00 INF] No pending work found
[2025-06-17 20:01:16.520 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 20:01:16.521 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 20:01:16.599 +10:00 INF] LEP Invoicer completed successfully in 1115ms. No work to process.
[2025-06-17 20:01:16.605 +10:00 INF] Database connection disposed
[2025-06-17 20:01:16.607 +10:00 INF] LEP Invoicer completed with result: 0
