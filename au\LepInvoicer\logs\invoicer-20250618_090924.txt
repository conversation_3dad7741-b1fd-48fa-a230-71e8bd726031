[2025-06-18 09:09:24.541 +10:00 INF] Starting LEP Invoicer application
[2025-06-18 09:09:24.574 +10:00 INF] Initializing FastReport...
[2025-06-18 09:09:24.653 +10:00 INF] FastReport initialized successfully
[2025-06-18 09:09:25.085 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-18 09:09:26.395 +10:00 INF] Starting LEP Invoicer at "2025-06-18T09:09:26.3949792+10:00"
[2025-06-18 09:09:26.399 +10:00 INF] Initializing database service...
[2025-06-18 09:09:26.402 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-18 09:09:26.501 +10:00 INF] Database connection established successfully
[2025-06-18 09:09:26.502 +10:00 INF] Database service initialized successfully
[2025-06-18 09:09:26.505 +10:00 INF] Checking for pending work...
[2025-06-18 09:09:26.508 +10:00 INF] Getting 500 orders to invoice
[2025-06-18 09:09:27.419 +10:00 INF] Found 0 orders to invoice (filtered 16 candidates)
[2025-06-18 09:09:27.422 +10:00 INF] Getting 100 credits to invoice
[2025-06-18 09:09:27.439 +10:00 INF] Found 0 credits to invoice
[2025-06-18 09:09:27.445 +10:00 INF] Getting 100 refunds to invoice
[2025-06-18 09:09:27.448 +10:00 INF] Found 0 refunds to invoice
[2025-06-18 09:09:27.453 +10:00 INF] No pending work found
[2025-06-18 09:09:27.455 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-18 09:09:27.456 +10:00 INF] Cleaning up invoicer logs...
[2025-06-18 09:09:27.553 +10:00 INF] LEP Invoicer completed successfully in 1158ms. No work to process.
[2025-06-18 09:09:27.559 +10:00 INF] Database connection disposed
[2025-06-18 09:09:27.561 +10:00 INF] LEP Invoicer completed with result: 0
