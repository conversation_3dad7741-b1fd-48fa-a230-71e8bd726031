[2025-06-17 19:12:07.690 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 19:12:07.740 +10:00 INF] Initializing FastReport...
[2025-06-17 19:12:07.831 +10:00 INF] FastReport initialized successfully
[2025-06-17 19:12:08.448 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 19:12:09.897 +10:00 INF] Starting LEP Invoicer at "2025-06-17T19:12:09.8967925+10:00"
[2025-06-17 19:12:09.902 +10:00 INF] Initializing database service...
[2025-06-17 19:12:09.910 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 19:12:10.037 +10:00 INF] Database connection established successfully
[2025-06-17 19:12:10.039 +10:00 INF] Database service initialized successfully
[2025-06-17 19:12:10.042 +10:00 INF] Checking for pending work...
[2025-06-17 19:12:10.045 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 19:12:11.095 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 19:12:11.099 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 19:12:11.112 +10:00 INF] Found 0 credits to invoice
[2025-06-17 19:12:11.114 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 19:12:11.120 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 19:12:11.122 +10:00 INF] No pending work found
[2025-06-17 19:12:11.124 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 19:12:11.127 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 19:12:11.227 +10:00 INF] LEP Invoicer completed successfully in 1330ms. No work to process.
[2025-06-17 19:12:11.241 +10:00 INF] Database connection disposed
[2025-06-17 19:12:11.243 +10:00 INF] LEP Invoicer completed with result: 0
