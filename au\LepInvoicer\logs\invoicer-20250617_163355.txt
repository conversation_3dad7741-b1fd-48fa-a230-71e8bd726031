[2025-06-17 16:33:55.783 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 16:33:55.810 +10:00 INF] Initializing FastReport...
[2025-06-17 16:33:55.891 +10:00 INF] FastReport initialized successfully
[2025-06-17 16:33:56.328 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 16:33:57.672 +10:00 INF] Starting LEP Invoicer at "2025-06-17T16:33:57.6722966+10:00"
[2025-06-17 16:33:57.676 +10:00 INF] Initializing database service...
[2025-06-17 16:33:57.678 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 16:33:57.872 +10:00 INF] Database connection established successfully
[2025-06-17 16:33:57.874 +10:00 INF] Database service initialized successfully
[2025-06-17 16:33:57.876 +10:00 INF] Checking for pending work...
[2025-06-17 16:33:57.879 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 16:33:58.850 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 16:33:58.852 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 16:33:58.865 +10:00 INF] Found 0 credits to invoice
[2025-06-17 16:33:58.869 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 16:33:58.875 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 16:33:58.876 +10:00 INF] No pending work found
[2025-06-17 16:33:58.877 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 16:33:58.879 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 16:33:58.975 +10:00 INF] LEP Invoicer completed successfully in 1303ms. No work to process.
[2025-06-17 16:33:59.007 +10:00 INF] Database connection disposed
[2025-06-17 16:33:59.017 +10:00 INF] LEP Invoicer completed with result: 0
