[2025-06-17 20:05:36.673 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 20:05:36.704 +10:00 INF] Initializing FastReport...
[2025-06-17 20:05:36.784 +10:00 INF] FastReport initialized successfully
[2025-06-17 20:05:37.177 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 20:05:38.457 +10:00 INF] Starting LEP Invoicer at "2025-06-17T20:05:38.4572178+10:00"
[2025-06-17 20:05:38.461 +10:00 INF] Initializing database service...
[2025-06-17 20:05:38.464 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 20:05:38.582 +10:00 INF] Database connection established successfully
[2025-06-17 20:05:38.583 +10:00 INF] Database service initialized successfully
[2025-06-17 20:05:38.586 +10:00 INF] Checking for pending work...
[2025-06-17 20:05:38.589 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 20:05:39.532 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 20:05:39.536 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 20:05:39.549 +10:00 INF] Found 0 credits to invoice
[2025-06-17 20:05:39.551 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 20:05:39.561 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 20:05:39.563 +10:00 INF] No pending work found
[2025-06-17 20:05:39.565 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 20:05:39.567 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 20:05:39.651 +10:00 INF] LEP Invoicer completed successfully in 1194ms. No work to process.
[2025-06-17 20:05:39.657 +10:00 INF] Database connection disposed
[2025-06-17 20:05:39.659 +10:00 INF] LEP Invoicer completed with result: 0
