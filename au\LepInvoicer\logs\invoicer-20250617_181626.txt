[2025-06-17 18:16:26.463 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 18:16:26.503 +10:00 INF] Initializing FastReport...
[2025-06-17 18:16:26.580 +10:00 INF] FastReport initialized successfully
[2025-06-17 18:16:26.998 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 18:16:28.226 +10:00 INF] Starting LEP Invoicer at "2025-06-17T18:16:28.2264240+10:00"
[2025-06-17 18:16:28.230 +10:00 INF] Initializing database service...
[2025-06-17 18:16:28.233 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 18:16:28.333 +10:00 INF] Database connection established successfully
[2025-06-17 18:16:28.334 +10:00 INF] Database service initialized successfully
[2025-06-17 18:16:28.337 +10:00 INF] Checking for pending work...
[2025-06-17 18:16:28.340 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 18:16:29.242 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 18:16:29.245 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 18:16:29.258 +10:00 INF] Found 0 credits to invoice
[2025-06-17 18:16:29.260 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 18:16:29.265 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 18:16:29.292 +10:00 INF] No pending work found
[2025-06-17 18:16:29.295 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 18:16:29.297 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 18:16:29.379 +10:00 INF] LEP Invoicer completed successfully in 1152ms. No work to process.
[2025-06-17 18:16:29.386 +10:00 INF] Database connection disposed
[2025-06-17 18:16:29.387 +10:00 INF] LEP Invoicer completed with result: 0
