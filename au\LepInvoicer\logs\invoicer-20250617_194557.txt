[2025-06-17 19:45:57.588 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 19:45:57.620 +10:00 INF] Initializing FastReport...
[2025-06-17 19:45:57.691 +10:00 INF] FastReport initialized successfully
[2025-06-17 19:45:58.104 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 19:45:59.458 +10:00 INF] Starting LEP Invoicer at "2025-06-17T19:45:59.4583560+10:00"
[2025-06-17 19:45:59.462 +10:00 INF] Initializing database service...
[2025-06-17 19:45:59.465 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 19:45:59.561 +10:00 INF] Database connection established successfully
[2025-06-17 19:45:59.562 +10:00 INF] Database service initialized successfully
[2025-06-17 19:45:59.565 +10:00 INF] Checking for pending work...
[2025-06-17 19:45:59.567 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 19:46:00.468 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 19:46:00.471 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 19:46:00.487 +10:00 INF] Found 0 credits to invoice
[2025-06-17 19:46:00.489 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 19:46:00.493 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 19:46:00.494 +10:00 INF] No pending work found
[2025-06-17 19:46:00.495 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 19:46:00.499 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 19:46:00.581 +10:00 INF] LEP Invoicer completed successfully in 1122ms. No work to process.
[2025-06-17 19:46:00.587 +10:00 INF] Database connection disposed
[2025-06-17 19:46:00.589 +10:00 INF] LEP Invoicer completed with result: 0
