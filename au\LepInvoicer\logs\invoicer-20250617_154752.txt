[2025-06-17 15:47:52.455 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 15:47:52.494 +10:00 INF] Initializing FastReport...
[2025-06-17 15:47:52.591 +10:00 INF] FastReport initialized successfully
[2025-06-17 15:47:52.998 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 15:47:54.236 +10:00 INF] Starting LEP Invoicer at "2025-06-17T15:47:54.2364239+10:00"
[2025-06-17 15:47:54.240 +10:00 INF] Initializing database service...
[2025-06-17 15:47:54.243 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 15:47:54.365 +10:00 INF] Database connection established successfully
[2025-06-17 15:47:54.367 +10:00 INF] Database service initialized successfully
[2025-06-17 15:47:54.373 +10:00 INF] Checking for pending work...
[2025-06-17 15:47:54.376 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 15:47:55.253 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 15:47:55.256 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 15:47:55.269 +10:00 INF] Found 0 credits to invoice
[2025-06-17 15:47:55.271 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 15:47:55.275 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 15:47:55.277 +10:00 INF] No pending work found
[2025-06-17 15:47:55.278 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 15:47:55.279 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 15:47:55.352 +10:00 INF] LEP Invoicer completed successfully in 1116ms. No work to process.
[2025-06-17 15:47:55.358 +10:00 INF] Database connection disposed
[2025-06-17 15:47:55.360 +10:00 INF] LEP Invoicer completed with result: 0
