[2025-06-18 10:12:20.015 +10:00 INF] Starting LEP Invoicer application
[2025-06-18 10:12:20.046 +10:00 INF] Initializing FastReport...
[2025-06-18 10:12:20.154 +10:00 INF] FastReport initialized successfully
[2025-06-18 10:12:20.824 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-18 10:12:22.859 +10:00 INF] Starting LEP Invoicer at "2025-06-18T10:12:22.8586773+10:00"
[2025-06-18 10:12:22.863 +10:00 INF] Initializing database service...
[2025-06-18 10:12:22.872 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-18 10:12:23.057 +10:00 INF] Database connection established successfully
[2025-06-18 10:12:23.075 +10:00 INF] Database service initialized successfully
[2025-06-18 10:12:23.082 +10:00 INF] Checking for pending work...
[2025-06-18 10:12:23.085 +10:00 INF] Getting 500 orders to invoice
[2025-06-18 10:12:24.595 +10:00 INF] Found 0 orders to invoice (filtered 16 candidates)
[2025-06-18 10:12:24.599 +10:00 INF] Getting 100 credits to invoice
[2025-06-18 10:12:24.632 +10:00 INF] Found 0 credits to invoice
[2025-06-18 10:12:24.634 +10:00 INF] Getting 100 refunds to invoice
[2025-06-18 10:12:24.651 +10:00 INF] Found 0 refunds to invoice
[2025-06-18 10:12:24.668 +10:00 INF] No pending work found
[2025-06-18 10:12:24.670 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-18 10:12:24.674 +10:00 INF] Cleaning up invoicer logs...
[2025-06-18 10:12:24.827 +10:00 INF] LEP Invoicer completed successfully in 1968ms. No work to process.
[2025-06-18 10:12:24.846 +10:00 INF] Database connection disposed
[2025-06-18 10:12:24.849 +10:00 INF] LEP Invoicer completed with result: 0
