[2025-06-17 16:50:22.544 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 16:50:22.571 +10:00 INF] Initializing FastReport...
[2025-06-17 16:50:22.642 +10:00 INF] FastReport initialized successfully
[2025-06-17 16:50:23.065 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 16:50:24.285 +10:00 INF] Starting LEP Invoicer at "2025-06-17T16:50:24.2850248+10:00"
[2025-06-17 16:50:24.288 +10:00 INF] Initializing database service...
[2025-06-17 16:50:24.291 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 16:50:24.392 +10:00 INF] Database connection established successfully
[2025-06-17 16:50:24.393 +10:00 INF] Database service initialized successfully
[2025-06-17 16:50:24.396 +10:00 INF] Checking for pending work...
[2025-06-17 16:50:24.399 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 16:50:25.362 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 16:50:25.364 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 16:50:25.379 +10:00 INF] Found 0 credits to invoice
[2025-06-17 16:50:25.381 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 16:50:25.387 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 16:50:25.389 +10:00 INF] No pending work found
[2025-06-17 16:50:25.392 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 16:50:25.395 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 16:50:25.482 +10:00 INF] LEP Invoicer completed successfully in 1197ms. No work to process.
[2025-06-17 16:50:25.488 +10:00 INF] Database connection disposed
[2025-06-17 16:50:25.490 +10:00 INF] LEP Invoicer completed with result: 0
