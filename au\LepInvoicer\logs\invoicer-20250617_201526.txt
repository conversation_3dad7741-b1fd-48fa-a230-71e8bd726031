[2025-06-17 20:15:26.511 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 20:15:26.541 +10:00 INF] Initializing FastReport...
[2025-06-17 20:15:26.621 +10:00 INF] FastReport initialized successfully
[2025-06-17 20:15:27.030 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 20:15:28.239 +10:00 INF] Starting LEP Invoicer at "2025-06-17T20:15:28.2389961+10:00"
[2025-06-17 20:15:28.242 +10:00 INF] Initializing database service...
[2025-06-17 20:15:28.245 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 20:15:28.343 +10:00 INF] Database connection established successfully
[2025-06-17 20:15:28.345 +10:00 INF] Database service initialized successfully
[2025-06-17 20:15:28.347 +10:00 INF] Checking for pending work...
[2025-06-17 20:15:28.350 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 20:15:29.277 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 20:15:29.280 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 20:15:29.297 +10:00 INF] Found 0 credits to invoice
[2025-06-17 20:15:29.300 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 20:15:29.303 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 20:15:29.305 +10:00 INF] No pending work found
[2025-06-17 20:15:29.306 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 20:15:29.310 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 20:15:29.386 +10:00 INF] LEP Invoicer completed successfully in 1146ms. No work to process.
[2025-06-17 20:15:29.392 +10:00 INF] Database connection disposed
[2025-06-17 20:15:29.394 +10:00 INF] LEP Invoicer completed with result: 0
