[2025-06-18 08:12:02.855 +10:00 INF] Starting LEP Invoicer application
[2025-06-18 08:12:02.899 +10:00 INF] Initializing FastReport...
[2025-06-18 08:12:02.985 +10:00 INF] FastReport initialized successfully
[2025-06-18 08:12:03.461 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-18 08:12:04.977 +10:00 INF] Starting LEP Invoicer at "2025-06-18T08:12:04.9767507+10:00"
[2025-06-18 08:12:04.980 +10:00 INF] Initializing database service...
[2025-06-18 08:12:04.983 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-18 08:12:05.089 +10:00 INF] Database connection established successfully
[2025-06-18 08:12:05.091 +10:00 INF] Database service initialized successfully
[2025-06-18 08:12:05.094 +10:00 INF] Checking for pending work...
[2025-06-18 08:12:05.097 +10:00 INF] Getting 500 orders to invoice
[2025-06-18 08:12:06.023 +10:00 INF] Found 0 orders to invoice (filtered 16 candidates)
[2025-06-18 08:12:06.026 +10:00 INF] Getting 100 credits to invoice
[2025-06-18 08:12:06.039 +10:00 INF] Found 0 credits to invoice
[2025-06-18 08:12:06.041 +10:00 INF] Getting 100 refunds to invoice
[2025-06-18 08:12:06.047 +10:00 INF] Found 0 refunds to invoice
[2025-06-18 08:12:06.048 +10:00 INF] No pending work found
[2025-06-18 08:12:06.049 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-18 08:12:06.051 +10:00 INF] Cleaning up invoicer logs...
[2025-06-18 08:12:06.131 +10:00 INF] LEP Invoicer completed successfully in 1154ms. No work to process.
[2025-06-18 08:12:06.137 +10:00 INF] Database connection disposed
[2025-06-18 08:12:06.139 +10:00 INF] LEP Invoicer completed with result: 0
