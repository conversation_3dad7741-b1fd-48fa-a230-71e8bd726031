[2025-06-17 20:28:32.650 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 20:28:32.680 +10:00 INF] Initializing FastReport...
[2025-06-17 20:28:32.751 +10:00 INF] FastReport initialized successfully
[2025-06-17 20:28:33.147 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 20:28:34.472 +10:00 INF] Starting LEP Invoicer at "2025-06-17T20:28:34.4720551+10:00"
[2025-06-17 20:28:34.476 +10:00 INF] Initializing database service...
[2025-06-17 20:28:34.479 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 20:28:34.577 +10:00 INF] Database connection established successfully
[2025-06-17 20:28:34.579 +10:00 INF] Database service initialized successfully
[2025-06-17 20:28:34.583 +10:00 INF] Checking for pending work...
[2025-06-17 20:28:34.586 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 20:28:35.475 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 20:28:35.478 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 20:28:35.497 +10:00 INF] Found 0 credits to invoice
[2025-06-17 20:28:35.499 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 20:28:35.503 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 20:28:35.505 +10:00 INF] No pending work found
[2025-06-17 20:28:35.509 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 20:28:35.511 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 20:28:35.622 +10:00 INF] LEP Invoicer completed successfully in 1150ms. No work to process.
[2025-06-17 20:28:35.630 +10:00 INF] Database connection disposed
[2025-06-17 20:28:35.634 +10:00 INF] LEP Invoicer completed with result: 0
