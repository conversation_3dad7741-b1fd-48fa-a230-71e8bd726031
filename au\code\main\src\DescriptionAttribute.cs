using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;

namespace lep
{
    public static class L
    {
        public static dynamic Main()
        {
            var enumns = Assembly.GetExecutingAssembly().GetTypes().Where(t => t.IsEnum)
                .Where(t => !t.Namespace.StartsWith("lep.configuration")).OrderBy(x => x.Name).ToArray();
            //dynamic d = new Dictionary<string, object>();

            var vk = new Dictionary<object, object>();
            var vd = new Dictionary<object, object>();
            var konst = new Dictionary<object, object>();

            foreach (var e in enumns)
            {
                vk[e.Name] = new Dictionary<object, object>();
                vd[e.Name] = new Dictionary<object, object>();
                konst[e.Name] = new Dictionary<object, object>();

                var fields = e.GetFields();
                foreach (var field in fields)
                {
                    if (field.Name.Equals("value__")) continue;

                    var das = field.GetCustomAttributes<DescriptionAttribute>().ToArray();
                    var des = "";
                    if (das.Length == 0)
                        des = field.Name;
                    else
                        des = das[0].DefaultDescription;

                    var val = field.GetRawConstantValue();

                    ((IDictionary<object, object>)vk[e.Name])[val] = field.Name;

                    ((IDictionary<object, object>)vd[e.Name])[val] = des;
                    ((IDictionary<object, object>)konst[e.Name])[field.Name] = field.GetRawConstantValue();
                }
            }

            dynamic d = new
            {
                ValueKey = vk,
                ValueDesc = vd,
                KeyVal = konst
            };

            return d;
        }
    }

    [AttributeUsage(AttributeTargets.Field)]
    public class DescriptionAttribute : Attribute
    {
        public DescriptionAttribute(string defaultDesc)
        {
            DefaultDescription = defaultDesc;
            CustomerDescription = defaultDesc;
        }

        public DescriptionAttribute(string defaultDesc, string customerDesc)
        {
            DefaultDescription = defaultDesc;
            CustomerDescription = customerDesc;
        }

        public string DefaultDescription { get; set; }
        public string CustomerDescription { get; set; }
    }
}