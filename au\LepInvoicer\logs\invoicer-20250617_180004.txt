[2025-06-17 18:00:04.821 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 18:00:04.861 +10:00 INF] Initializing FastReport...
[2025-06-17 18:00:04.948 +10:00 INF] FastReport initialized successfully
[2025-06-17 18:00:05.526 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 18:00:06.865 +10:00 INF] Starting LEP Invoicer at "2025-06-17T18:00:06.8647164+10:00"
[2025-06-17 18:00:06.868 +10:00 INF] Initializing database service...
[2025-06-17 18:00:06.871 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 18:00:06.974 +10:00 INF] Database connection established successfully
[2025-06-17 18:00:06.975 +10:00 INF] Database service initialized successfully
[2025-06-17 18:00:06.979 +10:00 INF] Checking for pending work...
[2025-06-17 18:00:06.982 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 18:00:07.847 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 18:00:07.851 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 18:00:07.867 +10:00 INF] Found 0 credits to invoice
[2025-06-17 18:00:07.872 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 18:00:07.877 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 18:00:07.881 +10:00 INF] No pending work found
[2025-06-17 18:00:07.883 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 18:00:07.884 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 18:00:07.958 +10:00 INF] LEP Invoicer completed successfully in 1094ms. No work to process.
[2025-06-17 18:00:07.974 +10:00 INF] Database connection disposed
[2025-06-17 18:00:07.976 +10:00 INF] LEP Invoicer completed with result: 0
