[2025-06-17 17:27:21.425 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 17:27:21.467 +10:00 INF] Initializing FastReport...
[2025-06-17 17:27:21.548 +10:00 INF] FastReport initialized successfully
[2025-06-17 17:27:21.949 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 17:27:23.212 +10:00 INF] Starting LEP Invoicer at "2025-06-17T17:27:23.2122042+10:00"
[2025-06-17 17:27:23.216 +10:00 INF] Initializing database service...
[2025-06-17 17:27:23.218 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 17:27:23.317 +10:00 INF] Database connection established successfully
[2025-06-17 17:27:23.318 +10:00 INF] Database service initialized successfully
[2025-06-17 17:27:23.321 +10:00 INF] Checking for pending work...
[2025-06-17 17:27:23.325 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 17:27:24.184 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 17:27:24.187 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 17:27:24.200 +10:00 INF] Found 0 credits to invoice
[2025-06-17 17:27:24.202 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 17:27:24.205 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 17:27:24.207 +10:00 INF] No pending work found
[2025-06-17 17:27:24.210 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 17:27:24.211 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 17:27:24.283 +10:00 INF] LEP Invoicer completed successfully in 1070ms. No work to process.
[2025-06-17 17:27:24.289 +10:00 INF] Database connection disposed
[2025-06-17 17:27:24.304 +10:00 INF] LEP Invoicer completed with result: 0
