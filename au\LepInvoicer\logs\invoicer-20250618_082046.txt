[2025-06-18 08:20:46.720 +10:00 INF] Starting LEP Invoicer application
[2025-06-18 08:20:46.770 +10:00 INF] Initializing FastReport...
[2025-06-18 08:20:46.858 +10:00 INF] FastReport initialized successfully
[2025-06-18 08:20:47.279 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-18 08:20:48.615 +10:00 INF] Starting LEP Invoicer at "2025-06-18T08:20:48.6147456+10:00"
[2025-06-18 08:20:48.618 +10:00 INF] Initializing database service...
[2025-06-18 08:20:48.621 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-18 08:20:48.721 +10:00 INF] Database connection established successfully
[2025-06-18 08:20:48.722 +10:00 INF] Database service initialized successfully
[2025-06-18 08:20:48.725 +10:00 INF] Checking for pending work...
[2025-06-18 08:20:48.728 +10:00 INF] Getting 500 orders to invoice
[2025-06-18 08:20:49.586 +10:00 INF] Found 0 orders to invoice (filtered 16 candidates)
[2025-06-18 08:20:49.591 +10:00 INF] Getting 100 credits to invoice
[2025-06-18 08:20:49.604 +10:00 INF] Found 0 credits to invoice
[2025-06-18 08:20:49.606 +10:00 INF] Getting 100 refunds to invoice
[2025-06-18 08:20:49.611 +10:00 INF] Found 0 refunds to invoice
[2025-06-18 08:20:49.613 +10:00 INF] No pending work found
[2025-06-18 08:20:49.614 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-18 08:20:49.616 +10:00 INF] Cleaning up invoicer logs...
[2025-06-18 08:20:49.703 +10:00 INF] LEP Invoicer completed successfully in 1088ms. No work to process.
[2025-06-18 08:20:49.709 +10:00 INF] Database connection disposed
[2025-06-18 08:20:49.712 +10:00 INF] LEP Invoicer completed with result: 0
