[2025-06-17 18:12:05.228 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 18:12:05.256 +10:00 INF] Initializing FastReport...
[2025-06-17 18:12:05.334 +10:00 INF] FastReport initialized successfully
[2025-06-17 18:12:05.801 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 18:12:07.247 +10:00 INF] Starting LEP Invoicer at "2025-06-17T18:12:07.2473934+10:00"
[2025-06-17 18:12:07.252 +10:00 INF] Initializing database service...
[2025-06-17 18:12:07.255 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 18:12:07.388 +10:00 INF] Database connection established successfully
[2025-06-17 18:12:07.390 +10:00 INF] Database service initialized successfully
[2025-06-17 18:12:07.393 +10:00 INF] Checking for pending work...
[2025-06-17 18:12:07.398 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 18:12:08.373 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 18:12:08.377 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 18:12:08.390 +10:00 INF] Found 0 credits to invoice
[2025-06-17 18:12:08.392 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 18:12:08.397 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 18:12:08.399 +10:00 INF] No pending work found
[2025-06-17 18:12:08.400 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 18:12:08.402 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 18:12:08.480 +10:00 INF] LEP Invoicer completed successfully in 1232ms. No work to process.
[2025-06-17 18:12:08.487 +10:00 INF] Database connection disposed
[2025-06-17 18:12:08.503 +10:00 INF] LEP Invoicer completed with result: 0
