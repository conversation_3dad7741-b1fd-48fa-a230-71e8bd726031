[2025-06-17 19:06:41.500 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 19:06:41.529 +10:00 INF] Initializing FastReport...
[2025-06-17 19:06:41.603 +10:00 INF] FastReport initialized successfully
[2025-06-17 19:06:41.961 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 19:06:43.113 +10:00 INF] Starting LEP Invoicer at "2025-06-17T19:06:43.1132505+10:00"
[2025-06-17 19:06:43.125 +10:00 INF] Initializing database service...
[2025-06-17 19:06:43.130 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 19:06:43.233 +10:00 INF] Database connection established successfully
[2025-06-17 19:06:43.242 +10:00 INF] Database service initialized successfully
[2025-06-17 19:06:43.245 +10:00 INF] Checking for pending work...
[2025-06-17 19:06:43.248 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 19:06:44.214 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 19:06:44.217 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 19:06:44.229 +10:00 INF] Found 0 credits to invoice
[2025-06-17 19:06:44.231 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 19:06:44.235 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 19:06:44.237 +10:00 INF] No pending work found
[2025-06-17 19:06:44.238 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 19:06:44.257 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 19:06:44.343 +10:00 INF] LEP Invoicer completed successfully in 1229ms. No work to process.
[2025-06-17 19:06:44.349 +10:00 INF] Database connection disposed
[2025-06-17 19:06:44.351 +10:00 INF] LEP Invoicer completed with result: 0
