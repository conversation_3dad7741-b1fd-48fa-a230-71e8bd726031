[2025-06-17 17:48:06.541 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 17:48:06.571 +10:00 INF] Initializing FastReport...
[2025-06-17 17:48:06.645 +10:00 INF] FastReport initialized successfully
[2025-06-17 17:48:07.082 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 17:48:08.447 +10:00 INF] Starting LEP Invoicer at "2025-06-17T17:48:08.4476057+10:00"
[2025-06-17 17:48:08.451 +10:00 INF] Initializing database service...
[2025-06-17 17:48:08.456 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 17:48:08.573 +10:00 INF] Database connection established successfully
[2025-06-17 17:48:08.575 +10:00 INF] Database service initialized successfully
[2025-06-17 17:48:08.587 +10:00 INF] Checking for pending work...
[2025-06-17 17:48:08.590 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 17:48:09.583 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 17:48:09.587 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 17:48:09.601 +10:00 INF] Found 0 credits to invoice
[2025-06-17 17:48:09.603 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 17:48:09.606 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 17:48:09.608 +10:00 INF] No pending work found
[2025-06-17 17:48:09.609 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 17:48:09.610 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 17:48:09.692 +10:00 INF] LEP Invoicer completed successfully in 1244ms. No work to process.
[2025-06-17 17:48:09.698 +10:00 INF] Database connection disposed
[2025-06-17 17:48:09.700 +10:00 INF] LEP Invoicer completed with result: 0
