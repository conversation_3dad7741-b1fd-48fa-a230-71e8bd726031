[2025-06-17 19:49:13.576 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 19:49:13.605 +10:00 INF] Initializing FastReport...
[2025-06-17 19:49:13.690 +10:00 INF] FastReport initialized successfully
[2025-06-17 19:49:14.242 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 19:49:15.589 +10:00 INF] Starting LEP Invoicer at "2025-06-17T19:49:15.5896236+10:00"
[2025-06-17 19:49:15.594 +10:00 INF] Initializing database service...
[2025-06-17 19:49:15.599 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 19:49:15.738 +10:00 INF] Database connection established successfully
[2025-06-17 19:49:15.742 +10:00 INF] Database service initialized successfully
[2025-06-17 19:49:15.746 +10:00 INF] Checking for pending work...
[2025-06-17 19:49:15.754 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 19:49:16.811 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 19:49:16.814 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 19:49:16.827 +10:00 INF] Found 0 credits to invoice
[2025-06-17 19:49:16.830 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 19:49:16.837 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 19:49:16.838 +10:00 INF] No pending work found
[2025-06-17 19:49:16.839 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 19:49:16.841 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 19:49:16.916 +10:00 INF] LEP Invoicer completed successfully in 1326ms. No work to process.
[2025-06-17 19:49:16.923 +10:00 INF] Database connection disposed
[2025-06-17 19:49:16.925 +10:00 INF] LEP Invoicer completed with result: 0
