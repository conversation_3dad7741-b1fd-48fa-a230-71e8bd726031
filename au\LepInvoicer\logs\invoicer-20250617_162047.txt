[2025-06-17 16:20:47.447 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 16:20:47.478 +10:00 INF] Initializing FastReport...
[2025-06-17 16:20:47.584 +10:00 INF] FastReport initialized successfully
[2025-06-17 16:20:48.105 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 16:20:49.374 +10:00 INF] Starting LEP Invoicer at "2025-06-17T16:20:49.3741435+10:00"
[2025-06-17 16:20:49.379 +10:00 INF] Initializing database service...
[2025-06-17 16:20:49.383 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 16:20:49.486 +10:00 INF] Database connection established successfully
[2025-06-17 16:20:49.488 +10:00 INF] Database service initialized successfully
[2025-06-17 16:20:49.491 +10:00 INF] Checking for pending work...
[2025-06-17 16:20:49.493 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 16:20:50.406 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 16:20:50.409 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 16:20:50.425 +10:00 INF] Found 0 credits to invoice
[2025-06-17 16:20:50.427 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 16:20:50.432 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 16:20:50.433 +10:00 INF] No pending work found
[2025-06-17 16:20:50.436 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 16:20:50.438 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 16:20:50.522 +10:00 INF] LEP Invoicer completed successfully in 1147ms. No work to process.
[2025-06-17 16:20:50.529 +10:00 INF] Database connection disposed
[2025-06-17 16:20:50.531 +10:00 INF] LEP Invoicer completed with result: 0
