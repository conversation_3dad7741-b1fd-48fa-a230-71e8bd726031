[2025-06-17 15:37:59.581 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 15:37:59.610 +10:00 INF] Initializing FastReport...
[2025-06-17 15:37:59.686 +10:00 INF] FastReport initialized successfully
[2025-06-17 15:38:00.104 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 15:38:01.498 +10:00 INF] Starting LEP Invoicer at "2025-06-17T15:38:01.4986436+10:00"
[2025-06-17 15:38:01.504 +10:00 INF] Initializing database service...
[2025-06-17 15:38:01.507 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 15:38:01.644 +10:00 INF] Database connection established successfully
[2025-06-17 15:38:01.646 +10:00 INF] Database service initialized successfully
[2025-06-17 15:38:01.656 +10:00 INF] Checking for pending work...
[2025-06-17 15:38:01.661 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 15:38:02.720 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 15:38:02.723 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 15:38:02.737 +10:00 INF] Found 0 credits to invoice
[2025-06-17 15:38:02.747 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 15:38:02.751 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 15:38:02.752 +10:00 INF] No pending work found
[2025-06-17 15:38:02.755 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 15:38:02.757 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 15:38:02.847 +10:00 INF] LEP Invoicer completed successfully in 1348ms. No work to process.
[2025-06-17 15:38:02.864 +10:00 INF] Database connection disposed
[2025-06-17 15:38:02.869 +10:00 INF] LEP Invoicer completed with result: 0
