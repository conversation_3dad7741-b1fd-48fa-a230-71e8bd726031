[2025-06-17 16:04:19.506 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 16:04:19.534 +10:00 INF] Initializing FastReport...
[2025-06-17 16:04:19.607 +10:00 INF] FastReport initialized successfully
[2025-06-17 16:04:20.028 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 16:04:21.283 +10:00 INF] Starting LEP Invoicer at "2025-06-17T16:04:21.2829858+10:00"
[2025-06-17 16:04:21.288 +10:00 INF] Initializing database service...
[2025-06-17 16:04:21.291 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 16:04:21.394 +10:00 INF] Database connection established successfully
[2025-06-17 16:04:21.395 +10:00 INF] Database service initialized successfully
[2025-06-17 16:04:21.399 +10:00 INF] Checking for pending work...
[2025-06-17 16:04:21.402 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 16:04:22.337 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 16:04:22.340 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 16:04:22.351 +10:00 INF] Found 0 credits to invoice
[2025-06-17 16:04:22.353 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 16:04:22.356 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 16:04:22.358 +10:00 INF] No pending work found
[2025-06-17 16:04:22.359 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 16:04:22.361 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 16:04:22.434 +10:00 INF] LEP Invoicer completed successfully in 1151ms. No work to process.
[2025-06-17 16:04:22.440 +10:00 INF] Database connection disposed
[2025-06-17 16:04:22.443 +10:00 INF] LEP Invoicer completed with result: 0
