using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.Data.SqlClient;
using NHibernate;
using NHibernate.Linq;
using LepInvoicer.Implementations;
using LepInvoicer.Interfaces;

namespace LepInvoicer.Implementations;

/// <summary>
/// Database service implementation for LEP Invoicer operations
/// </summary>
public class DatabaseService : IDatabaseService
{
    private readonly ILogger<DatabaseService> _logger;
    private readonly InvoicerConfiguration _config;
    private Microsoft.Data.SqlClient.SqlConnection _connection;
    private ISession _session;
    private bool _disposed = false;

    public DatabaseService(ILogger<DatabaseService> logger, IOptions<InvoicerConfiguration> config, ISession session)
    {
        _logger = logger;
        _config = config.Value;
        _session = session;
    }

    public Task Initialize()
    {
        try
        {
            _logger.LogInformation("Initializing database connection and NHibernate session...");

            // Initialize SQL connection
            _connection = new Microsoft.Data.SqlClient.SqlConnection(_config.ConnectionString);
            _connection.Open();

            // Execute initial cleanup SQL
            ExecuteSql(InvoicerConstants.SqlQueries.CleanupInitialSql).Wait();

            _logger.LogInformation("Database connection established successfully");
            return Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to initialize database connection");
            throw;
        }
    }

    public Task<List<KeyValuePair<int, string>>> GetOrdersToInvoice(int batchSize)
    {
        if (_session == null)
            throw new InvalidOperationException("Database not initialized");

        _logger.LogInformation("Getting {BatchSize} orders to invoice", batchSize);

        try
        {
            // Get candidate orders (without price filtering since PriceOfJobs is calculated)
            var candidateOrders = _session.Query<IOrder>()
                .Where(o => !_config.IgnoreCustomers.Contains(o.Customer.Name))
				.Where(o => o.Invoiced2 == null || (o.Invoiced2 != "Y" && o.Invoiced2 != "F" && o.Invoiced2 != "C"))
				.Where(o => o.FinishDate != null && o.FinishDate.Value.Year != 1)
				.Where(o => o.FinishDate.Value.Date >= _config.MinimumFinishDate)
				//.Where (o => o.Id == 1416689)
                .OrderByDescending(o => o.Id) // Match LinqPad script - process newest orders first
                .Take(batchSize * 3) // Get more candidates to account for zero-price filtering
                .ToList();

            // Filter out zero-priced orders (PriceOfJobs is calculated property)
            var validOrders = candidateOrders
                .Where(o => o.PriceOfJobs.HasValue && o.PriceOfJobs.Value > 0)
                .Take(batchSize)
                .Select(o => new KeyValuePair<int, string>(o.Id, o.Customer.Username))
                .ToList();

            _logger.LogInformation("Found {OrderCount} orders to invoice (filtered {CandidateCount} candidates)", validOrders.Count, candidateOrders.Count);
            return Task.FromResult(validOrders);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get orders to invoice");
            throw;
        }
    }

    public Task<List<OrderCredit>> GetCreditsToInvoice(int batchSize)
    {
        if (_session == null)
            throw new InvalidOperationException("Database not initialized");

        _logger.LogInformation("Getting {BatchSize} credits to invoice", batchSize);

        try
        {
            // Match LINQPad script: Type == "C" || Type == "M" || Type == "CI"
            var credits = _session.Query<OrderCredit>()
                .Fetch(c => c.Order)
                .Where(c => !c.Invoiced)
                .Where(c => c.Type == InvoicerConstants.CreditTypes.Credit ||
                           c.Type == InvoicerConstants.CreditTypes.Miscellaneous ||
                           c.Type == InvoicerConstants.CreditTypes.CreditInvoice)
                .Where(c => c.Order != null) // Must have associated order
                .Where(c => !_config.IgnoreCustomers.Contains(c.Order.Customer.Name))

                .OrderBy(c => c.DateCreated)
                .Take(batchSize)
                .ToList();

            _logger.LogInformation("Found {CreditCount} credits to invoice", credits.Count);
            return Task.FromResult(credits);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get credits to invoice");
            throw;
        }
    }

    public Task<List<OrderCredit>> GetRefundsToInvoice(int batchSize)
    {
        if (_session == null)
            throw new InvalidOperationException("Database not initialized");

        _logger.LogInformation("Getting {BatchSize} refunds to invoice", batchSize);

        try
        {
            // Match LINQPad script: Type == "S"
            var refunds = _session.Query<OrderCredit>()
                .Fetch(r => r.Customer)
                .Where(r => !r.Invoiced)
                .Where(r => r.Type == InvoicerConstants.CreditTypes.Refund) // Type == "S"
                .Where(r => r.Customer != null) // Must have customer
                .Where(r => !_config.IgnoreCustomers.Contains(r.Customer.Name))
                .OrderBy(r => r.DateCreated)
                .Take(batchSize)
                .ToList();

            _logger.LogInformation("Found {RefundCount} refunds to invoice", refunds.Count);
            return Task.FromResult(refunds);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get refunds to invoice");
            throw;
        }
    }

    public Task<IOrder> GetOrder(int orderId)
    {
        if (_session == null)
            throw new InvalidOperationException("Database not initialized");

        _logger.LogInformation("Getting order {OrderId}", orderId);

        try
        {
            var order = _session.Query<IOrder>()
                .Fetch(x => x.Jobs)
                .Where(x => x.Id == orderId)
                .FirstOrDefault();

            if (order == null)
                throw new InvalidOperationException($"Order {orderId} not found");

            return Task.FromResult(order);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get order {OrderId}", orderId);
            throw;
        }
    }

    public Task ExecuteSql(string sql)
    {
        if (_connection == null)
            throw new InvalidOperationException("Database not initialized");

        try
        {
            _logger.LogDebug("Executing SQL: {Sql}", sql);
            
            using var command = new Microsoft.Data.SqlClient.SqlCommand(sql, _connection);
            command.ExecuteNonQuery();
            
            return Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to execute SQL: {Sql}", sql);
            throw;
        }
    }

    public Task MarkOrderInvoiced(int orderId)
    {
        var sql = string.Format(InvoicerConstants.SqlQueries.MarkOrderInvoicedSql, orderId);
        return ExecuteSql(sql);
    }

    public Task MarkOrderFailed(int orderId, string errorMessage)
    {
        var escapedMessage = InvoicerUtilities.EscapeSqlString(errorMessage);
        var sql = string.Format(InvoicerConstants.SqlQueries.MarkOrderFailedSql, orderId, escapedMessage);
        return ExecuteSql(sql);
    }

    public Task MarkCreditInvoiced(int creditId)
    {
        var sql = string.Format(InvoicerConstants.SqlQueries.MarkCreditInvoicedSql, creditId);
        return ExecuteSql(sql);
    }

    public Task LogInvoicingResult(int orderId, int jobCount, decimal total, DateTime finishDate, bool success, string details)
    {
        var successFlag = success ? InvoicerConstants.Database.LogSuccessFlag : InvoicerConstants.Database.LogFailureFlag;
        var escapedDetails = details != null ? $"'{InvoicerUtilities.EscapeSqlString(details)}'" : "null";

        // Convert orderId = 0 to NULL for refunds (relaxed constraint now allows this)
        var orderIdValue = orderId == 0 ? "NULL" : orderId.ToString();

        var sql = string.Format(InvoicerConstants.SqlQueries.LogInvoicingResultSql,
            orderIdValue, jobCount, total, finishDate.ToString(_config.DateFormat), successFlag, escapedDetails, DateTime.Now.ToString(_config.DateFormat));

        return ExecuteSql(sql);
    }

    public Task CleanupInvoicerLogs()
    {
        _logger.LogInformation("Cleaning up invoicer logs...");
        return ExecuteSql(InvoicerConstants.SqlQueries.CleanupInvoicerLogsSql);
    }

    public Task<int?> GetSalesConsultantId(string salesConsultantName)
    {
        if (_session == null)
            throw new InvalidOperationException("Database not initialized");

        if (string.IsNullOrEmpty(salesConsultantName))
            return Task.FromResult<int?>(null);

        try
        {
            var sql = "SELECT Id FROM SalesConsultant WHERE Name = :name";
            var result = _session.CreateSQLQuery(sql)
                .SetParameter("name", salesConsultantName)
                .UniqueResult<int?>();

            return Task.FromResult(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get sales consultant ID for {SalesConsultantName}", salesConsultantName);
            return Task.FromResult<int?>(null);
        }
    }

    public Task<Dictionary<string, int>> GetOrderCreditTypeSummary()
    {
        if (_session == null)
            throw new InvalidOperationException("Database not initialized");

        try
        {
            var summary = _session.Query<OrderCredit>()
                .Where(c => !c.Invoiced)
                .GroupBy(c => c.Type)
                .Select(g => new { Type = g.Key, Count = g.Count() })
                .ToDictionary(x => x.Type ?? "NULL", x => x.Count);

            _logger.LogInformation("OrderCredit Type Summary: {Summary}",
                string.Join(", ", summary.Select(kvp => $"{kvp.Key}: {kvp.Value}")));

            return Task.FromResult(summary);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get OrderCredit type summary");
            throw;
        }
    }

    public async Task CreateInvoicer2LogEntriesForCredits()
    {
        if (_connection == null)
            throw new InvalidOperationException("Database not initialized");

        try
        {
            _logger.LogInformation("=== CREATING INVOICER2LOG ENTRIES FOR CREDITS ===");

            // Get 10 records of each type
            var types = new[] { "C", "M", "CI", "S" };

            foreach (var type in types)
            {
                _logger.LogInformation("--- Processing Type '{Type}' ---", type);

                var sql = $@"
                    SELECT TOP 10
                        oc.Id,
                        oc.Type,
                        ISNULL(oc.Amount, 0) as Amount,
                        oc.DateCreated,
                        oc.OrderId,
                        oc.CustomerId
                    FROM OrderCredit oc
                    WHERE oc.Type = '{type}'
                    ORDER BY oc.DateCreated DESC";

                using var command = new Microsoft.Data.SqlClient.SqlCommand(sql, _connection);
                using var reader = await command.ExecuteReaderAsync();

                var records = new List<(int Id, string Type, decimal Amount, DateTime DateCreated, int? OrderId, int? CustomerId)>();

                while (await reader.ReadAsync())
                {
                    var creditId = reader.GetInt32(0); // Id
                    var amount = reader.GetDecimal(2); // Amount
                    var dateCreated = reader.GetDateTime(3); // DateCreated
                    var orderIdFromCredit = reader.IsDBNull(4) ? (int?)null : reader.GetInt32(4); // OrderId
                    var customerId = reader.IsDBNull(5) ? (int?)null : reader.GetInt32(5); // CustomerId

                    records.Add((creditId, type, amount, dateCreated, orderIdFromCredit, customerId));
                }

                reader.Close();

                // Now create Invoicer2Log entries
                foreach (var record in records)
                {
                    await CreateInvoicer2LogEntryForCredit(record.Id, record.Type, record.Amount, record.DateCreated, record.OrderId, record.CustomerId);
                }

                _logger.LogInformation("  Created {Count} Invoicer2Log entries for type '{Type}'", records.Count, type);
            }

            _logger.LogInformation("=== INVOICER2LOG CREATION COMPLETE ===");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create Invoicer2Log entries for credits");
            throw;
        }
    }

    private async Task CreateInvoicer2LogEntryForCredit(int creditId, string type, decimal amount, DateTime dateCreated, int? orderId, int? customerId)
    {
        try
        {
            // Determine the OrderId to use for logging
            string orderIdValue;
            string logOrderIdDisplay;

            if (orderId.HasValue)
            {
                // Use the associated order ID (for types C, M, CI)
                orderIdValue = orderId.Value.ToString();
                logOrderIdDisplay = orderId.Value.ToString();
            }
            else
            {
                // For refunds (type S) without OrderId, use NULL
                orderIdValue = "NULL";
                logOrderIdDisplay = "NULL";
            }

            var sql = $@"
                INSERT INTO Invoicer2Log ([OrderId], [JobCount], [Total], [FinishDate], [Success], [Details], [DateCreated])
                VALUES ({orderIdValue}, 1, {amount}, '{dateCreated:yyyy-MM-dd HH:mm:ss}', 'Y', 'Credit Type {type} - CreditId {creditId} - CustomerId {customerId}', '{DateTime.Now:yyyy-MM-dd HH:mm:ss}')";

            using var command = new Microsoft.Data.SqlClient.SqlCommand(sql, _connection);
            await command.ExecuteNonQueryAsync();

            _logger.LogInformation("  Created Invoicer2Log entry: CreditId={CreditId}, Type={Type}, Amount={Amount:C}, OrderId={OrderId}, CustomerId={CustomerId}",
                creditId, type, amount, logOrderIdDisplay, customerId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create Invoicer2Log entry for credit {CreditId}", creditId);
        }
    }

    public void Dispose()
    {
        if (!_disposed)
        {
            _connection?.Close();
            _connection?.Dispose();
            _disposed = true;
            _logger.LogInformation("Database connection disposed");
        }
    }
}
