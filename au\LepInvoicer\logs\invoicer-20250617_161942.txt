[2025-06-17 16:19:42.431 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 16:19:42.462 +10:00 INF] Initializing FastReport...
[2025-06-17 16:19:42.534 +10:00 INF] FastReport initialized successfully
[2025-06-17 16:19:42.967 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 16:19:44.280 +10:00 INF] Starting LEP Invoicer at "2025-06-17T16:19:44.2798643+10:00"
[2025-06-17 16:19:44.283 +10:00 INF] Initializing database service...
[2025-06-17 16:19:44.286 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 16:19:44.386 +10:00 INF] Database connection established successfully
[2025-06-17 16:19:44.387 +10:00 INF] Database service initialized successfully
[2025-06-17 16:19:44.390 +10:00 INF] Checking for pending work...
[2025-06-17 16:19:44.393 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 16:19:45.261 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 16:19:45.264 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 16:19:45.276 +10:00 INF] Found 0 credits to invoice
[2025-06-17 16:19:45.285 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 16:19:45.289 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 16:19:45.305 +10:00 INF] No pending work found
[2025-06-17 16:19:45.324 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 16:19:45.326 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 16:19:45.407 +10:00 INF] LEP Invoicer completed successfully in 1127ms. No work to process.
[2025-06-17 16:19:45.413 +10:00 INF] Database connection disposed
[2025-06-17 16:19:45.414 +10:00 INF] LEP Invoicer completed with result: 0
