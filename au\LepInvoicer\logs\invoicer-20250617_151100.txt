[2025-06-17 15:11:01.039 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 15:11:01.091 +10:00 INF] Initializing FastReport...
[2025-06-17 15:11:01.186 +10:00 INF] FastReport initialized successfully
[2025-06-17 15:11:01.991 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 15:11:03.606 +10:00 INF] Starting LEP Invoicer at "2025-06-17T15:11:03.6064481+10:00"
[2025-06-17 15:11:03.611 +10:00 INF] Initializing database service...
[2025-06-17 15:11:03.614 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 15:11:03.725 +10:00 INF] Database connection established successfully
[2025-06-17 15:11:03.726 +10:00 INF] Database service initialized successfully
[2025-06-17 15:11:03.728 +10:00 INF] Checking for pending work...
[2025-06-17 15:11:03.731 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 15:11:04.640 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 15:11:04.644 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 15:11:04.673 +10:00 INF] Found 0 credits to invoice
[2025-06-17 15:11:04.676 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 15:11:04.718 +10:00 INF] Found 16 refunds to invoice
[2025-06-17 15:11:04.720 +10:00 INF] Found pending work: 16 refunds
[2025-06-17 15:11:04.722 +10:00 INF] Initializing MYOB and other services...
[2025-06-17 15:11:04.725 +10:00 INF] Initializing MYOB service...
[2025-06-17 15:11:04.726 +10:00 INF] Initializing MYOB with OAuth authentication
[2025-06-17 15:11:04.729 +10:00 INF] Starting OAuth authentication flow
[2025-06-17 15:11:04.742 +10:00 INF] Using existing OAuth tokens
[2025-06-17 15:11:04.744 +10:00 INF] Initializing MYOB services with OAuth keystore
[2025-06-17 15:11:04.751 +10:00 INF] MYOB services initialized successfully
[2025-06-17 15:11:04.753 +10:00 INF] OAuth keystore set for API calls
[2025-06-17 15:11:04.754 +10:00 INF] Getting company files from MYOB
[2025-06-17 15:11:05.062 +10:00 INF] MYOB service initialized with OAuth credentials. Using company file: LEP Colour Printers Pty Ltd
