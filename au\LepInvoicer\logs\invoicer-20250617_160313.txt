[2025-06-17 16:03:13.635 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 16:03:13.663 +10:00 INF] Initializing FastReport...
[2025-06-17 16:03:13.746 +10:00 INF] FastReport initialized successfully
[2025-06-17 16:03:14.186 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 16:03:15.628 +10:00 INF] Starting LEP Invoicer at "2025-06-17T16:03:15.6285194+10:00"
[2025-06-17 16:03:15.632 +10:00 INF] Initializing database service...
[2025-06-17 16:03:15.635 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 16:03:15.736 +10:00 INF] Database connection established successfully
[2025-06-17 16:03:15.738 +10:00 INF] Database service initialized successfully
[2025-06-17 16:03:15.741 +10:00 INF] Checking for pending work...
[2025-06-17 16:03:15.744 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 16:03:16.759 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 16:03:16.768 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 16:03:16.785 +10:00 INF] Found 0 credits to invoice
[2025-06-17 16:03:16.787 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 16:03:16.793 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 16:03:16.794 +10:00 INF] No pending work found
[2025-06-17 16:03:16.799 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 16:03:16.801 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 16:03:16.904 +10:00 INF] LEP Invoicer completed successfully in 1276ms. No work to process.
[2025-06-17 16:03:16.914 +10:00 INF] Database connection disposed
[2025-06-17 16:03:16.917 +10:00 INF] LEP Invoicer completed with result: 0
