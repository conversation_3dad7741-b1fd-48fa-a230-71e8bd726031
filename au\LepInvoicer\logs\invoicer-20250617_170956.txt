[2025-06-17 17:09:56.528 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 17:09:56.560 +10:00 INF] Initializing FastReport...
[2025-06-17 17:09:56.644 +10:00 INF] FastReport initialized successfully
[2025-06-17 17:09:57.049 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 17:09:58.295 +10:00 INF] Starting LEP Invoicer at "2025-06-17T17:09:58.2950690+10:00"
[2025-06-17 17:09:58.299 +10:00 INF] Initializing database service...
[2025-06-17 17:09:58.302 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 17:09:58.403 +10:00 INF] Database connection established successfully
[2025-06-17 17:09:58.404 +10:00 INF] Database service initialized successfully
[2025-06-17 17:09:58.407 +10:00 INF] Checking for pending work...
[2025-06-17 17:09:58.410 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 17:09:59.256 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 17:09:59.260 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 17:09:59.273 +10:00 INF] Found 0 credits to invoice
[2025-06-17 17:09:59.274 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 17:09:59.279 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 17:09:59.281 +10:00 INF] No pending work found
[2025-06-17 17:09:59.282 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 17:09:59.302 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 17:09:59.390 +10:00 INF] LEP Invoicer completed successfully in 1095ms. No work to process.
[2025-06-17 17:09:59.397 +10:00 INF] Database connection disposed
[2025-06-17 17:09:59.399 +10:00 INF] LEP Invoicer completed with result: 0
