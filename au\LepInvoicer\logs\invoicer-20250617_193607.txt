[2025-06-17 19:36:08.000 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 19:36:08.035 +10:00 INF] Initializing FastReport...
[2025-06-17 19:36:08.115 +10:00 INF] FastReport initialized successfully
[2025-06-17 19:36:08.580 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 19:36:10.110 +10:00 INF] Starting LEP Invoicer at "2025-06-17T19:36:10.1100573+10:00"
[2025-06-17 19:36:10.113 +10:00 INF] Initializing database service...
[2025-06-17 19:36:10.117 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 19:36:10.220 +10:00 INF] Database connection established successfully
[2025-06-17 19:36:10.222 +10:00 INF] Database service initialized successfully
[2025-06-17 19:36:10.236 +10:00 INF] Checking for pending work...
[2025-06-17 19:36:10.239 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 19:36:11.206 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 19:36:11.209 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 19:36:11.221 +10:00 INF] Found 0 credits to invoice
[2025-06-17 19:36:11.223 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 19:36:11.227 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 19:36:11.228 +10:00 INF] No pending work found
[2025-06-17 19:36:11.230 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 19:36:11.231 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 19:36:11.307 +10:00 INF] LEP Invoicer completed successfully in 1197ms. No work to process.
[2025-06-17 19:36:11.314 +10:00 INF] Database connection disposed
[2025-06-17 19:36:11.316 +10:00 INF] LEP Invoicer completed with result: 0
