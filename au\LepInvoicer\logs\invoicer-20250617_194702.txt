[2025-06-17 19:47:02.870 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 19:47:02.903 +10:00 INF] Initializing FastReport...
[2025-06-17 19:47:02.995 +10:00 INF] FastReport initialized successfully
[2025-06-17 19:47:03.422 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 19:47:04.737 +10:00 INF] Starting LEP Invoicer at "2025-06-17T19:47:04.7367892+10:00"
[2025-06-17 19:47:04.740 +10:00 INF] Initializing database service...
[2025-06-17 19:47:04.743 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 19:47:04.852 +10:00 INF] Database connection established successfully
[2025-06-17 19:47:04.855 +10:00 INF] Database service initialized successfully
[2025-06-17 19:47:04.859 +10:00 INF] Checking for pending work...
[2025-06-17 19:47:04.862 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 19:47:05.871 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 19:47:05.874 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 19:47:05.900 +10:00 INF] Found 0 credits to invoice
[2025-06-17 19:47:05.904 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 19:47:05.921 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 19:47:05.923 +10:00 INF] No pending work found
[2025-06-17 19:47:05.926 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 19:47:05.928 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 19:47:06.016 +10:00 INF] LEP Invoicer completed successfully in 1279ms. No work to process.
[2025-06-17 19:47:06.022 +10:00 INF] Database connection disposed
[2025-06-17 19:47:06.029 +10:00 INF] LEP Invoicer completed with result: 0
