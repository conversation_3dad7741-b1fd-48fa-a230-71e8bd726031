[2025-06-17 17:52:27.531 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 17:52:27.558 +10:00 INF] Initializing FastReport...
[2025-06-17 17:52:27.649 +10:00 INF] FastReport initialized successfully
[2025-06-17 17:52:28.154 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 17:52:29.353 +10:00 INF] Starting LEP Invoicer at "2025-06-17T17:52:29.3534873+10:00"
[2025-06-17 17:52:29.357 +10:00 INF] Initializing database service...
[2025-06-17 17:52:29.359 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 17:52:29.460 +10:00 INF] Database connection established successfully
[2025-06-17 17:52:29.461 +10:00 INF] Database service initialized successfully
[2025-06-17 17:52:29.464 +10:00 INF] Checking for pending work...
[2025-06-17 17:52:29.467 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 17:52:30.359 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 17:52:30.362 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 17:52:30.375 +10:00 INF] Found 0 credits to invoice
[2025-06-17 17:52:30.377 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 17:52:30.380 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 17:52:30.382 +10:00 INF] No pending work found
[2025-06-17 17:52:30.384 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 17:52:30.386 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 17:52:30.473 +10:00 INF] LEP Invoicer completed successfully in 1119ms. No work to process.
[2025-06-17 17:52:30.479 +10:00 INF] Database connection disposed
[2025-06-17 17:52:30.481 +10:00 INF] LEP Invoicer completed with result: 0
