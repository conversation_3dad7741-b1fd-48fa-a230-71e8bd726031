[2025-06-17 17:20:50.569 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 17:20:50.602 +10:00 INF] Initializing FastReport...
[2025-06-17 17:20:50.684 +10:00 INF] FastReport initialized successfully
[2025-06-17 17:20:51.129 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 17:20:52.371 +10:00 INF] Starting LEP Invoicer at "2025-06-17T17:20:52.3708791+10:00"
[2025-06-17 17:20:52.374 +10:00 INF] Initializing database service...
[2025-06-17 17:20:52.377 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 17:20:52.475 +10:00 INF] Database connection established successfully
[2025-06-17 17:20:52.479 +10:00 INF] Database service initialized successfully
[2025-06-17 17:20:52.481 +10:00 INF] Checking for pending work...
[2025-06-17 17:20:52.484 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 17:20:53.354 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 17:20:53.357 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 17:20:53.369 +10:00 INF] Found 0 credits to invoice
[2025-06-17 17:20:53.371 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 17:20:53.375 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 17:20:53.376 +10:00 INF] No pending work found
[2025-06-17 17:20:53.377 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 17:20:53.379 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 17:20:53.455 +10:00 INF] LEP Invoicer completed successfully in 1084ms. No work to process.
[2025-06-17 17:20:53.461 +10:00 INF] Database connection disposed
[2025-06-17 17:20:53.463 +10:00 INF] LEP Invoicer completed with result: 0
