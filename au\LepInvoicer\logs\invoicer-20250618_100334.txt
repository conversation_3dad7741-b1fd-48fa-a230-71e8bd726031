[2025-06-18 10:03:34.594 +10:00 INF] Starting LEP Invoicer application
[2025-06-18 10:03:34.622 +10:00 INF] Initializing FastReport...
[2025-06-18 10:03:34.692 +10:00 INF] FastReport initialized successfully
[2025-06-18 10:03:35.067 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-18 10:03:36.331 +10:00 INF] Starting LEP Invoicer at "2025-06-18T10:03:36.3309029+10:00"
[2025-06-18 10:03:36.340 +10:00 INF] Initializing database service...
[2025-06-18 10:03:36.346 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-18 10:03:36.463 +10:00 INF] Database connection established successfully
[2025-06-18 10:03:36.465 +10:00 INF] Database service initialized successfully
[2025-06-18 10:03:36.469 +10:00 INF] Checking for pending work...
[2025-06-18 10:03:36.475 +10:00 INF] Getting 500 orders to invoice
[2025-06-18 10:03:37.464 +10:00 INF] Found 0 orders to invoice (filtered 16 candidates)
[2025-06-18 10:03:37.467 +10:00 INF] Getting 100 credits to invoice
[2025-06-18 10:03:37.484 +10:00 INF] Found 0 credits to invoice
[2025-06-18 10:03:37.488 +10:00 INF] Getting 100 refunds to invoice
[2025-06-18 10:03:37.493 +10:00 INF] Found 0 refunds to invoice
[2025-06-18 10:03:37.502 +10:00 INF] No pending work found
[2025-06-18 10:03:37.504 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-18 10:03:37.505 +10:00 INF] Cleaning up invoicer logs...
[2025-06-18 10:03:37.601 +10:00 INF] LEP Invoicer completed successfully in 1270ms. No work to process.
[2025-06-18 10:03:37.609 +10:00 INF] Database connection disposed
[2025-06-18 10:03:37.612 +10:00 INF] LEP Invoicer completed with result: 0
