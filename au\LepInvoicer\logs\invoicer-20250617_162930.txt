[2025-06-17 16:29:30.614 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 16:29:30.642 +10:00 INF] Initializing FastReport...
[2025-06-17 16:29:30.711 +10:00 INF] FastReport initialized successfully
[2025-06-17 16:29:31.140 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 16:29:32.799 +10:00 INF] Starting LEP Invoicer at "2025-06-17T16:29:32.7994114+10:00"
[2025-06-17 16:29:32.803 +10:00 INF] Initializing database service...
[2025-06-17 16:29:32.805 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 16:29:33.009 +10:00 INF] Database connection established successfully
[2025-06-17 16:29:33.011 +10:00 INF] Database service initialized successfully
[2025-06-17 16:29:33.014 +10:00 INF] Checking for pending work...
[2025-06-17 16:29:33.018 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 16:29:35.566 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 16:29:35.568 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 16:29:35.590 +10:00 INF] Found 0 credits to invoice
[2025-06-17 16:29:35.592 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 16:29:35.606 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 16:29:35.607 +10:00 INF] No pending work found
[2025-06-17 16:29:35.609 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 16:29:35.612 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 16:29:35.786 +10:00 INF] LEP Invoicer completed successfully in 2987ms. No work to process.
[2025-06-17 16:29:35.793 +10:00 INF] Database connection disposed
[2025-06-17 16:29:35.795 +10:00 INF] LEP Invoicer completed with result: 0
