[2025-06-17 20:07:48.548 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 20:07:48.577 +10:00 INF] Initializing FastReport...
[2025-06-17 20:07:48.647 +10:00 INF] FastReport initialized successfully
[2025-06-17 20:07:49.014 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 20:07:50.207 +10:00 INF] Starting LEP Invoicer at "2025-06-17T20:07:50.2073877+10:00"
[2025-06-17 20:07:50.211 +10:00 INF] Initializing database service...
[2025-06-17 20:07:50.213 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 20:07:50.329 +10:00 INF] Database connection established successfully
[2025-06-17 20:07:50.330 +10:00 INF] Database service initialized successfully
[2025-06-17 20:07:50.333 +10:00 INF] Checking for pending work...
[2025-06-17 20:07:50.337 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 20:07:51.308 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 20:07:51.327 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 20:07:51.341 +10:00 INF] Found 0 credits to invoice
[2025-06-17 20:07:51.343 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 20:07:51.346 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 20:07:51.350 +10:00 INF] No pending work found
[2025-06-17 20:07:51.351 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 20:07:51.353 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 20:07:51.433 +10:00 INF] LEP Invoicer completed successfully in 1226ms. No work to process.
[2025-06-17 20:07:51.440 +10:00 INF] Database connection disposed
[2025-06-17 20:07:51.441 +10:00 INF] LEP Invoicer completed with result: 0
