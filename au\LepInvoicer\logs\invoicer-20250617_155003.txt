[2025-06-17 15:50:03.986 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 15:50:04.024 +10:00 INF] Initializing FastReport...
[2025-06-17 15:50:04.110 +10:00 INF] FastReport initialized successfully
[2025-06-17 15:50:04.569 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 15:50:05.986 +10:00 INF] Starting LEP Invoicer at "2025-06-17T15:50:05.9860243+10:00"
[2025-06-17 15:50:05.991 +10:00 INF] Initializing database service...
[2025-06-17 15:50:05.995 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 15:50:06.120 +10:00 INF] Database connection established successfully
[2025-06-17 15:50:06.121 +10:00 INF] Database service initialized successfully
[2025-06-17 15:50:06.124 +10:00 INF] Checking for pending work...
[2025-06-17 15:50:06.127 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 15:50:07.028 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 15:50:07.031 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 15:50:07.046 +10:00 INF] Found 0 credits to invoice
[2025-06-17 15:50:07.048 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 15:50:07.053 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 15:50:07.054 +10:00 INF] No pending work found
[2025-06-17 15:50:07.056 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 15:50:07.060 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 15:50:07.164 +10:00 INF] LEP Invoicer completed successfully in 1178ms. No work to process.
[2025-06-17 15:50:07.170 +10:00 INF] Database connection disposed
[2025-06-17 15:50:07.173 +10:00 INF] LEP Invoicer completed with result: 0
