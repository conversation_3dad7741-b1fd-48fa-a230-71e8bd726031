[2025-06-17 20:17:37.559 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 20:17:37.585 +10:00 INF] Initializing FastReport...
[2025-06-17 20:17:37.654 +10:00 INF] FastReport initialized successfully
[2025-06-17 20:17:38.064 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 20:17:39.436 +10:00 INF] Starting LEP Invoicer at "2025-06-17T20:17:39.4363679+10:00"
[2025-06-17 20:17:39.439 +10:00 INF] Initializing database service...
[2025-06-17 20:17:39.442 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 20:17:39.575 +10:00 INF] Database connection established successfully
[2025-06-17 20:17:39.576 +10:00 INF] Database service initialized successfully
[2025-06-17 20:17:39.579 +10:00 INF] Checking for pending work...
[2025-06-17 20:17:39.583 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 20:17:40.432 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 20:17:40.434 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 20:17:40.446 +10:00 INF] Found 0 credits to invoice
[2025-06-17 20:17:40.448 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 20:17:40.452 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 20:17:40.459 +10:00 INF] No pending work found
[2025-06-17 20:17:40.460 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 20:17:40.462 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 20:17:40.539 +10:00 INF] LEP Invoicer completed successfully in 1103ms. No work to process.
[2025-06-17 20:17:40.553 +10:00 INF] Database connection disposed
[2025-06-17 20:17:40.555 +10:00 INF] LEP Invoicer completed with result: 0
