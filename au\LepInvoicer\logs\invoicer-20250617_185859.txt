[2025-06-17 18:58:59.640 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 18:58:59.668 +10:00 INF] Initializing FastReport...
[2025-06-17 18:58:59.748 +10:00 INF] FastReport initialized successfully
[2025-06-17 18:59:00.196 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 18:59:01.565 +10:00 INF] Starting LEP Invoicer at "2025-06-17T18:59:01.5651680+10:00"
[2025-06-17 18:59:01.571 +10:00 INF] Initializing database service...
[2025-06-17 18:59:01.574 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 18:59:01.677 +10:00 INF] Database connection established successfully
[2025-06-17 18:59:01.679 +10:00 INF] Database service initialized successfully
[2025-06-17 18:59:01.682 +10:00 INF] Checking for pending work...
[2025-06-17 18:59:01.688 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 18:59:02.608 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 18:59:02.628 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 18:59:02.643 +10:00 INF] Found 0 credits to invoice
[2025-06-17 18:59:02.645 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 18:59:02.652 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 18:59:02.653 +10:00 INF] No pending work found
[2025-06-17 18:59:02.655 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 18:59:02.656 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 18:59:02.733 +10:00 INF] LEP Invoicer completed successfully in 1168ms. No work to process.
[2025-06-17 18:59:02.740 +10:00 INF] Database connection disposed
[2025-06-17 18:59:02.742 +10:00 INF] LEP Invoicer completed with result: 0
