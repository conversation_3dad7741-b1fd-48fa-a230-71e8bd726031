[2025-06-17 19:30:39.659 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 19:30:39.688 +10:00 INF] Initializing FastReport...
[2025-06-17 19:30:39.766 +10:00 INF] FastReport initialized successfully
[2025-06-17 19:30:40.186 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 19:30:41.485 +10:00 INF] Starting LEP Invoicer at "2025-06-17T19:30:41.4856411+10:00"
[2025-06-17 19:30:41.489 +10:00 INF] Initializing database service...
[2025-06-17 19:30:41.502 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 19:30:41.613 +10:00 INF] Database connection established successfully
[2025-06-17 19:30:41.614 +10:00 INF] Database service initialized successfully
[2025-06-17 19:30:41.617 +10:00 INF] Checking for pending work...
[2025-06-17 19:30:41.619 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 19:30:42.527 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 19:30:42.530 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 19:30:42.542 +10:00 INF] Found 0 credits to invoice
[2025-06-17 19:30:42.544 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 19:30:42.548 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 19:30:42.549 +10:00 INF] No pending work found
[2025-06-17 19:30:42.551 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 19:30:42.553 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 19:30:42.633 +10:00 INF] LEP Invoicer completed successfully in 1148ms. No work to process.
[2025-06-17 19:30:42.639 +10:00 INF] Database connection disposed
[2025-06-17 19:30:42.641 +10:00 INF] LEP Invoicer completed with result: 0
