//using System.IO;
//using Trinet.Core.IO.Ntfs;

//namespace lep
//{
//    public static class FileSystemInfoExtensions
//    {
//        public static void CopyMacIconFrom(this FileSystemInfo file, FileSystemInfo other)
//        {
//            foreach (var in_ads in FileSystem.ListAlternateDataStreams(other))
//            {
//                var out_ads = FileSystem.GetAlternateDataStream(file.FullName, in_ads.Name);
//                using (var in_s = in_ads.OpenRead())
//                {
//                    using (var out_s = out_ads.Open(FileMode.Create))
//                    {
//                        var buffer = new byte[1024];
//                        while (true)
//                        {
//                            var count = in_s.Read(buffer, 0, 1024);
//                            if (0 == count) break;
//                            out_s.Write(buffer, 0, count);
//                        }
//                    }
//                }
//            }
//        }
//    }
//}
