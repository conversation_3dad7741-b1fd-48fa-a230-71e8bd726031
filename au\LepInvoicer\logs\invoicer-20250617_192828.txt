[2025-06-17 19:28:28.621 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 19:28:28.655 +10:00 INF] Initializing FastReport...
[2025-06-17 19:28:28.749 +10:00 INF] FastReport initialized successfully
[2025-06-17 19:28:29.166 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 19:28:30.548 +10:00 INF] Starting LEP Invoicer at "2025-06-17T19:28:30.5480792+10:00"
[2025-06-17 19:28:30.552 +10:00 INF] Initializing database service...
[2025-06-17 19:28:30.556 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 19:28:30.662 +10:00 INF] Database connection established successfully
[2025-06-17 19:28:30.663 +10:00 INF] Database service initialized successfully
[2025-06-17 19:28:30.669 +10:00 INF] Checking for pending work...
[2025-06-17 19:28:30.674 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 19:28:31.569 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 19:28:31.574 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 19:28:31.588 +10:00 INF] Found 0 credits to invoice
[2025-06-17 19:28:31.592 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 19:28:31.595 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 19:28:31.597 +10:00 INF] No pending work found
[2025-06-17 19:28:31.601 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 19:28:31.603 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 19:28:31.695 +10:00 INF] LEP Invoicer completed successfully in 1147ms. No work to process.
[2025-06-17 19:28:31.701 +10:00 INF] Database connection disposed
[2025-06-17 19:28:31.703 +10:00 INF] LEP Invoicer completed with result: 0
