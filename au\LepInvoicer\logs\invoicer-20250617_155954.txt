[2025-06-17 15:59:54.715 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 15:59:54.758 +10:00 INF] Initializing FastReport...
[2025-06-17 15:59:54.837 +10:00 INF] FastReport initialized successfully
[2025-06-17 15:59:55.312 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 15:59:57.138 +10:00 INF] Starting LEP Invoicer at "2025-06-17T15:59:57.1380244+10:00"
[2025-06-17 15:59:57.143 +10:00 INF] Initializing database service...
[2025-06-17 15:59:57.149 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 15:59:57.273 +10:00 INF] Database connection established successfully
[2025-06-17 15:59:57.274 +10:00 INF] Database service initialized successfully
[2025-06-17 15:59:57.279 +10:00 INF] Checking for pending work...
[2025-06-17 15:59:57.282 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 15:59:58.557 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 15:59:58.566 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 15:59:58.582 +10:00 INF] Found 0 credits to invoice
[2025-06-17 15:59:58.585 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 15:59:58.595 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 15:59:58.598 +10:00 INF] No pending work found
[2025-06-17 15:59:58.599 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 15:59:58.601 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 15:59:58.707 +10:00 INF] LEP Invoicer completed successfully in 1569ms. No work to process.
[2025-06-17 15:59:58.716 +10:00 INF] Database connection disposed
[2025-06-17 15:59:58.719 +10:00 INF] LEP Invoicer completed with result: 0
