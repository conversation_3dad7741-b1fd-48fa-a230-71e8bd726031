[2025-06-17 19:14:19.628 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 19:14:19.656 +10:00 INF] Initializing FastReport...
[2025-06-17 19:14:19.729 +10:00 INF] FastReport initialized successfully
[2025-06-17 19:14:20.132 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 19:14:21.400 +10:00 INF] Starting LEP Invoicer at "2025-06-17T19:14:21.3997826+10:00"
[2025-06-17 19:14:21.410 +10:00 INF] Initializing database service...
[2025-06-17 19:14:21.414 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 19:14:21.526 +10:00 INF] Database connection established successfully
[2025-06-17 19:14:21.528 +10:00 INF] Database service initialized successfully
[2025-06-17 19:14:21.531 +10:00 INF] Checking for pending work...
[2025-06-17 19:14:21.540 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 19:14:22.435 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 19:14:22.438 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 19:14:22.451 +10:00 INF] Found 0 credits to invoice
[2025-06-17 19:14:22.453 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 19:14:22.458 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 19:14:22.466 +10:00 INF] No pending work found
[2025-06-17 19:14:22.467 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 19:14:22.470 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 19:14:22.590 +10:00 INF] LEP Invoicer completed successfully in 1190ms. No work to process.
[2025-06-17 19:14:22.596 +10:00 INF] Database connection disposed
[2025-06-17 19:14:22.599 +10:00 INF] LEP Invoicer completed with result: 0
