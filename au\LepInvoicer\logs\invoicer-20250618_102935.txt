[2025-06-18 10:29:35.782 +10:00 INF] Starting LEP Invoicer application
[2025-06-18 10:29:35.812 +10:00 INF] Initializing FastReport...
[2025-06-18 10:29:35.902 +10:00 INF] FastReport initialized successfully
[2025-06-18 10:29:36.406 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-18 10:29:37.769 +10:00 INF] Starting LEP Invoicer at "2025-06-18T10:29:37.7693609+10:00"
[2025-06-18 10:29:37.774 +10:00 INF] Initializing database service...
[2025-06-18 10:29:37.778 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-18 10:29:37.894 +10:00 INF] Database connection established successfully
[2025-06-18 10:29:37.896 +10:00 INF] Database service initialized successfully
[2025-06-18 10:29:37.899 +10:00 INF] Checking for pending work...
[2025-06-18 10:29:37.902 +10:00 INF] Getting 500 orders to invoice
[2025-06-18 10:29:38.863 +10:00 INF] Found 1 orders to invoice (filtered 18 candidates)
[2025-06-18 10:29:38.866 +10:00 INF] Getting 100 credits to invoice
[2025-06-18 10:29:38.878 +10:00 INF] Found 0 credits to invoice
[2025-06-18 10:29:38.880 +10:00 INF] Getting 100 refunds to invoice
[2025-06-18 10:29:38.887 +10:00 INF] Found 0 refunds to invoice
[2025-06-18 10:29:38.895 +10:00 INF] Found pending work: 1 orders
[2025-06-18 10:29:38.898 +10:00 INF] Initializing MYOB and other services...
[2025-06-18 10:29:38.910 +10:00 INF] Initializing MYOB service...
[2025-06-18 10:29:38.914 +10:00 INF] Initializing MYOB with OAuth authentication
[2025-06-18 10:29:38.918 +10:00 INF] Starting OAuth authentication flow
[2025-06-18 10:29:38.928 +10:00 INF] Using existing OAuth tokens
[2025-06-18 10:29:38.929 +10:00 INF] Initializing MYOB services with OAuth keystore
[2025-06-18 10:29:38.934 +10:00 INF] MYOB services initialized successfully
[2025-06-18 10:29:38.935 +10:00 INF] OAuth keystore set for API calls
[2025-06-18 10:29:38.936 +10:00 INF] Getting company files from MYOB
[2025-06-18 10:29:39.246 +10:00 INF] MYOB service initialized with OAuth credentials. Using company file: LEP Colour Printers Pty Ltd
[2025-06-18 10:29:41.473 +10:00 INF] MYOB service initialized successfully with company file: LEP Colour Printers Pty Ltd
[2025-06-18 10:29:41.476 +10:00 INF] All services initialized successfully
[2025-06-18 10:29:41.482 +10:00 INF] Processing order invoices...
[2025-06-18 10:29:41.485 +10:00 INF] Getting 500 orders to invoice
[2025-06-18 10:29:41.835 +10:00 INF] Found 1 orders to invoice (filtered 18 candidates)
[2025-06-18 10:29:41.837 +10:00 INF] Found 1 orders to process
[2025-06-18 10:29:41.841 +10:00 INF] Getting order 1417451
[2025-06-18 10:29:41.874 +10:00 INF] Processing order 1417451 with 1 jobs, total: ¤221.79
[2025-06-18 10:29:41.880 +10:00 INF] Creating order invoice for order 1417451
[2025-06-18 10:29:43.748 +10:00 INF] Successfully created MYOB invoice O1417451 for order 1417451
[2025-06-18 10:29:44.942 +10:00 INF] Successfully created MYOB invoice for order 1417451
[2025-06-18 10:29:45.668 +10:00 INF] Generating PDF invoice for order 1417451 at \\dfs01\resource\invoices\2025/Jun/18\O1417451.pdf
[2025-06-18 10:29:48.414 +10:00 INF] Successfully generated PDF for order 1417451
[2025-06-18 10:29:48.441 +10:00 WRN] No email address found for order 1417451
[2025-06-18 10:29:48.455 +10:00 INF] Successfully processed order 1417451 (MYOB + PDF)
[2025-06-18 10:29:48.463 +10:00 INF] Order processing completed. Processed: 1, Success: 1, Failed: 0
[2025-06-18 10:29:48.467 +10:00 INF] Processing credit invoices...
[2025-06-18 10:29:48.468 +10:00 INF] Getting 100 credits to invoice
[2025-06-18 10:29:48.472 +10:00 INF] Found 0 credits to invoice
[2025-06-18 10:29:48.484 +10:00 INF] Processing refund invoices...
[2025-06-18 10:29:48.485 +10:00 INF] Getting 100 refunds to invoice
[2025-06-18 10:29:48.488 +10:00 INF] Found 0 refunds to invoice
[2025-06-18 10:29:48.490 +10:00 INF] Cleaning up invoicer logs...
[2025-06-18 10:29:48.588 +10:00 INF] LEP Invoicer completed successfully in 10819ms. Orders: 1, Credits: 0, Refunds: 0
[2025-06-18 10:29:48.597 +10:00 INF] Database connection disposed
[2025-06-18 10:29:48.601 +10:00 INF] LEP Invoicer completed with result: 0
