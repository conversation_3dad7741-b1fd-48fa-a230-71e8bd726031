[2025-06-17 17:28:26.713 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 17:28:26.748 +10:00 INF] Initializing FastReport...
[2025-06-17 17:28:26.836 +10:00 INF] FastReport initialized successfully
[2025-06-17 17:28:27.266 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 17:28:28.532 +10:00 INF] Starting LEP Invoicer at "2025-06-17T17:28:28.5319827+10:00"
[2025-06-17 17:28:28.535 +10:00 INF] Initializing database service...
[2025-06-17 17:28:28.541 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 17:28:28.643 +10:00 INF] Database connection established successfully
[2025-06-17 17:28:28.645 +10:00 INF] Database service initialized successfully
[2025-06-17 17:28:28.648 +10:00 INF] Checking for pending work...
[2025-06-17 17:28:28.652 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 17:28:29.520 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 17:28:29.523 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 17:28:29.539 +10:00 INF] Found 0 credits to invoice
[2025-06-17 17:28:29.541 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 17:28:29.544 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 17:28:29.546 +10:00 INF] No pending work found
[2025-06-17 17:28:29.547 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 17:28:29.550 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 17:28:29.630 +10:00 INF] LEP Invoicer completed successfully in 1098ms. No work to process.
[2025-06-17 17:28:29.636 +10:00 INF] Database connection disposed
[2025-06-17 17:28:29.638 +10:00 INF] LEP Invoicer completed with result: 0
