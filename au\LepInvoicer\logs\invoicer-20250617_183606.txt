[2025-06-17 18:36:06.143 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 18:36:06.175 +10:00 INF] Initializing FastReport...
[2025-06-17 18:36:06.253 +10:00 INF] FastReport initialized successfully
[2025-06-17 18:36:06.765 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 18:36:08.120 +10:00 INF] Starting LEP Invoicer at "2025-06-17T18:36:08.1203736+10:00"
[2025-06-17 18:36:08.124 +10:00 INF] Initializing database service...
[2025-06-17 18:36:08.126 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 18:36:08.224 +10:00 INF] Database connection established successfully
[2025-06-17 18:36:08.226 +10:00 INF] Database service initialized successfully
[2025-06-17 18:36:08.229 +10:00 INF] Checking for pending work...
[2025-06-17 18:36:08.231 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 18:36:09.166 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 18:36:09.169 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 18:36:09.185 +10:00 INF] Found 0 credits to invoice
[2025-06-17 18:36:09.194 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 18:36:09.200 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 18:36:09.212 +10:00 INF] No pending work found
[2025-06-17 18:36:09.217 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 18:36:09.219 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 18:36:09.322 +10:00 INF] LEP Invoicer completed successfully in 1201ms. No work to process.
[2025-06-17 18:36:09.329 +10:00 INF] Database connection disposed
[2025-06-17 18:36:09.331 +10:00 INF] LEP Invoicer completed with result: 0
