[2025-06-17 18:18:36.782 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 18:18:36.809 +10:00 INF] Initializing FastReport...
[2025-06-17 18:18:36.880 +10:00 INF] FastReport initialized successfully
[2025-06-17 18:18:37.325 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 18:18:38.593 +10:00 INF] Starting LEP Invoicer at "2025-06-17T18:18:38.5935860+10:00"
[2025-06-17 18:18:38.597 +10:00 INF] Initializing database service...
[2025-06-17 18:18:38.600 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 18:18:38.699 +10:00 INF] Database connection established successfully
[2025-06-17 18:18:38.701 +10:00 INF] Database service initialized successfully
[2025-06-17 18:18:38.703 +10:00 INF] Checking for pending work...
[2025-06-17 18:18:38.706 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 18:18:39.576 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 18:18:39.579 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 18:18:39.593 +10:00 INF] Found 0 credits to invoice
[2025-06-17 18:18:39.595 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 18:18:39.600 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 18:18:39.602 +10:00 INF] No pending work found
[2025-06-17 18:18:39.603 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 18:18:39.615 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 18:18:39.702 +10:00 INF] LEP Invoicer completed successfully in 1109ms. No work to process.
[2025-06-17 18:18:39.718 +10:00 INF] Database connection disposed
[2025-06-17 18:18:39.720 +10:00 INF] LEP Invoicer completed with result: 0
