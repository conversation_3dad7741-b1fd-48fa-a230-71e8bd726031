[2025-06-17 15:23:47.819 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 15:23:47.847 +10:00 INF] Initializing FastReport...
[2025-06-17 15:23:47.938 +10:00 INF] FastReport initialized successfully
[2025-06-17 15:23:48.407 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 15:23:50.049 +10:00 INF] Starting LEP Invoicer at "2025-06-17T15:23:50.0495525+10:00"
[2025-06-17 15:23:50.055 +10:00 INF] Initializing database service...
[2025-06-17 15:23:50.059 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 15:23:50.227 +10:00 INF] Database connection established successfully
[2025-06-17 15:23:50.229 +10:00 INF] Database service initialized successfully
[2025-06-17 15:23:50.234 +10:00 INF] Checking for pending work...
[2025-06-17 15:23:50.240 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 15:23:51.234 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 15:23:51.240 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 15:23:51.268 +10:00 INF] Found 0 credits to invoice
[2025-06-17 15:23:51.272 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 15:23:51.285 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 15:23:51.293 +10:00 INF] No pending work found
[2025-06-17 15:23:51.316 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 15:23:51.325 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 15:23:51.425 +10:00 INF] LEP Invoicer completed successfully in 1375ms. No work to process.
[2025-06-17 15:23:51.432 +10:00 INF] Database connection disposed
[2025-06-17 15:23:51.434 +10:00 INF] LEP Invoicer completed with result: 0
