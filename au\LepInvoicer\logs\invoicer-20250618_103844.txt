[2025-06-18 10:38:44.694 +10:00 INF] Starting LEP Invoicer application
[2025-06-18 10:38:44.722 +10:00 INF] Initializing FastReport...
[2025-06-18 10:38:44.800 +10:00 INF] FastReport initialized successfully
[2025-06-18 10:38:45.248 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-18 10:38:46.601 +10:00 INF] Starting LEP Invoicer at "2025-06-18T10:38:46.6015445+10:00"
[2025-06-18 10:38:46.606 +10:00 INF] Initializing database service...
[2025-06-18 10:38:46.610 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-18 10:38:46.724 +10:00 INF] Database connection established successfully
[2025-06-18 10:38:46.726 +10:00 INF] Database service initialized successfully
[2025-06-18 10:38:46.728 +10:00 INF] Checking for pending work...
[2025-06-18 10:38:46.732 +10:00 INF] Getting 500 orders to invoice
[2025-06-18 10:38:47.727 +10:00 INF] Found 0 orders to invoice (filtered 17 candidates)
[2025-06-18 10:38:47.729 +10:00 INF] Getting 100 credits to invoice
[2025-06-18 10:38:47.742 +10:00 INF] Found 0 credits to invoice
[2025-06-18 10:38:47.744 +10:00 INF] Getting 100 refunds to invoice
[2025-06-18 10:38:47.748 +10:00 INF] Found 0 refunds to invoice
[2025-06-18 10:38:47.749 +10:00 INF] No pending work found
[2025-06-18 10:38:47.750 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-18 10:38:47.751 +10:00 INF] Cleaning up invoicer logs...
[2025-06-18 10:38:47.824 +10:00 INF] LEP Invoicer completed successfully in 1223ms. No work to process.
[2025-06-18 10:38:47.831 +10:00 INF] Database connection disposed
[2025-06-18 10:38:47.833 +10:00 INF] LEP Invoicer completed with result: 0
