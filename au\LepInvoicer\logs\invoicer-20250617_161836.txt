[2025-06-17 16:18:36.617 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 16:18:36.661 +10:00 INF] Initializing FastReport...
[2025-06-17 16:18:36.749 +10:00 INF] FastReport initialized successfully
[2025-06-17 16:18:37.192 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 16:18:38.482 +10:00 INF] Starting LEP Invoicer at "2025-06-17T16:18:38.4824388+10:00"
[2025-06-17 16:18:38.486 +10:00 INF] Initializing database service...
[2025-06-17 16:18:38.489 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 16:18:38.591 +10:00 INF] Database connection established successfully
[2025-06-17 16:18:38.592 +10:00 INF] Database service initialized successfully
[2025-06-17 16:18:38.595 +10:00 INF] Checking for pending work...
[2025-06-17 16:18:38.598 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 16:18:39.501 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 16:18:39.504 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 16:18:39.517 +10:00 INF] Found 0 credits to invoice
[2025-06-17 16:18:39.520 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 16:18:39.528 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 16:18:39.531 +10:00 INF] No pending work found
[2025-06-17 16:18:39.532 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 16:18:39.534 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 16:18:39.620 +10:00 INF] LEP Invoicer completed successfully in 1137ms. No work to process.
[2025-06-17 16:18:39.626 +10:00 INF] Database connection disposed
[2025-06-17 16:18:39.628 +10:00 INF] LEP Invoicer completed with result: 0
