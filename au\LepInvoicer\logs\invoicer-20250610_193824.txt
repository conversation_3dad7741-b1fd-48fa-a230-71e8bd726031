[2025-06-10 19:38:24.362 +10:00 INF] Starting LEP Invoicer application
[2025-06-10 19:38:25.121 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-10 19:38:27.238 +10:00 INF] Starting LEP Invoicer at "2025-06-10T19:38:27.2378633+10:00"
[2025-06-10 19:38:27.245 +10:00 INF] Initializing database service...
[2025-06-10 19:38:27.250 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-10 19:38:27.416 +10:00 INF] Database connection established successfully
[2025-06-10 19:38:27.418 +10:00 INF] Database service initialized successfully
[2025-06-10 19:38:27.424 +10:00 INF] Checking for pending work...
[2025-06-10 19:38:27.429 +10:00 INF] Getting 500 orders to invoice
[2025-06-10 19:38:28.344 +10:00 INF] Found 2 orders to invoice
[2025-06-10 19:38:28.349 +10:00 INF] Getting 100 credits to invoice
[2025-06-10 19:38:28.399 +10:00 INF] Found 0 credits to invoice
[2025-06-10 19:38:28.404 +10:00 INF] Getting 100 refunds to invoice
[2025-06-10 19:38:28.420 +10:00 INF] Found 0 refunds to invoice
[2025-06-10 19:38:28.423 +10:00 INF] Found pending work: 2 orders
[2025-06-10 19:38:28.426 +10:00 INF] Initializing MYOB and other services...
[2025-06-10 19:38:28.431 +10:00 INF] Initializing MYOB service...
[2025-06-10 19:38:28.434 +10:00 INF] Initializing MYOB with OAuth authentication
[2025-06-10 19:38:28.438 +10:00 INF] Starting OAuth authentication flow
[2025-06-10 19:38:28.546 +10:00 INF] Using existing OAuth tokens
[2025-06-10 19:38:28.549 +10:00 INF] Initializing MYOB services with OAuth keystore
[2025-06-10 19:38:28.555 +10:00 INF] MYOB services initialized successfully
[2025-06-10 19:38:28.559 +10:00 INF] OAuth keystore set for API calls
[2025-06-10 19:38:28.561 +10:00 INF] Getting company files from MYOB
[2025-06-10 19:38:29.021 +10:00 INF] MYOB service initialized with OAuth credentials. Using company file: LEP Colour Printers Pty Ltd
[2025-06-10 19:38:29.037 +10:00 INF] Current cache stats: {"AccountsCount":269,"TaxCodesCount":18,"CustomersCount":393,"LastCacheLoad":"2025-06-10T19:31:07.5727667+10:00","CacheAge":"00:07:21.4575092","IsExpired":false,"CacheDirectory":"C:\\Users\\<USER>\\AppData\\Local\\LepInvoicer\\cache"}
[2025-06-10 19:38:29.081 +10:00 INF] Using cached MYOB data: {"AccountsCount":269,"TaxCodesCount":18,"CustomersCount":393,"LastCacheLoad":"2025-06-10T19:31:07.5727667+10:00","CacheAge":"00:07:21.4575092","IsExpired":false,"CacheDirectory":"C:\\Users\\<USER>\\AppData\\Local\\LepInvoicer\\cache"}
[2025-06-10 19:38:30.369 +10:00 INF] Using cached GST tax code
[2025-06-10 19:38:30.372 +10:00 INF] Using cached freight account
[2025-06-10 19:38:30.374 +10:00 INF] Using cached discounts account
[2025-06-10 19:38:30.376 +10:00 INF] MYOB cache initialization complete: {"AccountsCount":269,"TaxCodesCount":18,"CustomersCount":393,"LastCacheLoad":"2025-06-10T19:31:07.5727667+10:00","CacheAge":"00:07:22.8037442","IsExpired":false,"CacheDirectory":"C:\\Users\\<USER>\\AppData\\Local\\LepInvoicer\\cache"}
[2025-06-10 19:38:30.398 +10:00 INF] MYOB service initialized successfully with company file: LEP Colour Printers Pty Ltd
[2025-06-10 19:38:30.401 +10:00 INF] All services initialized successfully
[2025-06-10 19:38:30.406 +10:00 INF] Processing order invoices...
[2025-06-10 19:38:30.408 +10:00 INF] Getting 500 orders to invoice
[2025-06-10 19:38:30.736 +10:00 INF] Found 2 orders to invoice
[2025-06-10 19:38:30.742 +10:00 INF] Found 2 orders to process
[2025-06-10 19:38:30.748 +10:00 INF] Getting order 1417006
[2025-06-10 19:38:31.075 +10:00 INF] Processing order 1417006 with 1 jobs, total: ¤0.00
[2025-06-10 19:38:31.082 +10:00 WRN] Order 1417006 has zero price, marking as invoiced
[2025-06-10 19:38:31.087 +10:00 INF] Getting order 1416838
[2025-06-10 19:38:31.099 +10:00 INF] Processing order 1416838 with 1 jobs, total: ¤0.00
[2025-06-10 19:38:31.108 +10:00 WRN] Order 1416838 has zero price, marking as invoiced
[2025-06-10 19:38:31.112 +10:00 INF] Order processing completed. Processed: 2, Success: 0, Failed: 2
[2025-06-10 19:38:31.119 +10:00 INF] Processing credit invoices...
[2025-06-10 19:38:31.121 +10:00 INF] Getting 100 credits to invoice
[2025-06-10 19:38:31.137 +10:00 INF] Found 0 credits to invoice
[2025-06-10 19:38:31.146 +10:00 INF] Processing refund invoices...
[2025-06-10 19:38:31.154 +10:00 INF] Getting 100 refunds to invoice
[2025-06-10 19:38:31.161 +10:00 INF] Found 0 refunds to invoice
[2025-06-10 19:38:31.164 +10:00 INF] Cleaning up invoicer logs...
[2025-06-10 19:38:31.238 +10:00 INF] LEP Invoicer completed successfully in 4000ms. Orders: 2, Credits: 0, Refunds: 0
[2025-06-10 19:38:31.250 +10:00 INF] Database connection disposed
[2025-06-10 19:38:31.254 +10:00 INF] LEP Invoicer completed with result: 0
