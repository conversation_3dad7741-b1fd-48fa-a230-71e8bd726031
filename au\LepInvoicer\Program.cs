using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Serilog;
using LepInvoicer.Implementations;
using LepInvoicer.Interfaces;
using FastReport;

namespace LepInvoicer;

public class Program
{
	public static async Task<int> Main(string[] args)
	{
		// Setup logging first with timestamped filename for each run
		var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
		var logFileName = $"logs/invoicer-{timestamp}.txt";

		Log.Logger = new LoggerConfiguration()
			.MinimumLevel.Information()
			.MinimumLevel.Override("Microsoft", Serilog.Events.LogEventLevel.Warning)
			.MinimumLevel.Override("System", Serilog.Events.LogEventLevel.Warning)
			.WriteTo.Console(outputTemplate: "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj}{NewLine}{Exception}")
			.WriteTo.File(logFileName,
				outputTemplate: "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} {Level:u3}] {Message:lj}{NewLine}{Exception}")
			.CreateLogger();

		try
		{
			Log.Information("Starting LEP Invoicer application");

			// Initialize FastReport
			try
			{
				Log.Information("Initializing FastReport...");
				// Force FastReport initialization in a controlled way
				var tempReport = new Report();
				tempReport.Dispose();
				Log.Information("FastReport initialized successfully");
			}
			catch (Exception frEx)
			{
				Log.Error(frEx, "Failed to initialize FastReport");
				throw;
			}

			var host = CreateHostBuilder(args).Build();
			var invoicer = host.Services.GetRequiredService<IInvoicerService>();

			var result = await invoicer.RunInvoicer();

			Log.Information("LEP Invoicer completed with result: {Result}", result);
			return result;
		}
		catch (Exception ex)
		{
			Log.Fatal(ex, "Application terminated unexpectedly");
			return 1;
		}
		finally
		{
			Log.CloseAndFlush();
		}
	}

	private static IHostBuilder CreateHostBuilder(string[] args) =>
		Host.CreateDefaultBuilder(args)
			.UseSerilog()
			.ConfigureServices((context, services) =>
			{
				services.Configure<InvoicerConfiguration>(context.Configuration.GetSection("Invoicer"));

				// Register NHibernate session factory and session
				services.AddSingleton<NHibernate.ISessionFactory>(provider =>
				{
					var config = provider.GetRequiredService<IOptions<InvoicerConfiguration>>().Value;

					var nhconfig = new NHibernate.Cfg.Configuration();
					nhconfig.SetProperty(NHibernate.Cfg.Environment.Dialect, typeof(NHibernate.Dialect.MsSql2012Dialect).AssemblyQualifiedName);
					nhconfig.SetProperty(NHibernate.Cfg.Environment.ConnectionDriver, typeof(NHibernate.Driver.SqlClientDriver).AssemblyQualifiedName);
					nhconfig.SetProperty(NHibernate.Cfg.Environment.Isolation, "ReadCommitted");
					nhconfig.SetProperty(NHibernate.Cfg.Environment.UseSecondLevelCache, false.ToString());
					nhconfig.SetProperty(NHibernate.Cfg.Environment.ShowSql, false.ToString());
					nhconfig.SetProperty(NHibernate.Cfg.Environment.ConnectionProvider, typeof(NHibernate.Connection.DriverConnectionProvider).AssemblyQualifiedName);
					nhconfig.SetProperty(NHibernate.Cfg.Environment.ConnectionString, config.ConnectionString);
					nhconfig.SetProperty(NHibernate.Cfg.Environment.CurrentSessionContextClass, "www");
					nhconfig.SetProperty(NHibernate.Cfg.Environment.BatchSize, "1000");

					// Add the assembly containing the domain entities
					nhconfig.AddAssembly(typeof(lep.user.ICustomerUser).Assembly);

					return nhconfig.BuildSessionFactory();
				});

				services.AddScoped<NHibernate.ISession>(provider =>
				{
					var sessionFactory = provider.GetRequiredService<NHibernate.ISessionFactory>();
					return sessionFactory.OpenSession();
				});

				// Configure AutoMapper
				var lepProfile = new LepCore.Setup.LepAutoMapperProfile(() => null);
				var mapperConfig = new MapperConfiguration(cfg => { cfg.AddProfile(lepProfile); });
				services.AddSingleton(mapperConfig.CreateMapper());

				// Register services
				services.AddSingleton<MYOBCache>(provider =>
				{
					var logger = provider.GetRequiredService<ILogger<MYOBCache>>();
					// Use a persistent directory outside of bin folder to survive rebuilds
					var cacheDirectory = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData), "LepInvoicer");
					return new MYOBCache(logger, cacheDirectory);
				});
				services.AddScoped<IInvoicerService, InvoicerService>();
				services.AddScoped<IMYOBService, MYOBService>();
				services.AddScoped<IEmailService, EmailService>();
				services.AddScoped<LepInvoicer.Interfaces.IPdfService, PdfService>();
				services.AddScoped<IDatabaseService, DatabaseService>();
				// Note: OAuthKeyService is created internally by MYOBService, not injected
			});
}
