using System;
using System.Diagnostics;
using System.Drawing.Printing;
using System.IO;
using FastReport.Export.PdfSimple;
using Serilog;

namespace lep
{
    public static class PrintUtils
    {
        /// <summary>
        /// printer names are now used to store printer name and paper tray name
        /// this function is used to assign PrinterName and PaperSource to a PrinterSetting
        /// </summary>
        /// <param name="printerNameAndTray"></param>
        /// <param name="p"></param>
        public static void ConfigurePrinterSettings(string printerNameAndTray, ref PrinterSettings p)
        {
            var x = printerNameAndTray.Split(new char[] { '|' }, 2, StringSplitOptions.None);
            p.PrinterName = x[0];
            foreach (PaperSource ps in p.PaperSources)
            {
                if (x.Length > 1)
                {
                    if (ps.SourceName == x[1])
                    {
                        p.DefaultPageSettings.PaperSource = ps;
                        break;
                    }
                }
                else
                {


                }

            }
        }

        public static void PrintToPrinter(string fileName, string printerName, int copies = 1)
        {
            var pdfToPrinterPath = LepGlobal.Instance.PDF2PrinterPath;
            var arguments = $@"  ""{fileName}""  ""{printerName}""  ";

            if (copies > 1)
            {
                arguments += $" copies={copies}";
            }

            System.Diagnostics.Process.Start(new ProcessStartInfo
            {
                UseShellExecute = true,
                FileName = pdfToPrinterPath,
                Arguments = arguments,
                CreateNoWindow = true,
                WindowStyle = ProcessWindowStyle.Hidden
            });
        }



        public static void PrintReport(FastReport.Report report, string printerName, int copies, string printName = null)
        {
            //printerName = @"\\henry\" + printerName;

            report.Prepare();
            //using (System.Security.Principal.WindowsImpersonationContext wic = System.Security.Principal.WindowsIdentity.Impersonate(IntPtr.Zero))
            {
                try
                {
                    var fileName = $"{printName}-{System.DateTime.Now.Ticks}.pdf";
                    using (var ms = new MemoryStream())
                    {
                        var pdfExport = new PDFSimpleExport();
                        pdfExport.Export(report, ms);

                        //write the pdf to file
                        var fullPath = Path.Combine(LepGlobal.Instance.DataDirectory.FullName, "TempUploads", fileName);
                        using (var file = new FileStream(fullPath, FileMode.Create, FileAccess.Write))
                        {
                            ms.WriteTo(file);
                            file.Close();
                        }

                        Log.Information("{Printing} {fileName}   ->  {printerName} ", "Print", fileName, fullPath);

                        if (!printerName.Contains("PDF"))
                        {
                            PrintUtils.PrintToPrinter(fullPath, printerName, copies);
                        }
                    }
                }
                catch (Exception ex)
                {
                    var m = ex.Message;
                    if ("The printer name is invalid" == m)
                        m += " -> " + printerName;

                    Log.Error(ex, m);
                }
            }
        }
    }
}
