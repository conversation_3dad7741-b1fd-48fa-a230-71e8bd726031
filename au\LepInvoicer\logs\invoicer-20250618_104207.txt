[2025-06-18 10:42:07.182 +10:00 INF] Starting LEP Invoicer application
[2025-06-18 10:42:07.225 +10:00 INF] Initializing FastReport...
[2025-06-18 10:42:07.328 +10:00 INF] FastReport initialized successfully
[2025-06-18 10:42:07.850 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-18 10:42:10.108 +10:00 INF] Starting LEP Invoicer at "2025-06-18T10:42:10.1075951+10:00"
[2025-06-18 10:42:10.115 +10:00 INF] Initializing database service...
[2025-06-18 10:42:10.131 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-18 10:42:10.271 +10:00 INF] Database connection established successfully
[2025-06-18 10:42:10.272 +10:00 INF] Database service initialized successfully
[2025-06-18 10:42:10.274 +10:00 INF] Checking for pending work...
[2025-06-18 10:42:10.278 +10:00 INF] Getting 500 orders to invoice
[2025-06-18 10:42:11.230 +10:00 INF] Found 0 orders to invoice (filtered 17 candidates)
[2025-06-18 10:42:11.233 +10:00 INF] Getting 100 credits to invoice
[2025-06-18 10:42:11.245 +10:00 INF] Found 0 credits to invoice
[2025-06-18 10:42:11.247 +10:00 INF] Getting 100 refunds to invoice
[2025-06-18 10:42:11.251 +10:00 INF] Found 0 refunds to invoice
[2025-06-18 10:42:11.252 +10:00 INF] No pending work found
[2025-06-18 10:42:11.253 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-18 10:42:11.259 +10:00 INF] Cleaning up invoicer logs...
[2025-06-18 10:42:11.344 +10:00 INF] LEP Invoicer completed successfully in 1237ms. No work to process.
[2025-06-18 10:42:11.351 +10:00 INF] Database connection disposed
[2025-06-18 10:42:11.353 +10:00 INF] LEP Invoicer completed with result: 0
