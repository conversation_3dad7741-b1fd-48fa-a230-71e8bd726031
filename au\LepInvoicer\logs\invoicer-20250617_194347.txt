[2025-06-17 19:43:47.670 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 19:43:47.699 +10:00 INF] Initializing FastReport...
[2025-06-17 19:43:47.793 +10:00 INF] FastReport initialized successfully
[2025-06-17 19:43:48.213 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 19:43:49.436 +10:00 INF] Starting LEP Invoicer at "2025-06-17T19:43:49.4361136+10:00"
[2025-06-17 19:43:49.440 +10:00 INF] Initializing database service...
[2025-06-17 19:43:49.443 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 19:43:49.546 +10:00 INF] Database connection established successfully
[2025-06-17 19:43:49.556 +10:00 INF] Database service initialized successfully
[2025-06-17 19:43:49.563 +10:00 INF] Checking for pending work...
[2025-06-17 19:43:49.568 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 19:43:50.442 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 19:43:50.445 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 19:43:50.456 +10:00 INF] Found 0 credits to invoice
[2025-06-17 19:43:50.458 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 19:43:50.461 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 19:43:50.464 +10:00 INF] No pending work found
[2025-06-17 19:43:50.466 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 19:43:50.470 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 19:43:50.554 +10:00 INF] LEP Invoicer completed successfully in 1118ms. No work to process.
[2025-06-17 19:43:50.561 +10:00 INF] Database connection disposed
[2025-06-17 19:43:50.563 +10:00 INF] LEP Invoicer completed with result: 0
