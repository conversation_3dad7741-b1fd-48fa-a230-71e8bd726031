[2025-06-18 07:46:43.652 +10:00 INF] Starting LEP Invoicer application
[2025-06-18 07:46:43.688 +10:00 INF] Initializing FastReport...
[2025-06-18 07:46:43.770 +10:00 INF] FastReport initialized successfully
[2025-06-18 07:46:44.181 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-18 07:46:45.644 +10:00 INF] Starting LEP Invoicer at "2025-06-18T07:46:45.6438690+10:00"
[2025-06-18 07:46:45.647 +10:00 INF] Initializing database service...
[2025-06-18 07:46:45.650 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-18 07:46:45.753 +10:00 INF] Database connection established successfully
[2025-06-18 07:46:45.755 +10:00 INF] Database service initialized successfully
[2025-06-18 07:46:45.758 +10:00 INF] Checking for pending work...
[2025-06-18 07:46:45.761 +10:00 INF] Getting 500 orders to invoice
[2025-06-18 07:46:46.865 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-18 07:46:46.868 +10:00 INF] Getting 100 credits to invoice
[2025-06-18 07:46:46.882 +10:00 INF] Found 0 credits to invoice
[2025-06-18 07:46:46.884 +10:00 INF] Getting 100 refunds to invoice
[2025-06-18 07:46:46.889 +10:00 INF] Found 0 refunds to invoice
[2025-06-18 07:46:46.890 +10:00 INF] No pending work found
[2025-06-18 07:46:46.891 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-18 07:46:46.893 +10:00 INF] Cleaning up invoicer logs...
[2025-06-18 07:46:46.984 +10:00 INF] LEP Invoicer completed successfully in 1340ms. No work to process.
[2025-06-18 07:46:46.991 +10:00 INF] Database connection disposed
[2025-06-18 07:46:46.993 +10:00 INF] LEP Invoicer completed with result: 0
