using lep.user;

namespace lep.security
{
	public interface ISecurityApplication
    {
        IUser Identity { get; }

        //bool HasAnyTask(string app);
        //bool HasTask(string app, string task);

        bool AttemptLogin(string username, string password);

        bool HasSession();

        void AbandonSession();

        //void AssertPermission(string task);
        void AssertIsCustomerUser(ICustomerUser customer);

        void DoPrivileged(PrivilegeDelegate func);

        IStaff GetSystemUser();
    }
}