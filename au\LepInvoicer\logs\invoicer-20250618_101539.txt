[2025-06-18 10:15:39.628 +10:00 INF] Starting LEP Invoicer application
[2025-06-18 10:15:39.655 +10:00 INF] Initializing FastReport...
[2025-06-18 10:15:39.725 +10:00 INF] FastReport initialized successfully
[2025-06-18 10:15:40.177 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-18 10:15:41.542 +10:00 INF] Starting LEP Invoicer at "2025-06-18T10:15:41.5423490+10:00"
[2025-06-18 10:15:41.558 +10:00 INF] Initializing database service...
[2025-06-18 10:15:41.561 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-18 10:15:41.666 +10:00 INF] Database connection established successfully
[2025-06-18 10:15:41.667 +10:00 INF] Database service initialized successfully
[2025-06-18 10:15:41.670 +10:00 INF] Checking for pending work...
[2025-06-18 10:15:41.673 +10:00 INF] Getting 500 orders to invoice
[2025-06-18 10:15:42.587 +10:00 INF] Found 0 orders to invoice (filtered 16 candidates)
[2025-06-18 10:15:42.591 +10:00 INF] Getting 100 credits to invoice
[2025-06-18 10:15:42.604 +10:00 INF] Found 0 credits to invoice
[2025-06-18 10:15:42.627 +10:00 INF] Getting 100 refunds to invoice
[2025-06-18 10:15:42.632 +10:00 INF] Found 0 refunds to invoice
[2025-06-18 10:15:42.633 +10:00 INF] No pending work found
[2025-06-18 10:15:42.634 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-18 10:15:42.636 +10:00 INF] Cleaning up invoicer logs...
[2025-06-18 10:15:42.734 +10:00 INF] LEP Invoicer completed successfully in 1191ms. No work to process.
[2025-06-18 10:15:42.740 +10:00 INF] Database connection disposed
[2025-06-18 10:15:42.742 +10:00 INF] LEP Invoicer completed with result: 0
