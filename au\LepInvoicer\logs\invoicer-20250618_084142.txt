[2025-06-18 08:41:42.572 +10:00 INF] Starting LEP Invoicer application
[2025-06-18 08:41:42.599 +10:00 INF] Initializing FastReport...
[2025-06-18 08:41:42.680 +10:00 INF] FastReport initialized successfully
[2025-06-18 08:41:43.085 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-18 08:41:44.343 +10:00 INF] Starting LEP Invoicer at "2025-06-18T08:41:44.3435171+10:00"
[2025-06-18 08:41:44.347 +10:00 INF] Initializing database service...
[2025-06-18 08:41:44.350 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-18 08:41:44.449 +10:00 INF] Database connection established successfully
[2025-06-18 08:41:44.450 +10:00 INF] Database service initialized successfully
[2025-06-18 08:41:44.453 +10:00 INF] Checking for pending work...
[2025-06-18 08:41:44.457 +10:00 INF] Getting 500 orders to invoice
[2025-06-18 08:41:45.399 +10:00 INF] Found 0 orders to invoice (filtered 16 candidates)
[2025-06-18 08:41:45.408 +10:00 INF] Getting 100 credits to invoice
[2025-06-18 08:41:45.421 +10:00 INF] Found 0 credits to invoice
[2025-06-18 08:41:45.422 +10:00 INF] Getting 100 refunds to invoice
[2025-06-18 08:41:45.427 +10:00 INF] Found 0 refunds to invoice
[2025-06-18 08:41:45.430 +10:00 INF] No pending work found
[2025-06-18 08:41:45.431 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-18 08:41:45.435 +10:00 INF] Cleaning up invoicer logs...
[2025-06-18 08:41:45.516 +10:00 INF] LEP Invoicer completed successfully in 1173ms. No work to process.
[2025-06-18 08:41:45.523 +10:00 INF] Database connection disposed
[2025-06-18 08:41:45.525 +10:00 INF] LEP Invoicer completed with result: 0
