[2025-06-17 19:33:55.796 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 19:33:55.826 +10:00 INF] Initializing FastReport...
[2025-06-17 19:33:55.944 +10:00 INF] FastReport initialized successfully
[2025-06-17 19:33:56.659 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 19:33:57.990 +10:00 INF] Starting LEP Invoicer at "2025-06-17T19:33:57.9901515+10:00"
[2025-06-17 19:33:57.993 +10:00 INF] Initializing database service...
[2025-06-17 19:33:57.996 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 19:33:58.098 +10:00 INF] Database connection established successfully
[2025-06-17 19:33:58.099 +10:00 INF] Database service initialized successfully
[2025-06-17 19:33:58.102 +10:00 INF] Checking for pending work...
[2025-06-17 19:33:58.105 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 19:33:59.072 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 19:33:59.075 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 19:33:59.087 +10:00 INF] Found 0 credits to invoice
[2025-06-17 19:33:59.089 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 19:33:59.093 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 19:33:59.095 +10:00 INF] No pending work found
[2025-06-17 19:33:59.096 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 19:33:59.098 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 19:33:59.194 +10:00 INF] LEP Invoicer completed successfully in 1204ms. No work to process.
[2025-06-17 19:33:59.201 +10:00 INF] Database connection disposed
[2025-06-17 19:33:59.208 +10:00 INF] LEP Invoicer completed with result: 0
