[2025-06-17 18:37:11.595 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 18:37:11.624 +10:00 INF] Initializing FastReport...
[2025-06-17 18:37:11.706 +10:00 INF] FastReport initialized successfully
[2025-06-17 18:37:12.130 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 18:37:13.592 +10:00 INF] Starting LEP Invoicer at "2025-06-17T18:37:13.5914373+10:00"
[2025-06-17 18:37:13.595 +10:00 INF] Initializing database service...
[2025-06-17 18:37:13.598 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 18:37:13.699 +10:00 INF] Database connection established successfully
[2025-06-17 18:37:13.700 +10:00 INF] Database service initialized successfully
[2025-06-17 18:37:13.703 +10:00 INF] Checking for pending work...
[2025-06-17 18:37:13.706 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 18:37:14.717 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 18:37:14.719 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 18:37:14.732 +10:00 INF] Found 0 credits to invoice
[2025-06-17 18:37:14.735 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 18:37:14.740 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 18:37:14.741 +10:00 INF] No pending work found
[2025-06-17 18:37:14.742 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 18:37:14.744 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 18:37:14.821 +10:00 INF] LEP Invoicer completed successfully in 1230ms. No work to process.
[2025-06-17 18:37:14.832 +10:00 INF] Database connection disposed
[2025-06-17 18:37:14.849 +10:00 INF] LEP Invoicer completed with result: 0
