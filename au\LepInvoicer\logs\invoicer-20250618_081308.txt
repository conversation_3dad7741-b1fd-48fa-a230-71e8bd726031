[2025-06-18 08:13:08.707 +10:00 INF] Starting LEP Invoicer application
[2025-06-18 08:13:08.734 +10:00 INF] Initializing FastReport...
[2025-06-18 08:13:08.806 +10:00 INF] FastReport initialized successfully
[2025-06-18 08:13:09.245 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-18 08:13:10.551 +10:00 INF] Starting LEP Invoicer at "2025-06-18T08:13:10.5513510+10:00"
[2025-06-18 08:13:10.555 +10:00 INF] Initializing database service...
[2025-06-18 08:13:10.558 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-18 08:13:10.664 +10:00 INF] Database connection established successfully
[2025-06-18 08:13:10.666 +10:00 INF] Database service initialized successfully
[2025-06-18 08:13:10.669 +10:00 INF] Checking for pending work...
[2025-06-18 08:13:10.672 +10:00 INF] Getting 500 orders to invoice
[2025-06-18 08:13:11.687 +10:00 INF] Found 0 orders to invoice (filtered 16 candidates)
[2025-06-18 08:13:11.689 +10:00 INF] Getting 100 credits to invoice
[2025-06-18 08:13:11.706 +10:00 INF] Found 0 credits to invoice
[2025-06-18 08:13:11.708 +10:00 INF] Getting 100 refunds to invoice
[2025-06-18 08:13:11.712 +10:00 INF] Found 0 refunds to invoice
[2025-06-18 08:13:11.713 +10:00 INF] No pending work found
[2025-06-18 08:13:11.715 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-18 08:13:11.719 +10:00 INF] Cleaning up invoicer logs...
[2025-06-18 08:13:11.791 +10:00 INF] LEP Invoicer completed successfully in 1239ms. No work to process.
[2025-06-18 08:13:11.810 +10:00 INF] Database connection disposed
[2025-06-18 08:13:11.814 +10:00 INF] LEP Invoicer completed with result: 0
