[2025-06-17 19:31:45.521 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 19:31:45.555 +10:00 INF] Initializing FastReport...
[2025-06-17 19:31:45.647 +10:00 INF] FastReport initialized successfully
[2025-06-17 19:31:46.060 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 19:31:47.301 +10:00 INF] Starting LEP Invoicer at "2025-06-17T19:31:47.3012029+10:00"
[2025-06-17 19:31:47.305 +10:00 INF] Initializing database service...
[2025-06-17 19:31:47.308 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 19:31:47.410 +10:00 INF] Database connection established successfully
[2025-06-17 19:31:47.412 +10:00 INF] Database service initialized successfully
[2025-06-17 19:31:47.414 +10:00 INF] Checking for pending work...
[2025-06-17 19:31:47.417 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 19:31:48.290 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 19:31:48.292 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 19:31:48.313 +10:00 INF] Found 0 credits to invoice
[2025-06-17 19:31:48.315 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 19:31:48.321 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 19:31:48.325 +10:00 INF] No pending work found
[2025-06-17 19:31:48.327 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 19:31:48.329 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 19:31:48.411 +10:00 INF] LEP Invoicer completed successfully in 1109ms. No work to process.
[2025-06-17 19:31:48.421 +10:00 INF] Database connection disposed
[2025-06-17 19:31:48.429 +10:00 INF] LEP Invoicer completed with result: 0
