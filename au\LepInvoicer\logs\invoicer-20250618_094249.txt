[2025-06-18 09:42:49.656 +10:00 INF] Starting LEP Invoicer application
[2025-06-18 09:42:49.684 +10:00 INF] Initializing FastReport...
[2025-06-18 09:42:49.759 +10:00 INF] FastReport initialized successfully
[2025-06-18 09:42:50.190 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-18 09:42:51.604 +10:00 INF] Starting LEP Invoicer at "2025-06-18T09:42:51.6039948+10:00"
[2025-06-18 09:42:51.608 +10:00 INF] Initializing database service...
[2025-06-18 09:42:51.611 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-18 09:42:51.707 +10:00 INF] Database connection established successfully
[2025-06-18 09:42:51.708 +10:00 INF] Database service initialized successfully
[2025-06-18 09:42:51.710 +10:00 INF] Checking for pending work...
[2025-06-18 09:42:51.713 +10:00 INF] Getting 500 orders to invoice
[2025-06-18 09:42:52.589 +10:00 INF] Found 0 orders to invoice (filtered 16 candidates)
[2025-06-18 09:42:52.592 +10:00 INF] Getting 100 credits to invoice
[2025-06-18 09:42:52.607 +10:00 INF] Found 0 credits to invoice
[2025-06-18 09:42:52.609 +10:00 INF] Getting 100 refunds to invoice
[2025-06-18 09:42:52.620 +10:00 INF] Found 0 refunds to invoice
[2025-06-18 09:42:52.627 +10:00 INF] No pending work found
[2025-06-18 09:42:52.628 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-18 09:42:52.630 +10:00 INF] Cleaning up invoicer logs...
[2025-06-18 09:42:52.713 +10:00 INF] LEP Invoicer completed successfully in 1109ms. No work to process.
[2025-06-18 09:42:52.726 +10:00 INF] Database connection disposed
[2025-06-18 09:42:52.729 +10:00 INF] LEP Invoicer completed with result: 0
