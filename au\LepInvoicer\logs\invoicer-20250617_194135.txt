[2025-06-17 19:41:35.997 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 19:41:36.027 +10:00 INF] Initializing FastReport...
[2025-06-17 19:41:36.106 +10:00 INF] FastReport initialized successfully
[2025-06-17 19:41:36.492 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 19:41:37.854 +10:00 INF] Starting LEP Invoicer at "2025-06-17T19:41:37.8537514+10:00"
[2025-06-17 19:41:37.857 +10:00 INF] Initializing database service...
[2025-06-17 19:41:37.860 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 19:41:37.959 +10:00 INF] Database connection established successfully
[2025-06-17 19:41:37.980 +10:00 INF] Database service initialized successfully
[2025-06-17 19:41:37.985 +10:00 INF] Checking for pending work...
[2025-06-17 19:41:37.989 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 19:41:38.873 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 19:41:38.875 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 19:41:38.889 +10:00 INF] Found 0 credits to invoice
[2025-06-17 19:41:38.891 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 19:41:38.894 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 19:41:38.898 +10:00 INF] No pending work found
[2025-06-17 19:41:38.901 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 19:41:38.908 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 19:41:39.004 +10:00 INF] LEP Invoicer completed successfully in 1150ms. No work to process.
[2025-06-17 19:41:39.010 +10:00 INF] Database connection disposed
[2025-06-17 19:41:39.013 +10:00 INF] LEP Invoicer completed with result: 0
