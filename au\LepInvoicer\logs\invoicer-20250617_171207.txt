[2025-06-17 17:12:07.573 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 17:12:07.603 +10:00 INF] Initializing FastReport...
[2025-06-17 17:12:07.685 +10:00 INF] FastReport initialized successfully
[2025-06-17 17:12:08.266 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 17:12:09.883 +10:00 INF] Starting LEP Invoicer at "2025-06-17T17:12:09.8830550+10:00"
[2025-06-17 17:12:09.887 +10:00 INF] Initializing database service...
[2025-06-17 17:12:09.890 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 17:12:09.995 +10:00 INF] Database connection established successfully
[2025-06-17 17:12:09.996 +10:00 INF] Database service initialized successfully
[2025-06-17 17:12:10.001 +10:00 INF] Checking for pending work...
[2025-06-17 17:12:10.004 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 17:12:11.059 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 17:12:11.061 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 17:12:11.074 +10:00 INF] Found 0 credits to invoice
[2025-06-17 17:12:11.076 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 17:12:11.080 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 17:12:11.081 +10:00 INF] No pending work found
[2025-06-17 17:12:11.083 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 17:12:11.084 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 17:12:11.164 +10:00 INF] LEP Invoicer completed successfully in 1280ms. No work to process.
[2025-06-17 17:12:11.171 +10:00 INF] Database connection disposed
[2025-06-17 17:12:11.173 +10:00 INF] LEP Invoicer completed with result: 0
