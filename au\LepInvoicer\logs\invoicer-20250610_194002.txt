[2025-06-10 19:40:02.736 +10:00 INF] Starting LEP Invoicer application
[2025-06-10 19:40:03.637 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-10 19:40:06.034 +10:00 INF] Starting LEP Invoicer at "2025-06-10T19:40:06.0344692+10:00"
[2025-06-10 19:40:06.040 +10:00 INF] Initializing database service...
[2025-06-10 19:40:06.044 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-10 19:40:06.231 +10:00 INF] Database connection established successfully
[2025-06-10 19:40:06.255 +10:00 INF] Database service initialized successfully
[2025-06-10 19:40:06.265 +10:00 INF] Checking for pending work...
[2025-06-10 19:40:06.270 +10:00 INF] Getting 500 orders to invoice
[2025-06-10 19:40:07.228 +10:00 INF] Found 2 orders to invoice
[2025-06-10 19:40:07.263 +10:00 INF] Getting 100 credits to invoice
[2025-06-10 19:40:07.312 +10:00 INF] Found 0 credits to invoice
[2025-06-10 19:40:07.316 +10:00 INF] Getting 100 refunds to invoice
[2025-06-10 19:40:07.332 +10:00 INF] Found 0 refunds to invoice
[2025-06-10 19:40:07.335 +10:00 INF] Found pending work: 2 orders
[2025-06-10 19:40:07.339 +10:00 INF] Initializing MYOB and other services...
[2025-06-10 19:40:07.346 +10:00 INF] Initializing MYOB service...
[2025-06-10 19:40:07.373 +10:00 INF] Initializing MYOB with OAuth authentication
[2025-06-10 19:40:07.378 +10:00 INF] Starting OAuth authentication flow
[2025-06-10 19:40:07.512 +10:00 INF] Using existing OAuth tokens
[2025-06-10 19:40:07.514 +10:00 INF] Initializing MYOB services with OAuth keystore
[2025-06-10 19:40:07.520 +10:00 INF] MYOB services initialized successfully
[2025-06-10 19:40:07.522 +10:00 INF] OAuth keystore set for API calls
[2025-06-10 19:40:07.524 +10:00 INF] Getting company files from MYOB
[2025-06-10 19:40:07.843 +10:00 INF] MYOB service initialized with OAuth credentials. Using company file: LEP Colour Printers Pty Ltd
[2025-06-10 19:40:07.856 +10:00 INF] Current cache stats: {"AccountsCount":269,"TaxCodesCount":18,"CustomersCount":393,"LastCacheLoad":"2025-06-10T19:31:07.5727667+10:00","CacheAge":"00:09:00.2777992","IsExpired":false,"CacheDirectory":"C:\\Users\\<USER>\\AppData\\Local\\LepInvoicer\\cache"}
[2025-06-10 19:40:07.914 +10:00 INF] Using cached MYOB data: {"AccountsCount":269,"TaxCodesCount":18,"CustomersCount":393,"LastCacheLoad":"2025-06-10T19:31:07.5727667+10:00","CacheAge":"00:09:00.2777992","IsExpired":false,"CacheDirectory":"C:\\Users\\<USER>\\AppData\\Local\\LepInvoicer\\cache"}
[2025-06-10 19:40:08.615 +10:00 INF] Using cached GST tax code
[2025-06-10 19:40:08.619 +10:00 INF] Using cached freight account
[2025-06-10 19:40:08.621 +10:00 INF] Using cached discounts account
[2025-06-10 19:40:08.624 +10:00 INF] MYOB cache initialization complete: {"AccountsCount":269,"TaxCodesCount":18,"CustomersCount":393,"LastCacheLoad":"2025-06-10T19:31:07.5727667+10:00","CacheAge":"00:09:01.0512674","IsExpired":false,"CacheDirectory":"C:\\Users\\<USER>\\AppData\\Local\\LepInvoicer\\cache"}
[2025-06-10 19:40:08.641 +10:00 INF] MYOB service initialized successfully with company file: LEP Colour Printers Pty Ltd
[2025-06-10 19:40:08.648 +10:00 INF] All services initialized successfully
[2025-06-10 19:40:08.653 +10:00 INF] Processing order invoices...
[2025-06-10 19:40:08.659 +10:00 INF] Getting 500 orders to invoice
[2025-06-10 19:40:09.008 +10:00 INF] Found 2 orders to invoice
[2025-06-10 19:40:09.014 +10:00 INF] Found 2 orders to process
[2025-06-10 19:40:09.019 +10:00 INF] Getting order 1417006
[2025-06-10 19:40:09.367 +10:00 INF] Processing order 1417006 with 1 jobs, total: ¤0.00
[2025-06-10 19:40:09.374 +10:00 WRN] Order 1417006 has zero price, marking as invoiced
[2025-06-10 19:40:09.380 +10:00 INF] Getting order 1416838
[2025-06-10 19:40:09.390 +10:00 INF] Processing order 1416838 with 1 jobs, total: ¤0.00
[2025-06-10 19:40:09.395 +10:00 WRN] Order 1416838 has zero price, marking as invoiced
[2025-06-10 19:40:09.398 +10:00 INF] Order processing completed. Processed: 2, Success: 0, Failed: 2
[2025-06-10 19:40:09.404 +10:00 INF] Processing credit invoices...
[2025-06-10 19:40:09.407 +10:00 INF] Getting 100 credits to invoice
[2025-06-10 19:40:09.416 +10:00 INF] Found 0 credits to invoice
[2025-06-10 19:40:09.424 +10:00 INF] Processing refund invoices...
[2025-06-10 19:40:09.426 +10:00 INF] Getting 100 refunds to invoice
[2025-06-10 19:40:09.434 +10:00 INF] Found 0 refunds to invoice
[2025-06-10 19:40:09.457 +10:00 INF] Cleaning up invoicer logs...
[2025-06-10 19:40:09.531 +10:00 INF] LEP Invoicer completed successfully in 3496ms. Orders: 2, Credits: 0, Refunds: 0
[2025-06-10 19:40:09.568 +10:00 INF] Database connection disposed
[2025-06-10 19:40:09.571 +10:00 INF] LEP Invoicer completed with result: 0
