[2025-06-17 20:26:21.587 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 20:26:21.613 +10:00 INF] Initializing FastReport...
[2025-06-17 20:26:21.688 +10:00 INF] FastReport initialized successfully
[2025-06-17 20:26:22.142 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 20:26:23.474 +10:00 INF] Starting LEP Invoicer at "2025-06-17T20:26:23.4745424+10:00"
[2025-06-17 20:26:23.478 +10:00 INF] Initializing database service...
[2025-06-17 20:26:23.481 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 20:26:23.578 +10:00 INF] Database connection established successfully
[2025-06-17 20:26:23.579 +10:00 INF] Database service initialized successfully
[2025-06-17 20:26:23.582 +10:00 INF] Checking for pending work...
[2025-06-17 20:26:23.584 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 20:26:24.431 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 20:26:24.433 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 20:26:24.445 +10:00 INF] Found 0 credits to invoice
[2025-06-17 20:26:24.447 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 20:26:24.450 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 20:26:24.453 +10:00 INF] No pending work found
[2025-06-17 20:26:24.454 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 20:26:24.455 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 20:26:24.527 +10:00 INF] LEP Invoicer completed successfully in 1052ms. No work to process.
[2025-06-17 20:26:24.533 +10:00 INF] Database connection disposed
[2025-06-17 20:26:24.535 +10:00 INF] LEP Invoicer completed with result: 0
