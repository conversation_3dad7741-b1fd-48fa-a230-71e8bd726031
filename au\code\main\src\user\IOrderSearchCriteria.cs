using lep.job;
using lep.run;

namespace lep.user
{
	public interface IOrderSearchCriteria
    {
        int Id { get; set; }
        bool IsSearchPanelOpen { get; set; }
        OrderStatusOptions? OrderStatus { get; set; }
        JobTypeOptions? JobType { get; set; }
        IPaperSize Size { get; set; }
        IStock Stock { get; set; }
        RunCelloglazeOptions? Celloglaze { get; set; }
        string Customer { get; set; }
        string OrderNr { get; set; }
        string JobNr { get; set; }
        bool IsNewOrder { get; set; }
        bool IsOnlyUrgentOrder { get; set; }
        bool IsOnPrepay { get; set; }
        bool IsOnhold { get; set; }
        bool IsAwaitingPayment { get; set; }
        bool IsCorrectedOrder { get; set; }
        bool IsOpenOrder { get; set; }
        bool IsNonBusinessCard { get; set; }
        bool IsWaitingApproval { get; set; }
        bool IsWithdraw { get; set; }
        bool IsQuoteRequired { get; set; }
        string Ordering { get; set; }
        int Page { get; set; }
        IUser Staff { get; set; }
        bool IsOrderWithDigitalJob { get; set; }
        bool IsOrderWithOutworkJob { get; set; }
        bool HideOnHoldOrders { get; set; }
        bool ShowOnlyOnHoldOrders { get; set; }
    }
}
