[2025-06-17 20:25:16.501 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 20:25:16.530 +10:00 INF] Initializing FastReport...
[2025-06-17 20:25:16.602 +10:00 INF] FastReport initialized successfully
[2025-06-17 20:25:17.027 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 20:25:18.274 +10:00 INF] Starting LEP Invoicer at "2025-06-17T20:25:18.2740594+10:00"
[2025-06-17 20:25:18.278 +10:00 INF] Initializing database service...
[2025-06-17 20:25:18.281 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 20:25:18.379 +10:00 INF] Database connection established successfully
[2025-06-17 20:25:18.380 +10:00 INF] Database service initialized successfully
[2025-06-17 20:25:18.382 +10:00 INF] Checking for pending work...
[2025-06-17 20:25:18.385 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 20:25:19.301 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 20:25:19.304 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 20:25:19.316 +10:00 INF] Found 0 credits to invoice
[2025-06-17 20:25:19.318 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 20:25:19.323 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 20:25:19.325 +10:00 INF] No pending work found
[2025-06-17 20:25:19.326 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 20:25:19.327 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 20:25:19.416 +10:00 INF] LEP Invoicer completed successfully in 1141ms. No work to process.
[2025-06-17 20:25:19.422 +10:00 INF] Database connection disposed
[2025-06-17 20:25:19.424 +10:00 INF] LEP Invoicer completed with result: 0
