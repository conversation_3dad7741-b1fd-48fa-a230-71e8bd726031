[2025-06-17 16:07:34.883 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 16:07:34.911 +10:00 INF] Initializing FastReport...
[2025-06-17 16:07:35.002 +10:00 INF] FastReport initialized successfully
[2025-06-17 16:07:35.488 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 16:07:36.908 +10:00 INF] Starting LEP Invoicer at "2025-06-17T16:07:36.9082065+10:00"
[2025-06-17 16:07:36.912 +10:00 INF] Initializing database service...
[2025-06-17 16:07:36.915 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 16:07:37.021 +10:00 INF] Database connection established successfully
[2025-06-17 16:07:37.022 +10:00 INF] Database service initialized successfully
[2025-06-17 16:07:37.025 +10:00 INF] Checking for pending work...
[2025-06-17 16:07:37.028 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 16:07:38.679 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 16:07:38.683 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 16:07:38.794 +10:00 INF] Found 0 credits to invoice
[2025-06-17 16:07:38.832 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 16:07:38.883 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 16:07:38.885 +10:00 INF] No pending work found
[2025-06-17 16:07:38.890 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 16:07:38.907 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 16:07:39.131 +10:00 INF] LEP Invoicer completed successfully in 2223ms. No work to process.
[2025-06-17 16:07:39.163 +10:00 INF] Database connection disposed
[2025-06-17 16:07:39.177 +10:00 INF] LEP Invoicer completed with result: 0
