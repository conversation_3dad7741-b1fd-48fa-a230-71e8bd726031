[2025-06-17 16:41:35.633 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 16:41:35.667 +10:00 INF] Initializing FastReport...
[2025-06-17 16:41:35.754 +10:00 INF] FastReport initialized successfully
[2025-06-17 16:41:36.273 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 16:41:37.633 +10:00 INF] Starting LEP Invoicer at "2025-06-17T16:41:37.6332060+10:00"
[2025-06-17 16:41:37.637 +10:00 INF] Initializing database service...
[2025-06-17 16:41:37.641 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 16:41:37.793 +10:00 INF] Database connection established successfully
[2025-06-17 16:41:37.794 +10:00 INF] Database service initialized successfully
[2025-06-17 16:41:37.797 +10:00 INF] Checking for pending work...
[2025-06-17 16:41:37.800 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 16:41:38.724 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 16:41:38.726 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 16:41:38.738 +10:00 INF] Found 0 credits to invoice
[2025-06-17 16:41:38.740 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 16:41:38.744 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 16:41:38.746 +10:00 INF] No pending work found
[2025-06-17 16:41:38.747 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 16:41:38.749 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 16:41:38.836 +10:00 INF] LEP Invoicer completed successfully in 1203ms. No work to process.
[2025-06-17 16:41:38.843 +10:00 INF] Database connection disposed
[2025-06-17 16:41:38.845 +10:00 INF] LEP Invoicer completed with result: 0
