[2025-06-17 19:48:08.606 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 19:48:08.651 +10:00 INF] Initializing FastReport...
[2025-06-17 19:48:08.755 +10:00 INF] FastReport initialized successfully
[2025-06-17 19:48:09.132 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 19:48:10.372 +10:00 INF] Starting LEP Invoicer at "2025-06-17T19:48:10.3720359+10:00"
[2025-06-17 19:48:10.375 +10:00 INF] Initializing database service...
[2025-06-17 19:48:10.378 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 19:48:10.496 +10:00 INF] Database connection established successfully
[2025-06-17 19:48:10.498 +10:00 INF] Database service initialized successfully
[2025-06-17 19:48:10.501 +10:00 INF] Checking for pending work...
[2025-06-17 19:48:10.504 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 19:48:11.449 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 19:48:11.456 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 19:48:11.470 +10:00 INF] Found 0 credits to invoice
[2025-06-17 19:48:11.472 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 19:48:11.476 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 19:48:11.489 +10:00 INF] No pending work found
[2025-06-17 19:48:11.492 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 19:48:11.493 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 19:48:11.582 +10:00 INF] LEP Invoicer completed successfully in 1210ms. No work to process.
[2025-06-17 19:48:11.588 +10:00 INF] Database connection disposed
[2025-06-17 19:48:11.590 +10:00 INF] LEP Invoicer completed with result: 0
