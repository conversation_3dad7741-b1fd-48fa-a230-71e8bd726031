#region using

using Newtonsoft.Json;
using System;
using System.Security.Principal;

#endregion using

namespace lep.user.impl
{
	[Serializable]
    public class BaseUser : IUser, IIdentity, IPrincipal
    {
        //private DateTime dateCreated;
        //private DateTime dateModified;
        //private bool isEnabled = false;
        //private bool isStaff = false;
        //private DateTime lastLogin;
        private string password;

        protected BaseUser()
        {
        }

        public void SetPassword(string password)
        {
            this.password = password;
        }

        #region IUser Members

        public virtual int Id { get; set; }

        public virtual string Username { get; set; }

        public virtual string Password
        {
            get { return password; }
        }

        [JsonIgnore]
        public virtual string HashPassword { get; set; }

        public virtual bool IsEnabled { get; set; }

        public virtual string Email { get; set; } = String.Empty;
        public virtual bool IsStaff { get; set; }
        public virtual string FirstName { get; set; } = String.Empty;
        public virtual string LastName { get; set; } = String.Empty;
        public virtual string AreaCode { get; set; } = String.Empty;
        public virtual string Phone { get; set; } = String.Empty;
        public virtual string Mobile { get; set; } = String.Empty;

        public virtual DateTime DateCreated { get; set; }
        public virtual DateTime DateModified { get; set; }
        public virtual DateTime? LastLogin { get; set; }

        #endregion IUser Members

        #region IIdentity/IPrincipal magic

        IIdentity IPrincipal.Identity
        {
            get { return this; }
        }

        bool IPrincipal.IsInRole(string role)
        {
            return false;
        }

        string IIdentity.AuthenticationType
        {
            get { return "application"; }
        }

        string IIdentity.Name
        {
            get { return Username; }
        }

        bool IIdentity.IsAuthenticated
        {
            get { return true; }
        }

        #endregion IIdentity/IPrincipal magic
    }
}