[2025-06-17 15:36:54.444 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 15:36:54.473 +10:00 INF] Initializing FastReport...
[2025-06-17 15:36:54.545 +10:00 INF] FastReport initialized successfully
[2025-06-17 15:36:55.007 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 15:36:56.395 +10:00 INF] Starting LEP Invoicer at "2025-06-17T15:36:56.3952817+10:00"
[2025-06-17 15:36:56.399 +10:00 INF] Initializing database service...
[2025-06-17 15:36:56.402 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 15:36:56.504 +10:00 INF] Database connection established successfully
[2025-06-17 15:36:56.505 +10:00 INF] Database service initialized successfully
[2025-06-17 15:36:56.508 +10:00 INF] Checking for pending work...
[2025-06-17 15:36:56.511 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 15:36:57.405 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 15:36:57.408 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 15:36:57.420 +10:00 INF] Found 0 credits to invoice
[2025-06-17 15:36:57.422 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 15:36:57.428 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 15:36:57.429 +10:00 INF] No pending work found
[2025-06-17 15:36:57.431 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 15:36:57.432 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 15:36:57.506 +10:00 INF] LEP Invoicer completed successfully in 1111ms. No work to process.
[2025-06-17 15:36:57.514 +10:00 INF] Database connection disposed
[2025-06-17 15:36:57.516 +10:00 INF] LEP Invoicer completed with result: 0
