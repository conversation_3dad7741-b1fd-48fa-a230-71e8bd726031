[2025-06-17 17:23:00.508 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 17:23:00.571 +10:00 INF] Initializing FastReport...
[2025-06-17 17:23:00.649 +10:00 INF] FastReport initialized successfully
[2025-06-17 17:23:01.075 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 17:23:02.648 +10:00 INF] Starting LEP Invoicer at "2025-06-17T17:23:02.6480374+10:00"
[2025-06-17 17:23:02.655 +10:00 INF] Initializing database service...
[2025-06-17 17:23:02.659 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 17:23:02.774 +10:00 INF] Database connection established successfully
[2025-06-17 17:23:02.776 +10:00 INF] Database service initialized successfully
[2025-06-17 17:23:02.779 +10:00 INF] Checking for pending work...
[2025-06-17 17:23:02.782 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 17:23:03.692 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 17:23:03.696 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 17:23:03.709 +10:00 INF] Found 0 credits to invoice
[2025-06-17 17:23:03.711 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 17:23:03.716 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 17:23:03.718 +10:00 INF] No pending work found
[2025-06-17 17:23:03.719 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 17:23:03.721 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 17:23:03.803 +10:00 INF] LEP Invoicer completed successfully in 1155ms. No work to process.
[2025-06-17 17:23:03.812 +10:00 INF] Database connection disposed
[2025-06-17 17:23:03.816 +10:00 INF] LEP Invoicer completed with result: 0
