[2025-06-17 19:09:56.560 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 19:09:56.587 +10:00 INF] Initializing FastReport...
[2025-06-17 19:09:56.660 +10:00 INF] FastReport initialized successfully
[2025-06-17 19:09:57.088 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 19:09:58.385 +10:00 INF] Starting LEP Invoicer at "2025-06-17T19:09:58.3855792+10:00"
[2025-06-17 19:09:58.390 +10:00 INF] Initializing database service...
[2025-06-17 19:09:58.409 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 19:09:58.517 +10:00 INF] Database connection established successfully
[2025-06-17 19:09:58.518 +10:00 INF] Database service initialized successfully
[2025-06-17 19:09:58.521 +10:00 INF] Checking for pending work...
[2025-06-17 19:09:58.528 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 19:09:59.411 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 19:09:59.413 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 19:09:59.430 +10:00 INF] Found 0 credits to invoice
[2025-06-17 19:09:59.434 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 19:09:59.437 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 19:09:59.439 +10:00 INF] No pending work found
[2025-06-17 19:09:59.440 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 19:09:59.443 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 19:09:59.537 +10:00 INF] LEP Invoicer completed successfully in 1151ms. No work to process.
[2025-06-17 19:09:59.543 +10:00 INF] Database connection disposed
[2025-06-17 19:09:59.545 +10:00 INF] LEP Invoicer completed with result: 0
