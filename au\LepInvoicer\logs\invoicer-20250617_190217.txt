[2025-06-17 19:02:17.671 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 19:02:17.699 +10:00 INF] Initializing FastReport...
[2025-06-17 19:02:17.787 +10:00 INF] FastReport initialized successfully
[2025-06-17 19:02:18.250 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 19:02:19.581 +10:00 INF] Starting LEP Invoicer at "2025-06-17T19:02:19.5813648+10:00"
[2025-06-17 19:02:19.585 +10:00 INF] Initializing database service...
[2025-06-17 19:02:19.587 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 19:02:19.695 +10:00 INF] Database connection established successfully
[2025-06-17 19:02:19.696 +10:00 INF] Database service initialized successfully
[2025-06-17 19:02:19.698 +10:00 INF] Checking for pending work...
[2025-06-17 19:02:19.736 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 19:02:20.614 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 19:02:20.617 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 19:02:20.632 +10:00 INF] Found 0 credits to invoice
[2025-06-17 19:02:20.634 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 19:02:20.639 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 19:02:20.641 +10:00 INF] No pending work found
[2025-06-17 19:02:20.642 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 19:02:20.644 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 19:02:20.729 +10:00 INF] LEP Invoicer completed successfully in 1148ms. No work to process.
[2025-06-17 19:02:20.735 +10:00 INF] Database connection disposed
[2025-06-17 19:02:20.737 +10:00 INF] LEP Invoicer completed with result: 0
