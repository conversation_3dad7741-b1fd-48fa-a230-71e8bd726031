[2025-06-17 16:15:19.720 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 16:15:19.755 +10:00 INF] Initializing FastReport...
[2025-06-17 16:15:19.828 +10:00 INF] FastReport initialized successfully
[2025-06-17 16:15:20.223 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 16:15:21.561 +10:00 INF] Starting LEP Invoicer at "2025-06-17T16:15:21.5608834+10:00"
[2025-06-17 16:15:21.564 +10:00 INF] Initializing database service...
[2025-06-17 16:15:21.567 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 16:15:21.670 +10:00 INF] Database connection established successfully
[2025-06-17 16:15:21.672 +10:00 INF] Database service initialized successfully
[2025-06-17 16:15:21.674 +10:00 INF] Checking for pending work...
[2025-06-17 16:15:21.677 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 16:15:22.617 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 16:15:22.623 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 16:15:22.636 +10:00 INF] Found 0 credits to invoice
[2025-06-17 16:15:22.638 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 16:15:22.643 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 16:15:22.644 +10:00 INF] No pending work found
[2025-06-17 16:15:22.645 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 16:15:22.647 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 16:15:22.722 +10:00 INF] LEP Invoicer completed successfully in 1161ms. No work to process.
[2025-06-17 16:15:22.729 +10:00 INF] Database connection disposed
[2025-06-17 16:15:22.735 +10:00 INF] LEP Invoicer completed with result: 0
