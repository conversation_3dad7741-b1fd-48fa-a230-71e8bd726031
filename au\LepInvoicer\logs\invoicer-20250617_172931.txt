[2025-06-17 17:29:31.591 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 17:29:31.617 +10:00 INF] Initializing FastReport...
[2025-06-17 17:29:31.700 +10:00 INF] FastReport initialized successfully
[2025-06-17 17:29:32.107 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 17:29:33.308 +10:00 INF] Starting LEP Invoicer at "2025-06-17T17:29:33.3077872+10:00"
[2025-06-17 17:29:33.312 +10:00 INF] Initializing database service...
[2025-06-17 17:29:33.314 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 17:29:33.420 +10:00 INF] Database connection established successfully
[2025-06-17 17:29:33.421 +10:00 INF] Database service initialized successfully
[2025-06-17 17:29:33.424 +10:00 INF] Checking for pending work...
[2025-06-17 17:29:33.427 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 17:29:34.320 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 17:29:34.322 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 17:29:34.334 +10:00 INF] Found 0 credits to invoice
[2025-06-17 17:29:34.336 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 17:29:34.339 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 17:29:34.342 +10:00 INF] No pending work found
[2025-06-17 17:29:34.343 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 17:29:34.344 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 17:29:34.421 +10:00 INF] LEP Invoicer completed successfully in 1113ms. No work to process.
[2025-06-17 17:29:34.427 +10:00 INF] Database connection disposed
[2025-06-17 17:29:34.440 +10:00 INF] LEP Invoicer completed with result: 0
