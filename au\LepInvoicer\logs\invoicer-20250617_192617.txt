[2025-06-17 19:26:17.693 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 19:26:17.728 +10:00 INF] Initializing FastReport...
[2025-06-17 19:26:17.800 +10:00 INF] FastReport initialized successfully
[2025-06-17 19:26:18.194 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 19:26:19.656 +10:00 INF] Starting LEP Invoicer at "2025-06-17T19:26:19.6561890+10:00"
[2025-06-17 19:26:19.659 +10:00 INF] Initializing database service...
[2025-06-17 19:26:19.662 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 19:26:19.767 +10:00 INF] Database connection established successfully
[2025-06-17 19:26:19.768 +10:00 INF] Database service initialized successfully
[2025-06-17 19:26:19.772 +10:00 INF] Checking for pending work...
[2025-06-17 19:26:19.775 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 19:26:20.662 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 19:26:20.665 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 19:26:20.679 +10:00 INF] Found 0 credits to invoice
[2025-06-17 19:26:20.681 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 19:26:20.687 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 19:26:20.688 +10:00 INF] No pending work found
[2025-06-17 19:26:20.689 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 19:26:20.691 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 19:26:20.772 +10:00 INF] LEP Invoicer completed successfully in 1115ms. No work to process.
[2025-06-17 19:26:20.778 +10:00 INF] Database connection disposed
[2025-06-17 19:26:20.782 +10:00 INF] LEP Invoicer completed with result: 0
