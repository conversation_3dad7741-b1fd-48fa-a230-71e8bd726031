[2025-06-10 19:51:22.986 +10:00 INF] Starting LEP Invoicer application
[2025-06-10 19:51:23.737 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-10 19:51:25.870 +10:00 INF] Starting LEP Invoicer at "2025-06-10T19:51:25.8703779+10:00"
[2025-06-10 19:51:25.877 +10:00 INF] Initializing database service...
[2025-06-10 19:51:25.884 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-10 19:51:26.054 +10:00 INF] Database connection established successfully
[2025-06-10 19:51:26.056 +10:00 INF] Database service initialized successfully
[2025-06-10 19:51:26.060 +10:00 INF] Checking for pending work...
[2025-06-10 19:51:26.064 +10:00 INF] Getting 500 orders to invoice
[2025-06-10 19:51:28.032 +10:00 INF] Found 2 orders to invoice
[2025-06-10 19:51:28.040 +10:00 INF] Getting 100 credits to invoice
[2025-06-10 19:51:28.092 +10:00 INF] Found 0 credits to invoice
[2025-06-10 19:51:28.098 +10:00 INF] Getting 100 refunds to invoice
[2025-06-10 19:51:28.115 +10:00 INF] Found 0 refunds to invoice
[2025-06-10 19:51:28.118 +10:00 INF] Found pending work: 2 orders
[2025-06-10 19:51:28.122 +10:00 INF] Initializing MYOB and other services...
[2025-06-10 19:51:28.127 +10:00 INF] Initializing MYOB service...
[2025-06-10 19:51:28.129 +10:00 INF] Initializing MYOB with OAuth authentication
[2025-06-10 19:51:28.135 +10:00 INF] Starting OAuth authentication flow
[2025-06-10 19:51:28.249 +10:00 INF] Using existing OAuth tokens
[2025-06-10 19:51:28.254 +10:00 INF] Initializing MYOB services with OAuth keystore
[2025-06-10 19:51:28.260 +10:00 INF] MYOB services initialized successfully
[2025-06-10 19:51:28.264 +10:00 INF] OAuth keystore set for API calls
[2025-06-10 19:51:28.280 +10:00 INF] Getting company files from MYOB
[2025-06-10 19:51:29.054 +10:00 INF] MYOB service initialized with OAuth credentials. Using company file: LEP Colour Printers Pty Ltd
[2025-06-10 19:51:29.070 +10:00 INF] Current cache stats: {"AccountsCount":269,"TaxCodesCount":18,"CustomersCount":393,"LastCacheLoad":"2025-06-10T19:31:07.5727667+10:00","CacheAge":"00:20:21.4917007","IsExpired":false,"CacheDirectory":"C:\\Users\\<USER>\\AppData\\Local\\LepInvoicer\\cache"}
[2025-06-10 19:51:29.126 +10:00 INF] Using cached MYOB data: {"AccountsCount":269,"TaxCodesCount":18,"CustomersCount":393,"LastCacheLoad":"2025-06-10T19:31:07.5727667+10:00","CacheAge":"00:20:21.4917007","IsExpired":false,"CacheDirectory":"C:\\Users\\<USER>\\AppData\\Local\\LepInvoicer\\cache"}
[2025-06-10 19:51:29.965 +10:00 INF] Using cached GST tax code
[2025-06-10 19:51:29.969 +10:00 INF] Using cached freight account
[2025-06-10 19:51:29.971 +10:00 INF] Using cached discounts account
[2025-06-10 19:51:29.973 +10:00 INF] MYOB cache initialization complete: {"AccountsCount":269,"TaxCodesCount":18,"CustomersCount":393,"LastCacheLoad":"2025-06-10T19:31:07.5727667+10:00","CacheAge":"00:20:22.4006766","IsExpired":false,"CacheDirectory":"C:\\Users\\<USER>\\AppData\\Local\\LepInvoicer\\cache"}
[2025-06-10 19:51:29.993 +10:00 INF] MYOB service initialized successfully with company file: LEP Colour Printers Pty Ltd
[2025-06-10 19:51:29.997 +10:00 INF] All services initialized successfully
[2025-06-10 19:51:30.002 +10:00 INF] Processing order invoices...
[2025-06-10 19:51:30.023 +10:00 INF] Getting 500 orders to invoice
[2025-06-10 19:51:30.382 +10:00 INF] Found 2 orders to invoice
[2025-06-10 19:51:30.385 +10:00 INF] Found 2 orders to process
[2025-06-10 19:51:30.389 +10:00 INF] Getting order 1417006
[2025-06-10 19:51:30.720 +10:00 INF] Processing order 1417006 with 1 jobs, total: ¤0.00
[2025-06-10 19:51:30.728 +10:00 WRN] Order 1417006 has zero price, marking as invoiced
[2025-06-10 19:51:30.731 +10:00 INF] Getting order 1416838
[2025-06-10 19:51:30.743 +10:00 INF] Processing order 1416838 with 1 jobs, total: ¤0.00
[2025-06-10 19:51:30.746 +10:00 WRN] Order 1416838 has zero price, marking as invoiced
[2025-06-10 19:51:30.748 +10:00 INF] Order processing completed. Processed: 2, Success: 0, Failed: 2
[2025-06-10 19:51:30.752 +10:00 INF] Processing credit invoices...
[2025-06-10 19:51:30.766 +10:00 INF] Getting 100 credits to invoice
[2025-06-10 19:51:30.777 +10:00 INF] Found 0 credits to invoice
[2025-06-10 19:51:30.786 +10:00 INF] Processing refund invoices...
[2025-06-10 19:51:30.790 +10:00 INF] Getting 100 refunds to invoice
[2025-06-10 19:51:30.805 +10:00 INF] Found 0 refunds to invoice
[2025-06-10 19:51:30.808 +10:00 INF] Cleaning up invoicer logs...
[2025-06-10 19:51:30.884 +10:00 INF] LEP Invoicer completed successfully in 5013ms. Orders: 2, Credits: 0, Refunds: 0
[2025-06-10 19:51:30.898 +10:00 INF] Database connection disposed
[2025-06-10 19:51:30.901 +10:00 INF] LEP Invoicer completed with result: 0
