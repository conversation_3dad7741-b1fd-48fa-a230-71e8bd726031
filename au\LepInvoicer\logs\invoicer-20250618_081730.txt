[2025-06-18 08:17:30.505 +10:00 INF] Starting LEP Invoicer application
[2025-06-18 08:17:30.532 +10:00 INF] Initializing FastReport...
[2025-06-18 08:17:30.604 +10:00 INF] FastReport initialized successfully
[2025-06-18 08:17:31.047 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-18 08:17:32.383 +10:00 INF] Starting LEP Invoicer at "2025-06-18T08:17:32.3828342+10:00"
[2025-06-18 08:17:32.386 +10:00 INF] Initializing database service...
[2025-06-18 08:17:32.389 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-18 08:17:32.498 +10:00 INF] Database connection established successfully
[2025-06-18 08:17:32.499 +10:00 INF] Database service initialized successfully
[2025-06-18 08:17:32.502 +10:00 INF] Checking for pending work...
[2025-06-18 08:17:32.505 +10:00 INF] Getting 500 orders to invoice
[2025-06-18 08:17:33.396 +10:00 INF] Found 0 orders to invoice (filtered 16 candidates)
[2025-06-18 08:17:33.399 +10:00 INF] Getting 100 credits to invoice
[2025-06-18 08:17:33.411 +10:00 INF] Found 0 credits to invoice
[2025-06-18 08:17:33.413 +10:00 INF] Getting 100 refunds to invoice
[2025-06-18 08:17:33.418 +10:00 INF] Found 0 refunds to invoice
[2025-06-18 08:17:33.420 +10:00 INF] No pending work found
[2025-06-18 08:17:33.432 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-18 08:17:33.437 +10:00 INF] Cleaning up invoicer logs...
[2025-06-18 08:17:33.521 +10:00 INF] LEP Invoicer completed successfully in 1138ms. No work to process.
[2025-06-18 08:17:33.528 +10:00 INF] Database connection disposed
[2025-06-18 08:17:33.530 +10:00 INF] LEP Invoicer completed with result: 0
