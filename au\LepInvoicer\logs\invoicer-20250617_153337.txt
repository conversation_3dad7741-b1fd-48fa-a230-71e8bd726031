[2025-06-17 15:33:37.635 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 15:33:37.692 +10:00 INF] Initializing FastReport...
[2025-06-17 15:33:37.765 +10:00 INF] FastReport initialized successfully
[2025-06-17 15:33:38.227 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 15:33:39.583 +10:00 INF] Starting LEP Invoicer at "2025-06-17T15:33:39.5830672+10:00"
[2025-06-17 15:33:39.593 +10:00 INF] Initializing database service...
[2025-06-17 15:33:39.600 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 15:33:39.702 +10:00 INF] Database connection established successfully
[2025-06-17 15:33:39.704 +10:00 INF] Database service initialized successfully
[2025-06-17 15:33:39.708 +10:00 INF] Checking for pending work...
[2025-06-17 15:33:39.713 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 15:33:40.634 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 15:33:40.636 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 15:33:40.652 +10:00 INF] Found 0 credits to invoice
[2025-06-17 15:33:40.654 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 15:33:40.659 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 15:33:40.661 +10:00 INF] No pending work found
[2025-06-17 15:33:40.663 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 15:33:40.668 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 15:33:40.793 +10:00 INF] LEP Invoicer completed successfully in 1210ms. No work to process.
[2025-06-17 15:33:40.802 +10:00 INF] Database connection disposed
[2025-06-17 15:33:40.811 +10:00 INF] LEP Invoicer completed with result: 0
