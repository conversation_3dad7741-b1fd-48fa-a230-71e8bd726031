[2025-06-17 17:30:36.653 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 17:30:36.681 +10:00 INF] Initializing FastReport...
[2025-06-17 17:30:36.760 +10:00 INF] FastReport initialized successfully
[2025-06-17 17:30:37.196 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 17:30:38.417 +10:00 INF] Starting LEP Invoicer at "2025-06-17T17:30:38.4168192+10:00"
[2025-06-17 17:30:38.420 +10:00 INF] Initializing database service...
[2025-06-17 17:30:38.424 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 17:30:38.529 +10:00 INF] Database connection established successfully
[2025-06-17 17:30:38.531 +10:00 INF] Database service initialized successfully
[2025-06-17 17:30:38.533 +10:00 INF] Checking for pending work...
[2025-06-17 17:30:38.537 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 17:30:39.577 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 17:30:39.580 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 17:30:39.597 +10:00 INF] Found 0 credits to invoice
[2025-06-17 17:30:39.602 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 17:30:39.614 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 17:30:39.616 +10:00 INF] No pending work found
[2025-06-17 17:30:39.617 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 17:30:39.619 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 17:30:39.723 +10:00 INF] LEP Invoicer completed successfully in 1306ms. No work to process.
[2025-06-17 17:30:39.731 +10:00 INF] Database connection disposed
[2025-06-17 17:30:39.745 +10:00 INF] LEP Invoicer completed with result: 0
