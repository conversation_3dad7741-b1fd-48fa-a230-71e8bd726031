[2025-06-18 10:48:53.078 +10:00 INF] Starting LEP Invoicer application
[2025-06-18 10:48:53.118 +10:00 INF] Initializing FastReport...
[2025-06-18 10:48:53.245 +10:00 INF] FastReport initialized successfully
[2025-06-18 10:48:54.147 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-18 10:48:55.730 +10:00 INF] Starting LEP Invoicer at "2025-06-18T10:48:55.7305765+10:00"
[2025-06-18 10:48:55.741 +10:00 INF] Initializing database service...
[2025-06-18 10:48:55.744 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-18 10:48:55.898 +10:00 INF] Database connection established successfully
[2025-06-18 10:48:55.899 +10:00 INF] Database service initialized successfully
[2025-06-18 10:48:55.902 +10:00 INF] Checking for pending work...
[2025-06-18 10:48:55.906 +10:00 INF] Getting 500 orders to invoice
[2025-06-18 10:48:56.657 +10:00 INF] Found 0 orders to invoice (marked 0 zero-priced as invoiced, filtered 0 candidates)
[2025-06-18 10:48:56.659 +10:00 INF] Getting 100 credits to invoice
[2025-06-18 10:48:56.672 +10:00 INF] Found 0 credits to invoice
[2025-06-18 10:48:56.675 +10:00 INF] Getting 100 refunds to invoice
[2025-06-18 10:48:56.680 +10:00 INF] Found 0 refunds to invoice
[2025-06-18 10:48:56.681 +10:00 INF] No pending work found
[2025-06-18 10:48:56.682 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-18 10:48:56.683 +10:00 INF] Cleaning up invoicer logs...
[2025-06-18 10:48:56.768 +10:00 INF] LEP Invoicer completed successfully in 1037ms. No work to process.
[2025-06-18 10:48:56.776 +10:00 INF] Database connection disposed
[2025-06-18 10:48:56.778 +10:00 INF] LEP Invoicer completed with result: 0
