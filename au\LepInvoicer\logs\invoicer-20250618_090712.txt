[2025-06-18 09:07:12.565 +10:00 INF] Starting LEP Invoicer application
[2025-06-18 09:07:12.599 +10:00 INF] Initializing FastReport...
[2025-06-18 09:07:12.684 +10:00 INF] FastReport initialized successfully
[2025-06-18 09:07:13.170 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-18 09:07:14.455 +10:00 INF] Starting LEP Invoicer at "2025-06-18T09:07:14.4546703+10:00"
[2025-06-18 09:07:14.462 +10:00 INF] Initializing database service...
[2025-06-18 09:07:14.465 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-18 09:07:14.619 +10:00 INF] Database connection established successfully
[2025-06-18 09:07:14.621 +10:00 INF] Database service initialized successfully
[2025-06-18 09:07:14.624 +10:00 INF] Checking for pending work...
[2025-06-18 09:07:14.627 +10:00 INF] Getting 500 orders to invoice
[2025-06-18 09:07:15.506 +10:00 INF] Found 0 orders to invoice (filtered 16 candidates)
[2025-06-18 09:07:15.509 +10:00 INF] Getting 100 credits to invoice
[2025-06-18 09:07:15.540 +10:00 INF] Found 0 credits to invoice
[2025-06-18 09:07:15.543 +10:00 INF] Getting 100 refunds to invoice
[2025-06-18 09:07:15.549 +10:00 INF] Found 0 refunds to invoice
[2025-06-18 09:07:15.552 +10:00 INF] No pending work found
[2025-06-18 09:07:15.554 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-18 09:07:15.557 +10:00 INF] Cleaning up invoicer logs...
[2025-06-18 09:07:15.677 +10:00 INF] LEP Invoicer completed successfully in 1222ms. No work to process.
[2025-06-18 09:07:15.684 +10:00 INF] Database connection disposed
[2025-06-18 09:07:15.686 +10:00 INF] LEP Invoicer completed with result: 0
