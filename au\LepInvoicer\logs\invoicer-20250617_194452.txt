[2025-06-17 19:44:52.571 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 19:44:52.598 +10:00 INF] Initializing FastReport...
[2025-06-17 19:44:52.679 +10:00 INF] FastReport initialized successfully
[2025-06-17 19:44:53.188 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 19:44:54.463 +10:00 INF] Starting LEP Invoicer at "2025-06-17T19:44:54.4635369+10:00"
[2025-06-17 19:44:54.467 +10:00 INF] Initializing database service...
[2025-06-17 19:44:54.470 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 19:44:54.571 +10:00 INF] Database connection established successfully
[2025-06-17 19:44:54.572 +10:00 INF] Database service initialized successfully
[2025-06-17 19:44:54.575 +10:00 INF] Checking for pending work...
[2025-06-17 19:44:54.579 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 19:44:55.460 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 19:44:55.470 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 19:44:55.499 +10:00 INF] Found 0 credits to invoice
[2025-06-17 19:44:55.502 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 19:44:55.506 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 19:44:55.511 +10:00 INF] No pending work found
[2025-06-17 19:44:55.514 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 19:44:55.516 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 19:44:55.601 +10:00 INF] LEP Invoicer completed successfully in 1137ms. No work to process.
[2025-06-17 19:44:55.608 +10:00 INF] Database connection disposed
[2025-06-17 19:44:55.609 +10:00 INF] LEP Invoicer completed with result: 0
