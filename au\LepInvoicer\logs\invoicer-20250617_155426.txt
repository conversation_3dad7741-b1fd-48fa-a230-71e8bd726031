[2025-06-17 15:54:26.613 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 15:54:26.641 +10:00 INF] Initializing FastReport...
[2025-06-17 15:54:26.712 +10:00 INF] FastReport initialized successfully
[2025-06-17 15:54:27.140 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 15:54:28.470 +10:00 INF] Starting LEP Invoicer at "2025-06-17T15:54:28.4703436+10:00"
[2025-06-17 15:54:28.474 +10:00 INF] Initializing database service...
[2025-06-17 15:54:28.477 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 15:54:28.588 +10:00 INF] Database connection established successfully
[2025-06-17 15:54:28.589 +10:00 INF] Database service initialized successfully
[2025-06-17 15:54:28.597 +10:00 INF] Checking for pending work...
[2025-06-17 15:54:28.601 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 15:54:29.501 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 15:54:29.505 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 15:54:29.519 +10:00 INF] Found 0 credits to invoice
[2025-06-17 15:54:29.521 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 15:54:29.526 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 15:54:29.538 +10:00 INF] No pending work found
[2025-06-17 15:54:29.542 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 15:54:29.551 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 15:54:29.658 +10:00 INF] LEP Invoicer completed successfully in 1187ms. No work to process.
[2025-06-17 15:54:29.665 +10:00 INF] Database connection disposed
[2025-06-17 15:54:29.667 +10:00 INF] LEP Invoicer completed with result: 0
