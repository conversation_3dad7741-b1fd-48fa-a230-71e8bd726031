[2025-06-10 19:31:01.767 +10:00 INF] Starting LEP Invoicer application
[2025-06-10 19:31:02.636 +10:00 INF] Loaded MYOB cache: 0 accounts, 0 tax codes, 0 customers
[2025-06-10 19:31:05.023 +10:00 INF] Starting LEP Invoicer at "2025-06-10T19:31:05.0231347+10:00"
[2025-06-10 19:31:05.029 +10:00 INF] Initializing services...
[2025-06-10 19:31:05.034 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-10 19:31:05.217 +10:00 INF] Database connection established successfully
[2025-06-10 19:31:05.229 +10:00 INF] Initializing MYOB service...
[2025-06-10 19:31:05.233 +10:00 INF] Initializing MYOB with OAuth authentication
[2025-06-10 19:31:05.238 +10:00 INF] Starting OAuth authentication flow
[2025-06-10 19:31:05.362 +10:00 INF] Using existing OAuth tokens
[2025-06-10 19:31:05.370 +10:00 INF] Initializing MYOB services with OAuth keystore
[2025-06-10 19:31:05.381 +10:00 INF] MYOB services initialized successfully
[2025-06-10 19:31:05.386 +10:00 INF] OAuth keystore set for API calls
[2025-06-10 19:31:05.428 +10:00 INF] Getting company files from MYOB
[2025-06-10 19:31:06.191 +10:00 INF] MYOB service initialized with OAuth credentials. Using company file: LEP Colour Printers Pty Ltd
[2025-06-10 19:31:06.203 +10:00 INF] Current cache stats: {"AccountsCount":0,"TaxCodesCount":0,"CustomersCount":0,"LastCacheLoad":"2025-06-10T19:31:02.6186087+10:00","CacheAge":"00:00:03.5787591","IsExpired":true,"CacheDirectory":"C:\\Users\\<USER>\\AppData\\Local\\LepInvoicer\\cache"}
[2025-06-10 19:31:06.227 +10:00 INF] MYOB cache needs refresh (expired or empty), fetching from API...
[2025-06-10 19:31:07.573 +10:00 INF] Updated accounts cache with 269 accounts
[2025-06-10 19:31:07.578 +10:00 INF] Refreshed 279 accounts from MYOB API
[2025-06-10 19:31:08.374 +10:00 INF] Updated tax codes cache with 18 tax codes
[2025-06-10 19:31:08.379 +10:00 INF] Refreshed 18 tax codes from MYOB API
[2025-06-10 19:31:09.862 +10:00 INF] Updated customers cache with 393 customers
[2025-06-10 19:31:09.866 +10:00 INF] Refreshed 400 customers from MYOB API
[2025-06-10 19:31:09.870 +10:00 INF] Using cached GST tax code
[2025-06-10 19:31:09.873 +10:00 INF] Using cached freight account
[2025-06-10 19:31:09.877 +10:00 INF] Using cached discounts account
[2025-06-10 19:31:09.883 +10:00 INF] MYOB cache initialization complete: {"AccountsCount":269,"TaxCodesCount":18,"CustomersCount":393,"LastCacheLoad":"2025-06-10T19:31:09.8620995+10:00","CacheAge":"00:00:00.0208191","IsExpired":false,"CacheDirectory":"C:\\Users\\<USER>\\AppData\\Local\\LepInvoicer\\cache"}
[2025-06-10 19:31:09.907 +10:00 INF] MYOB service initialized successfully with company file: LEP Colour Printers Pty Ltd
[2025-06-10 19:31:09.922 +10:00 INF] Services initialized successfully
[2025-06-10 19:31:09.934 +10:00 INF] Processing order invoices...
[2025-06-10 19:31:09.939 +10:00 INF] Getting 500 orders to invoice
[2025-06-10 19:31:10.909 +10:00 INF] Found 2 orders to invoice
[2025-06-10 19:31:10.914 +10:00 INF] Found 2 orders to process
[2025-06-10 19:31:10.920 +10:00 INF] Getting order 1417006
[2025-06-10 19:31:11.263 +10:00 INF] Processing order 1417006 with 1 jobs, total: ¤0.00
[2025-06-10 19:31:11.273 +10:00 WRN] Order 1417006 has zero price, marking as invoiced
[2025-06-10 19:31:11.280 +10:00 INF] Getting order 1416838
[2025-06-10 19:31:11.291 +10:00 INF] Processing order 1416838 with 1 jobs, total: ¤0.00
[2025-06-10 19:31:11.298 +10:00 WRN] Order 1416838 has zero price, marking as invoiced
[2025-06-10 19:31:11.301 +10:00 INF] Order processing completed. Processed: 2, Success: 0, Failed: 2
[2025-06-10 19:31:11.311 +10:00 INF] Processing credit invoices...
[2025-06-10 19:31:11.316 +10:00 INF] Getting 100 credits to invoice
[2025-06-10 19:31:11.359 +10:00 INF] Found 0 credits to invoice
[2025-06-10 19:31:11.365 +10:00 INF] Processing refund invoices...
[2025-06-10 19:31:11.377 +10:00 INF] Getting 100 refunds to invoice
[2025-06-10 19:31:11.406 +10:00 INF] Found 0 refunds to invoice
[2025-06-10 19:31:11.412 +10:00 INF] Cleaning up invoicer logs...
[2025-06-10 19:31:11.484 +10:00 INF] LEP Invoicer completed successfully in 6461ms. Orders: 2, Credits: 0, Refunds: 0
[2025-06-10 19:31:11.520 +10:00 INF] Database connection disposed
[2025-06-10 19:31:11.523 +10:00 INF] LEP Invoicer completed with result: 0
