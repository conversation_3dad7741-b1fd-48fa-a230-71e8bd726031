[2025-06-18 09:30:59.631 +10:00 INF] Starting LEP Invoicer application
[2025-06-18 09:30:59.670 +10:00 INF] Initializing FastReport...
[2025-06-18 09:30:59.744 +10:00 INF] FastReport initialized successfully
[2025-06-18 09:31:00.291 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-18 09:31:02.851 +10:00 INF] Starting LEP Invoicer at "2025-06-18T09:31:02.8509006+10:00"
[2025-06-18 09:31:03.672 +10:00 INF] Initializing database service...
[2025-06-18 09:31:03.742 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-18 09:31:03.899 +10:00 INF] Database connection established successfully
[2025-06-18 09:31:03.901 +10:00 INF] Database service initialized successfully
[2025-06-18 09:31:03.914 +10:00 INF] Checking for pending work...
[2025-06-18 09:31:03.936 +10:00 INF] Getting 500 orders to invoice
[2025-06-18 09:31:04.917 +10:00 INF] Found 0 orders to invoice (filtered 16 candidates)
[2025-06-18 09:31:04.919 +10:00 INF] Getting 100 credits to invoice
[2025-06-18 09:31:04.932 +10:00 INF] Found 0 credits to invoice
[2025-06-18 09:31:04.933 +10:00 INF] Getting 100 refunds to invoice
[2025-06-18 09:31:04.936 +10:00 INF] Found 0 refunds to invoice
[2025-06-18 09:31:04.939 +10:00 INF] No pending work found
[2025-06-18 09:31:04.940 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-18 09:31:04.941 +10:00 INF] Cleaning up invoicer logs...
[2025-06-18 09:31:05.017 +10:00 INF] LEP Invoicer completed successfully in 2167ms. No work to process.
[2025-06-18 09:31:05.024 +10:00 INF] Database connection disposed
[2025-06-18 09:31:05.025 +10:00 INF] LEP Invoicer completed with result: 0
