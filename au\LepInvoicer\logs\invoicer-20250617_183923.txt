[2025-06-17 18:39:23.609 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 18:39:23.647 +10:00 INF] Initializing FastReport...
[2025-06-17 18:39:23.720 +10:00 INF] FastReport initialized successfully
[2025-06-17 18:39:24.109 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 18:39:25.391 +10:00 INF] Starting LEP Invoicer at "2025-06-17T18:39:25.3909412+10:00"
[2025-06-17 18:39:25.395 +10:00 INF] Initializing database service...
[2025-06-17 18:39:25.404 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 18:39:25.513 +10:00 INF] Database connection established successfully
[2025-06-17 18:39:25.514 +10:00 INF] Database service initialized successfully
[2025-06-17 18:39:25.517 +10:00 INF] Checking for pending work...
[2025-06-17 18:39:25.523 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 18:39:26.434 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 18:39:26.436 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 18:39:26.450 +10:00 INF] Found 0 credits to invoice
[2025-06-17 18:39:26.452 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 18:39:26.459 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 18:39:26.460 +10:00 INF] No pending work found
[2025-06-17 18:39:26.461 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 18:39:26.463 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 18:39:26.546 +10:00 INF] LEP Invoicer completed successfully in 1155ms. No work to process.
[2025-06-17 18:39:26.554 +10:00 INF] Database connection disposed
[2025-06-17 18:39:26.556 +10:00 INF] LEP Invoicer completed with result: 0
