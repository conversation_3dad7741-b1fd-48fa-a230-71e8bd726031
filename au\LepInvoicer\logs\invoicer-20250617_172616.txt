[2025-06-17 17:26:16.492 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 17:26:16.521 +10:00 INF] Initializing FastReport...
[2025-06-17 17:26:16.602 +10:00 INF] FastReport initialized successfully
[2025-06-17 17:26:16.992 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 17:26:18.381 +10:00 INF] Starting LEP Invoicer at "2025-06-17T17:26:18.3815627+10:00"
[2025-06-17 17:26:18.391 +10:00 INF] Initializing database service...
[2025-06-17 17:26:18.395 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 17:26:18.497 +10:00 INF] Database connection established successfully
[2025-06-17 17:26:18.498 +10:00 INF] Database service initialized successfully
[2025-06-17 17:26:18.502 +10:00 INF] Checking for pending work...
[2025-06-17 17:26:18.505 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 17:26:19.490 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 17:26:19.493 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 17:26:19.515 +10:00 INF] Found 0 credits to invoice
[2025-06-17 17:26:19.531 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 17:26:19.535 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 17:26:19.536 +10:00 INF] No pending work found
[2025-06-17 17:26:19.542 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 17:26:19.544 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 17:26:19.622 +10:00 INF] LEP Invoicer completed successfully in 1240ms. No work to process.
[2025-06-17 17:26:19.629 +10:00 INF] Database connection disposed
[2025-06-17 17:26:19.631 +10:00 INF] LEP Invoicer completed with result: 0
