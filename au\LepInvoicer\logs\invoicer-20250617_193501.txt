[2025-06-17 19:35:01.813 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 19:35:01.841 +10:00 INF] Initializing FastReport...
[2025-06-17 19:35:01.958 +10:00 INF] FastReport initialized successfully
[2025-06-17 19:35:02.524 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 19:35:03.814 +10:00 INF] Starting LEP Invoicer at "2025-06-17T19:35:03.8139131+10:00"
[2025-06-17 19:35:03.817 +10:00 INF] Initializing database service...
[2025-06-17 19:35:03.820 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 19:35:03.921 +10:00 INF] Database connection established successfully
[2025-06-17 19:35:03.922 +10:00 INF] Database service initialized successfully
[2025-06-17 19:35:03.925 +10:00 INF] Checking for pending work...
[2025-06-17 19:35:03.928 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 19:35:04.794 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 19:35:04.797 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 19:35:04.809 +10:00 INF] Found 0 credits to invoice
[2025-06-17 19:35:04.811 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 19:35:04.817 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 19:35:04.818 +10:00 INF] No pending work found
[2025-06-17 19:35:04.819 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 19:35:04.821 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 19:35:04.895 +10:00 INF] LEP Invoicer completed successfully in 1081ms. No work to process.
[2025-06-17 19:35:04.902 +10:00 INF] Database connection disposed
[2025-06-17 19:35:04.904 +10:00 INF] LEP Invoicer completed with result: 0
