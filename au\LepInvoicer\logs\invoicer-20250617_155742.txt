[2025-06-17 15:57:42.589 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 15:57:42.618 +10:00 INF] Initializing FastReport...
[2025-06-17 15:57:42.701 +10:00 INF] FastReport initialized successfully
[2025-06-17 15:57:43.127 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 15:57:44.403 +10:00 INF] Starting LEP Invoicer at "2025-06-17T15:57:44.4027190+10:00"
[2025-06-17 15:57:44.413 +10:00 INF] Initializing database service...
[2025-06-17 15:57:44.416 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 15:57:44.515 +10:00 INF] Database connection established successfully
[2025-06-17 15:57:44.516 +10:00 INF] Database service initialized successfully
[2025-06-17 15:57:44.519 +10:00 INF] Checking for pending work...
[2025-06-17 15:57:44.522 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 15:57:45.520 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 15:57:45.523 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 15:57:45.537 +10:00 INF] Found 0 credits to invoice
[2025-06-17 15:57:45.546 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 15:57:45.549 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 15:57:45.553 +10:00 INF] No pending work found
[2025-06-17 15:57:45.554 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 15:57:45.555 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 15:57:45.675 +10:00 INF] LEP Invoicer completed successfully in 1272ms. No work to process.
[2025-06-17 15:57:45.682 +10:00 INF] Database connection disposed
[2025-06-17 15:57:45.689 +10:00 INF] LEP Invoicer completed with result: 0
