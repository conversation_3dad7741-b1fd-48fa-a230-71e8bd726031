[2025-06-17 18:07:43.489 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 18:07:43.516 +10:00 INF] Initializing FastReport...
[2025-06-17 18:07:43.591 +10:00 INF] FastReport initialized successfully
[2025-06-17 18:07:44.005 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 18:07:45.283 +10:00 INF] Starting LEP Invoicer at "2025-06-17T18:07:45.2831351+10:00"
[2025-06-17 18:07:45.296 +10:00 INF] Initializing database service...
[2025-06-17 18:07:45.299 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 18:07:45.399 +10:00 INF] Database connection established successfully
[2025-06-17 18:07:45.409 +10:00 INF] Database service initialized successfully
[2025-06-17 18:07:45.412 +10:00 INF] Checking for pending work...
[2025-06-17 18:07:45.415 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 18:07:46.305 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 18:07:46.308 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 18:07:46.322 +10:00 INF] Found 0 credits to invoice
[2025-06-17 18:07:46.331 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 18:07:46.335 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 18:07:46.336 +10:00 INF] No pending work found
[2025-06-17 18:07:46.338 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 18:07:46.339 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 18:07:46.426 +10:00 INF] LEP Invoicer completed successfully in 1143ms. No work to process.
[2025-06-17 18:07:46.432 +10:00 INF] Database connection disposed
[2025-06-17 18:07:46.435 +10:00 INF] LEP Invoicer completed with result: 0
