[2025-06-18 09:47:21.654 +10:00 INF] Starting LEP Invoicer application
[2025-06-18 09:47:21.685 +10:00 INF] Initializing FastReport...
[2025-06-18 09:47:21.761 +10:00 INF] FastReport initialized successfully
[2025-06-18 09:47:22.246 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-18 09:47:23.664 +10:00 INF] Starting LEP Invoicer at "2025-06-18T09:47:23.6638523+10:00"
[2025-06-18 09:47:23.668 +10:00 INF] Initializing database service...
[2025-06-18 09:47:23.672 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-18 09:47:23.806 +10:00 INF] Database connection established successfully
[2025-06-18 09:47:23.807 +10:00 INF] Database service initialized successfully
[2025-06-18 09:47:23.810 +10:00 INF] Checking for pending work...
[2025-06-18 09:47:23.814 +10:00 INF] Getting 500 orders to invoice
[2025-06-18 09:47:24.933 +10:00 INF] Found 0 orders to invoice (filtered 16 candidates)
[2025-06-18 09:47:24.936 +10:00 INF] Getting 100 credits to invoice
[2025-06-18 09:47:24.952 +10:00 INF] Found 0 credits to invoice
[2025-06-18 09:47:24.954 +10:00 INF] Getting 100 refunds to invoice
[2025-06-18 09:47:24.960 +10:00 INF] Found 0 refunds to invoice
[2025-06-18 09:47:24.961 +10:00 INF] No pending work found
[2025-06-18 09:47:24.966 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-18 09:47:24.970 +10:00 INF] Cleaning up invoicer logs...
[2025-06-18 09:47:25.075 +10:00 INF] LEP Invoicer completed successfully in 1411ms. No work to process.
[2025-06-18 09:47:25.081 +10:00 INF] Database connection disposed
[2025-06-18 09:47:25.084 +10:00 INF] LEP Invoicer completed with result: 0
