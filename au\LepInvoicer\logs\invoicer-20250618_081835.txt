[2025-06-18 08:18:35.727 +10:00 INF] Starting LEP Invoicer application
[2025-06-18 08:18:35.768 +10:00 INF] Initializing FastReport...
[2025-06-18 08:18:35.860 +10:00 INF] FastReport initialized successfully
[2025-06-18 08:18:36.339 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-18 08:18:37.604 +10:00 INF] Starting LEP Invoicer at "2025-06-18T08:18:37.6039502+10:00"
[2025-06-18 08:18:37.607 +10:00 INF] Initializing database service...
[2025-06-18 08:18:37.613 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-18 08:18:37.717 +10:00 INF] Database connection established successfully
[2025-06-18 08:18:37.718 +10:00 INF] Database service initialized successfully
[2025-06-18 08:18:37.721 +10:00 INF] Checking for pending work...
[2025-06-18 08:18:37.724 +10:00 INF] Getting 500 orders to invoice
[2025-06-18 08:18:38.598 +10:00 INF] Found 0 orders to invoice (filtered 16 candidates)
[2025-06-18 08:18:38.601 +10:00 INF] Getting 100 credits to invoice
[2025-06-18 08:18:38.612 +10:00 INF] Found 0 credits to invoice
[2025-06-18 08:18:38.614 +10:00 INF] Getting 100 refunds to invoice
[2025-06-18 08:18:38.617 +10:00 INF] Found 0 refunds to invoice
[2025-06-18 08:18:38.620 +10:00 INF] No pending work found
[2025-06-18 08:18:38.621 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-18 08:18:38.622 +10:00 INF] Cleaning up invoicer logs...
[2025-06-18 08:18:38.694 +10:00 INF] LEP Invoicer completed successfully in 1090ms. No work to process.
[2025-06-18 08:18:38.700 +10:00 INF] Database connection disposed
[2025-06-18 08:18:38.702 +10:00 INF] LEP Invoicer completed with result: 0
