[2025-06-18 09:32:07.802 +10:00 INF] Starting LEP Invoicer application
[2025-06-18 09:32:07.863 +10:00 INF] Initializing FastReport...
[2025-06-18 09:32:07.946 +10:00 INF] FastReport initialized successfully
[2025-06-18 09:32:08.372 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-18 09:32:09.963 +10:00 INF] Starting LEP Invoicer at "2025-06-18T09:32:09.9625028+10:00"
[2025-06-18 09:32:09.972 +10:00 INF] Initializing database service...
[2025-06-18 09:32:09.978 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-18 09:32:10.134 +10:00 INF] Database connection established successfully
[2025-06-18 09:32:10.136 +10:00 INF] Database service initialized successfully
[2025-06-18 09:32:10.138 +10:00 INF] Checking for pending work...
[2025-06-18 09:32:10.142 +10:00 INF] Getting 500 orders to invoice
[2025-06-18 09:32:11.073 +10:00 INF] Found 0 orders to invoice (filtered 16 candidates)
[2025-06-18 09:32:11.076 +10:00 INF] Getting 100 credits to invoice
[2025-06-18 09:32:11.089 +10:00 INF] Found 0 credits to invoice
[2025-06-18 09:32:11.091 +10:00 INF] Getting 100 refunds to invoice
[2025-06-18 09:32:11.097 +10:00 INF] Found 0 refunds to invoice
[2025-06-18 09:32:11.101 +10:00 INF] No pending work found
[2025-06-18 09:32:11.103 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-18 09:32:11.104 +10:00 INF] Cleaning up invoicer logs...
[2025-06-18 09:32:11.192 +10:00 INF] LEP Invoicer completed successfully in 1229ms. No work to process.
[2025-06-18 09:32:11.199 +10:00 INF] Database connection disposed
[2025-06-18 09:32:11.201 +10:00 INF] LEP Invoicer completed with result: 0
