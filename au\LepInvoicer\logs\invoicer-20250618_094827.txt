[2025-06-18 09:48:28.030 +10:00 INF] Starting LEP Invoicer application
[2025-06-18 09:48:28.059 +10:00 INF] Initializing FastReport...
[2025-06-18 09:48:28.131 +10:00 INF] FastReport initialized successfully
[2025-06-18 09:48:28.519 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-18 09:48:29.832 +10:00 INF] Starting LEP Invoicer at "2025-06-18T09:48:29.8322647+10:00"
[2025-06-18 09:48:29.836 +10:00 INF] Initializing database service...
[2025-06-18 09:48:29.839 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-18 09:48:29.943 +10:00 INF] Database connection established successfully
[2025-06-18 09:48:29.944 +10:00 INF] Database service initialized successfully
[2025-06-18 09:48:29.946 +10:00 INF] Checking for pending work...
[2025-06-18 09:48:29.949 +10:00 INF] Getting 500 orders to invoice
[2025-06-18 09:48:30.838 +10:00 INF] Found 0 orders to invoice (filtered 16 candidates)
[2025-06-18 09:48:30.841 +10:00 INF] Getting 100 credits to invoice
[2025-06-18 09:48:30.854 +10:00 INF] Found 0 credits to invoice
[2025-06-18 09:48:30.858 +10:00 INF] Getting 100 refunds to invoice
[2025-06-18 09:48:30.863 +10:00 INF] Found 0 refunds to invoice
[2025-06-18 09:48:30.864 +10:00 INF] No pending work found
[2025-06-18 09:48:30.865 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-18 09:48:30.866 +10:00 INF] Cleaning up invoicer logs...
[2025-06-18 09:48:30.943 +10:00 INF] LEP Invoicer completed successfully in 1110ms. No work to process.
[2025-06-18 09:48:30.949 +10:00 INF] Database connection disposed
[2025-06-18 09:48:30.951 +10:00 INF] LEP Invoicer completed with result: 0
