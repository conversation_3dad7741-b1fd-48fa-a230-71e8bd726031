[2025-06-17 19:38:19.542 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 19:38:19.570 +10:00 INF] Initializing FastReport...
[2025-06-17 19:38:19.644 +10:00 INF] FastReport initialized successfully
[2025-06-17 19:38:20.080 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 19:38:21.431 +10:00 INF] Starting LEP Invoicer at "2025-06-17T19:38:21.4311010+10:00"
[2025-06-17 19:38:21.436 +10:00 INF] Initializing database service...
[2025-06-17 19:38:21.438 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 19:38:21.575 +10:00 INF] Database connection established successfully
[2025-06-17 19:38:21.579 +10:00 INF] Database service initialized successfully
[2025-06-17 19:38:21.591 +10:00 INF] Checking for pending work...
[2025-06-17 19:38:21.594 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 19:38:22.562 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 19:38:22.568 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 19:38:22.597 +10:00 INF] Found 0 credits to invoice
[2025-06-17 19:38:22.599 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 19:38:22.604 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 19:38:22.610 +10:00 INF] No pending work found
[2025-06-17 19:38:22.612 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 19:38:22.614 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 19:38:22.712 +10:00 INF] LEP Invoicer completed successfully in 1281ms. No work to process.
[2025-06-17 19:38:22.720 +10:00 INF] Database connection disposed
[2025-06-17 19:38:22.732 +10:00 INF] LEP Invoicer completed with result: 0
