[2025-06-17 15:10:50.598 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 15:10:50.639 +10:00 INF] Initializing FastReport...
[2025-06-17 15:10:50.717 +10:00 INF] FastReport initialized successfully
[2025-06-17 15:10:51.201 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 15:10:52.875 +10:00 INF] Starting LEP Invoicer at "2025-06-17T15:10:52.8751108+10:00"
[2025-06-17 15:10:52.879 +10:00 INF] Initializing database service...
[2025-06-17 15:10:52.882 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 15:10:53.006 +10:00 INF] Database connection established successfully
[2025-06-17 15:10:53.008 +10:00 INF] Database service initialized successfully
[2025-06-17 15:10:53.014 +10:00 INF] Checking for pending work...
[2025-06-17 15:10:53.020 +10:00 INF] Getting 500 orders to invoice
