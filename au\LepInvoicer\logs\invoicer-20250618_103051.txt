[2025-06-18 10:30:51.079 +10:00 INF] Starting LEP Invoicer application
[2025-06-18 10:30:51.110 +10:00 INF] Initializing FastReport...
[2025-06-18 10:30:51.198 +10:00 INF] FastReport initialized successfully
[2025-06-18 10:30:51.757 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-18 10:30:53.359 +10:00 INF] Starting LEP Invoicer at "2025-06-18T10:30:53.3588348+10:00"
[2025-06-18 10:30:53.363 +10:00 INF] Initializing database service...
[2025-06-18 10:30:53.366 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-18 10:30:53.472 +10:00 INF] Database connection established successfully
[2025-06-18 10:30:53.474 +10:00 INF] Database service initialized successfully
[2025-06-18 10:30:53.477 +10:00 INF] Checking for pending work...
[2025-06-18 10:30:53.479 +10:00 INF] Getting 500 orders to invoice
[2025-06-18 10:30:54.392 +10:00 INF] Found 0 orders to invoice (filtered 17 candidates)
[2025-06-18 10:30:54.394 +10:00 INF] Getting 100 credits to invoice
[2025-06-18 10:30:54.406 +10:00 INF] Found 0 credits to invoice
[2025-06-18 10:30:54.408 +10:00 INF] Getting 100 refunds to invoice
[2025-06-18 10:30:54.412 +10:00 INF] Found 0 refunds to invoice
[2025-06-18 10:30:54.414 +10:00 INF] No pending work found
[2025-06-18 10:30:54.415 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-18 10:30:54.416 +10:00 INF] Cleaning up invoicer logs...
[2025-06-18 10:30:54.498 +10:00 INF] LEP Invoicer completed successfully in 1139ms. No work to process.
[2025-06-18 10:30:54.505 +10:00 INF] Database connection disposed
[2025-06-18 10:30:54.507 +10:00 INF] LEP Invoicer completed with result: 0
