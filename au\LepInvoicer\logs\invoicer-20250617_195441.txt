[2025-06-17 19:54:41.624 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 19:54:41.660 +10:00 INF] Initializing FastReport...
[2025-06-17 19:54:41.733 +10:00 INF] FastReport initialized successfully
[2025-06-17 19:54:42.178 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 19:54:43.436 +10:00 INF] Starting LEP Invoicer at "2025-06-17T19:54:43.4363041+10:00"
[2025-06-17 19:54:43.439 +10:00 INF] Initializing database service...
[2025-06-17 19:54:43.442 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 19:54:43.545 +10:00 INF] Database connection established successfully
[2025-06-17 19:54:43.546 +10:00 INF] Database service initialized successfully
[2025-06-17 19:54:43.549 +10:00 INF] Checking for pending work...
[2025-06-17 19:54:43.551 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 19:54:44.507 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 19:54:44.510 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 19:54:44.522 +10:00 INF] Found 0 credits to invoice
[2025-06-17 19:54:44.524 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 19:54:44.527 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 19:54:44.529 +10:00 INF] No pending work found
[2025-06-17 19:54:44.533 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 19:54:44.534 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 19:54:44.606 +10:00 INF] LEP Invoicer completed successfully in 1170ms. No work to process.
[2025-06-17 19:54:44.614 +10:00 INF] Database connection disposed
[2025-06-17 19:54:44.617 +10:00 INF] LEP Invoicer completed with result: 0
