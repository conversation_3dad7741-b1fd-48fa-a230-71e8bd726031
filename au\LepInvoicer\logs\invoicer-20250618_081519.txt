[2025-06-18 08:15:19.676 +10:00 INF] Starting LEP Invoicer application
[2025-06-18 08:15:19.708 +10:00 INF] Initializing FastReport...
[2025-06-18 08:15:19.784 +10:00 INF] FastReport initialized successfully
[2025-06-18 08:15:20.285 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-18 08:15:21.623 +10:00 INF] Starting LEP Invoicer at "2025-06-18T08:15:21.6228248+10:00"
[2025-06-18 08:15:21.626 +10:00 INF] Initializing database service...
[2025-06-18 08:15:21.629 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-18 08:15:21.726 +10:00 INF] Database connection established successfully
[2025-06-18 08:15:21.728 +10:00 INF] Database service initialized successfully
[2025-06-18 08:15:21.731 +10:00 INF] Checking for pending work...
[2025-06-18 08:15:21.733 +10:00 INF] Getting 500 orders to invoice
[2025-06-18 08:15:22.622 +10:00 INF] Found 0 orders to invoice (filtered 16 candidates)
[2025-06-18 08:15:22.625 +10:00 INF] Getting 100 credits to invoice
[2025-06-18 08:15:22.642 +10:00 INF] Found 0 credits to invoice
[2025-06-18 08:15:22.644 +10:00 INF] Getting 100 refunds to invoice
[2025-06-18 08:15:22.650 +10:00 INF] Found 0 refunds to invoice
[2025-06-18 08:15:22.653 +10:00 INF] No pending work found
[2025-06-18 08:15:22.655 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-18 08:15:22.656 +10:00 INF] Cleaning up invoicer logs...
[2025-06-18 08:15:22.742 +10:00 INF] LEP Invoicer completed successfully in 1119ms. No work to process.
[2025-06-18 08:15:22.749 +10:00 INF] Database connection disposed
[2025-06-18 08:15:22.750 +10:00 INF] LEP Invoicer completed with result: 0
