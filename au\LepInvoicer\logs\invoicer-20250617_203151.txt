[2025-06-17 20:31:51.662 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 20:31:51.698 +10:00 INF] Initializing FastReport...
[2025-06-17 20:31:51.791 +10:00 INF] FastReport initialized successfully
[2025-06-17 20:31:52.208 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 20:31:53.502 +10:00 INF] Starting LEP Invoicer at "2025-06-17T20:31:53.5023703+10:00"
[2025-06-17 20:31:53.506 +10:00 INF] Initializing database service...
[2025-06-17 20:31:53.508 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 20:31:53.609 +10:00 INF] Database connection established successfully
[2025-06-17 20:31:53.612 +10:00 INF] Database service initialized successfully
[2025-06-17 20:31:53.614 +10:00 INF] Checking for pending work...
[2025-06-17 20:31:53.617 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 20:31:54.536 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 20:31:54.538 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 20:31:54.550 +10:00 INF] Found 0 credits to invoice
[2025-06-17 20:31:54.552 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 20:31:54.555 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 20:31:54.557 +10:00 INF] No pending work found
[2025-06-17 20:31:54.560 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 20:31:54.561 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 20:31:54.634 +10:00 INF] LEP Invoicer completed successfully in 1131ms. No work to process.
[2025-06-17 20:31:54.640 +10:00 INF] Database connection disposed
[2025-06-17 20:31:54.661 +10:00 INF] LEP Invoicer completed with result: 0
