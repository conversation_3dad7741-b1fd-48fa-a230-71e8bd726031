[2025-06-17 19:01:11.791 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 19:01:11.819 +10:00 INF] Initializing FastReport...
[2025-06-17 19:01:11.903 +10:00 INF] FastReport initialized successfully
[2025-06-17 19:01:12.323 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 19:01:13.602 +10:00 INF] Starting LEP Invoicer at "2025-06-17T19:01:13.6022122+10:00"
[2025-06-17 19:01:13.605 +10:00 INF] Initializing database service...
[2025-06-17 19:01:13.626 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 19:01:13.741 +10:00 INF] Database connection established successfully
[2025-06-17 19:01:13.744 +10:00 INF] Database service initialized successfully
[2025-06-17 19:01:13.747 +10:00 INF] Checking for pending work...
[2025-06-17 19:01:13.749 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 19:01:14.700 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 19:01:14.703 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 19:01:14.716 +10:00 INF] Found 0 credits to invoice
[2025-06-17 19:01:14.718 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 19:01:14.722 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 19:01:14.723 +10:00 INF] No pending work found
[2025-06-17 19:01:14.725 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 19:01:14.726 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 19:01:14.799 +10:00 INF] LEP Invoicer completed successfully in 1196ms. No work to process.
[2025-06-17 19:01:14.805 +10:00 INF] Database connection disposed
[2025-06-17 19:01:14.807 +10:00 INF] LEP Invoicer completed with result: 0
