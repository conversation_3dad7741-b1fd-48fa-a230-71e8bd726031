[2025-06-17 15:20:47.403 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 15:20:47.433 +10:00 INF] Initializing FastReport...
[2025-06-17 15:20:47.544 +10:00 INF] FastReport initialized successfully
[2025-06-17 15:20:48.106 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 15:20:49.682 +10:00 INF] Starting LEP Invoicer at "2025-06-17T15:20:49.6825240+10:00"
[2025-06-17 15:20:49.688 +10:00 INF] Initializing database service...
[2025-06-17 15:20:49.691 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 15:20:49.838 +10:00 INF] Database connection established successfully
[2025-06-17 15:20:49.841 +10:00 INF] Database service initialized successfully
[2025-06-17 15:20:49.844 +10:00 INF] Checking for pending work...
[2025-06-17 15:20:49.848 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 15:20:50.892 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 15:20:50.898 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 15:20:50.943 +10:00 ERR] Failed to get credits to invoice
NHibernate.Exceptions.GenericADOException: could not execute query
[ 
                SELECT TOP (100) oc.*
                FROM [OrderCredit] oc
                INNER JOIN [Order] o ON oc.OrderId = o.Id
                INNER JOIN [CustomerUser] cu ON o.CustomerId = cu.Id
                WHERE (oc.Invoiced IS NULL OR oc.Invoiced = '')
                    AND oc.Type IN ('C', 'M', 'CI')
                    AND oc.OrderId IS NOT NULL
                    AND cu.Name NOT IN ('LEP Colour Printers Pty Ltd', 'LEP Marketing', 'LEP TEST J', 'LEP TEST T', 'lepdemo', 'LEP Colour Printers Pty Ltd', 'LEP Marketing', 'LEP TEST J', 'LEP TEST T', 'lepdemo')
                ORDER BY oc.DateCreated ]
[SQL: 
                SELECT TOP (100) oc.*
                FROM [OrderCredit] oc
                INNER JOIN [Order] o ON oc.OrderId = o.Id
                INNER JOIN [CustomerUser] cu ON o.CustomerId = cu.Id
                WHERE (oc.Invoiced IS NULL OR oc.Invoiced = '')
                    AND oc.Type IN ('C', 'M', 'CI')
                    AND oc.OrderId IS NOT NULL
                    AND cu.Name NOT IN ('LEP Colour Printers Pty Ltd', 'LEP Marketing', 'LEP TEST J', 'LEP TEST T', 'lepdemo', 'LEP Colour Printers Pty Ltd', 'LEP Marketing', 'LEP TEST J', 'LEP TEST T', 'lepdemo')
                ORDER BY oc.DateCreated]
 ---> System.Data.SqlClient.SqlException (0x80131904): Invalid object name 'CustomerUser'.
   at System.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at System.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at System.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at System.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at System.Data.SqlClient.SqlDataReader.TryConsumeMetaData()
   at System.Data.SqlClient.SqlDataReader.get_MetaData()
   at System.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString)
   at System.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean async, Int32 timeout, Task& task, Boolean asyncWrite, SqlDataReader ds)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, TaskCompletionSource`1 completion, Int32 timeout, Task& task, Boolean asyncWrite, String method)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior)
   at System.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at NHibernate.AdoNet.AbstractBatcher.DoExecuteReader(DbCommand cmd)
   at NHibernate.AdoNet.AbstractBatcher.ExecuteReader(DbCommand cmd)
   at NHibernate.Loader.Loader.GetResultSet(DbCommand st, QueryParameters queryParameters, ISessionImplementor session, IResultTransformer forcedResultTransformer)
   at NHibernate.Loader.Loader.DoQuery(ISessionImplementor session, QueryParameters queryParameters, Boolean returnProxies, IResultTransformer forcedResultTransformer, QueryCacheResultBuilder queryCacheResultBuilder)
   at NHibernate.Loader.Loader.DoQueryAndInitializeNonLazyCollections(ISessionImplementor session, QueryParameters queryParameters, Boolean returnProxies, IResultTransformer forcedResultTransformer, QueryCacheResultBuilder queryCacheResultBuilder)
   at NHibernate.Loader.Loader.DoList(ISessionImplementor session, QueryParameters queryParameters, IResultTransformer forcedResultTransformer, QueryCacheResultBuilder queryCacheResultBuilder)
ClientConnectionId:81de6a8b-fdba-4f96-b66a-f3d6404b8da6
Error Number:208,State:1,Class:16
   --- End of inner exception stack trace ---
   at NHibernate.Loader.Loader.DoList(ISessionImplementor session, QueryParameters queryParameters, IResultTransformer forcedResultTransformer, QueryCacheResultBuilder queryCacheResultBuilder)
   at NHibernate.Loader.Loader.DoList(ISessionImplementor session, QueryParameters queryParameters)
   at NHibernate.Loader.Loader.ListIgnoreQueryCache(ISessionImplementor session, QueryParameters queryParameters)
   at NHibernate.Loader.Loader.List(ISessionImplementor session, QueryParameters queryParameters, ISet`1 querySpaces)
   at NHibernate.Loader.Custom.CustomLoader.List(ISessionImplementor session, QueryParameters queryParameters)
   at NHibernate.Impl.SessionImpl.ListCustomQuery(ICustomQuery customQuery, QueryParameters queryParameters, IList results)
   at NHibernate.Impl.AbstractSessionImpl.List(NativeSQLQuerySpecification spec, QueryParameters queryParameters, IList results)
   at NHibernate.Impl.AbstractSessionImpl.List[T](NativeSQLQuerySpecification spec, QueryParameters queryParameters)
   at NHibernate.Impl.SqlQueryImpl.List[T]()
   at LepInvoicer.Implementations.DatabaseService.GetCreditsToInvoice(Int32 batchSize) in C:\LepSF\au\LepInvoicer\Implementations\DatabaseService.cs:line 113
[2025-06-17 15:20:51.086 +10:00 ERR] LEP Invoicer failed after 1404ms
NHibernate.Exceptions.GenericADOException: could not execute query
[ 
                SELECT TOP (100) oc.*
                FROM [OrderCredit] oc
                INNER JOIN [Order] o ON oc.OrderId = o.Id
                INNER JOIN [CustomerUser] cu ON o.CustomerId = cu.Id
                WHERE (oc.Invoiced IS NULL OR oc.Invoiced = '')
                    AND oc.Type IN ('C', 'M', 'CI')
                    AND oc.OrderId IS NOT NULL
                    AND cu.Name NOT IN ('LEP Colour Printers Pty Ltd', 'LEP Marketing', 'LEP TEST J', 'LEP TEST T', 'lepdemo', 'LEP Colour Printers Pty Ltd', 'LEP Marketing', 'LEP TEST J', 'LEP TEST T', 'lepdemo')
                ORDER BY oc.DateCreated ]
[SQL: 
                SELECT TOP (100) oc.*
                FROM [OrderCredit] oc
                INNER JOIN [Order] o ON oc.OrderId = o.Id
                INNER JOIN [CustomerUser] cu ON o.CustomerId = cu.Id
                WHERE (oc.Invoiced IS NULL OR oc.Invoiced = '')
                    AND oc.Type IN ('C', 'M', 'CI')
                    AND oc.OrderId IS NOT NULL
                    AND cu.Name NOT IN ('LEP Colour Printers Pty Ltd', 'LEP Marketing', 'LEP TEST J', 'LEP TEST T', 'lepdemo', 'LEP Colour Printers Pty Ltd', 'LEP Marketing', 'LEP TEST J', 'LEP TEST T', 'lepdemo')
                ORDER BY oc.DateCreated]
 ---> System.Data.SqlClient.SqlException (0x80131904): Invalid object name 'CustomerUser'.
   at System.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at System.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at System.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at System.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at System.Data.SqlClient.SqlDataReader.TryConsumeMetaData()
   at System.Data.SqlClient.SqlDataReader.get_MetaData()
   at System.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString)
   at System.Data.SqlClient.SqlCommand.RunExecuteReaderTds(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, Boolean async, Int32 timeout, Task& task, Boolean asyncWrite, SqlDataReader ds)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, TaskCompletionSource`1 completion, Int32 timeout, Task& task, Boolean asyncWrite, String method)
   at System.Data.SqlClient.SqlCommand.RunExecuteReader(CommandBehavior cmdBehavior, RunBehavior runBehavior, Boolean returnStream, String method)
   at System.Data.SqlClient.SqlCommand.ExecuteReader(CommandBehavior behavior)
   at System.Data.SqlClient.SqlCommand.ExecuteDbDataReader(CommandBehavior behavior)
   at NHibernate.AdoNet.AbstractBatcher.DoExecuteReader(DbCommand cmd)
   at NHibernate.AdoNet.AbstractBatcher.ExecuteReader(DbCommand cmd)
   at NHibernate.Loader.Loader.GetResultSet(DbCommand st, QueryParameters queryParameters, ISessionImplementor session, IResultTransformer forcedResultTransformer)
   at NHibernate.Loader.Loader.DoQuery(ISessionImplementor session, QueryParameters queryParameters, Boolean returnProxies, IResultTransformer forcedResultTransformer, QueryCacheResultBuilder queryCacheResultBuilder)
   at NHibernate.Loader.Loader.DoQueryAndInitializeNonLazyCollections(ISessionImplementor session, QueryParameters queryParameters, Boolean returnProxies, IResultTransformer forcedResultTransformer, QueryCacheResultBuilder queryCacheResultBuilder)
   at NHibernate.Loader.Loader.DoList(ISessionImplementor session, QueryParameters queryParameters, IResultTransformer forcedResultTransformer, QueryCacheResultBuilder queryCacheResultBuilder)
ClientConnectionId:81de6a8b-fdba-4f96-b66a-f3d6404b8da6
Error Number:208,State:1,Class:16
   --- End of inner exception stack trace ---
   at NHibernate.Loader.Loader.DoList(ISessionImplementor session, QueryParameters queryParameters, IResultTransformer forcedResultTransformer, QueryCacheResultBuilder queryCacheResultBuilder)
   at NHibernate.Loader.Loader.DoList(ISessionImplementor session, QueryParameters queryParameters)
   at NHibernate.Loader.Loader.ListIgnoreQueryCache(ISessionImplementor session, QueryParameters queryParameters)
   at NHibernate.Loader.Loader.List(ISessionImplementor session, QueryParameters queryParameters, ISet`1 querySpaces)
   at NHibernate.Loader.Custom.CustomLoader.List(ISessionImplementor session, QueryParameters queryParameters)
   at NHibernate.Impl.SessionImpl.ListCustomQuery(ICustomQuery customQuery, QueryParameters queryParameters, IList results)
   at NHibernate.Impl.AbstractSessionImpl.List(NativeSQLQuerySpecification spec, QueryParameters queryParameters, IList results)
   at NHibernate.Impl.AbstractSessionImpl.List[T](NativeSQLQuerySpecification spec, QueryParameters queryParameters)
   at NHibernate.Impl.SqlQueryImpl.List[T]()
   at LepInvoicer.Implementations.DatabaseService.GetCreditsToInvoice(Int32 batchSize) in C:\LepSF\au\LepInvoicer\Implementations\DatabaseService.cs:line 113
   at LepInvoicer.Implementations.InvoicerService.CheckForPendingWork() in C:\LepSF\au\LepInvoicer\Implementations\InvoicerService.cs:line 127
   at LepInvoicer.Implementations.InvoicerService.RunInvoicer() in C:\LepSF\au\LepInvoicer\Implementations\InvoicerService.cs:line 55
[2025-06-17 15:20:51.157 +10:00 INF] Database connection disposed
[2025-06-17 15:20:51.159 +10:00 INF] LEP Invoicer completed with result: 1
