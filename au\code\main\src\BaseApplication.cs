#region

using lep.security;
using lep.user;

using Serilog;
using NHibernate;
using System;
using System.Reflection;
using System.Threading;

#endregion

namespace lep
{
	public class BaseApplication
	{
		//// private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

		#region services

		public ISession Session { get; set; }
		public ISecurityApplication SecurityApplication { get; set; }
		protected IUser CurrentUser => SecurityApplication.Identity as IUser;

		#endregion

		public BaseApplication(ISession session, ISecurityApplication securityApp)
		{
			Session = session;
			SecurityApplication = securityApp;
			SiteLocation = SiteLocation.AU;
		}

		public T Get<T>(object id) => Session.Get<T>(id);

		public void Evict(object obj) => Session.Evict(obj);

		public void ForceSave<T>(T obj) => RunWithTransaction(Session, () => Session.Save(obj));

		public void Save<T>(T obj) => RunWithTransaction(Session, () => Session.SaveOrUpdate(obj));

		public void Delete<T>(T obj) => RunWithTransaction(Session, () => Session.Delete(obj));

		public void RunWithTransaction(ISession session, ThreadStart func)
		{
			//func();
			//return;
			


			if (!session.GetCurrentTransaction()?.IsActive ?? true)
			{
				using (var transaction = session.BeginTransaction())
				{
					try
					{
						func();
						//Session.Flush();
						transaction.Commit();
					}
					catch (Exception ex)
					{
						transaction.Rollback();
						Log.Error(ex, ex.Message);
						throw;
					}
				}
			}
			else
			{
				func();
			}
		}

		public SiteLocation SiteLocation { set; protected get; }
	}
}
