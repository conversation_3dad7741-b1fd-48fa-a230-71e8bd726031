[2025-06-17 19:55:46.625 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 19:55:46.652 +10:00 INF] Initializing FastReport...
[2025-06-17 19:55:46.723 +10:00 INF] FastReport initialized successfully
[2025-06-17 19:55:47.132 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 19:55:48.458 +10:00 INF] Starting LEP Invoicer at "2025-06-17T19:55:48.4580225+10:00"
[2025-06-17 19:55:48.461 +10:00 INF] Initializing database service...
[2025-06-17 19:55:48.464 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 19:55:48.560 +10:00 INF] Database connection established successfully
[2025-06-17 19:55:48.561 +10:00 INF] Database service initialized successfully
[2025-06-17 19:55:48.564 +10:00 INF] Checking for pending work...
[2025-06-17 19:55:48.567 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 19:55:49.465 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 19:55:49.484 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 19:55:49.498 +10:00 INF] Found 0 credits to invoice
[2025-06-17 19:55:49.506 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 19:55:49.510 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 19:55:49.517 +10:00 INF] No pending work found
[2025-06-17 19:55:49.518 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 19:55:49.521 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 19:55:49.626 +10:00 INF] LEP Invoicer completed successfully in 1168ms. No work to process.
[2025-06-17 19:55:49.634 +10:00 INF] Database connection disposed
[2025-06-17 19:55:49.638 +10:00 INF] LEP Invoicer completed with result: 0
