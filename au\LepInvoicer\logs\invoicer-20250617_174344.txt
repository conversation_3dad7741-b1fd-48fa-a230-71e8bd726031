[2025-06-17 17:43:44.747 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 17:43:44.777 +10:00 INF] Initializing FastReport...
[2025-06-17 17:43:44.861 +10:00 INF] FastReport initialized successfully
[2025-06-17 17:43:45.275 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 17:43:46.568 +10:00 INF] Starting LEP Invoicer at "2025-06-17T17:43:46.5685212+10:00"
[2025-06-17 17:43:46.573 +10:00 INF] Initializing database service...
[2025-06-17 17:43:46.576 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 17:43:46.692 +10:00 INF] Database connection established successfully
[2025-06-17 17:43:46.694 +10:00 INF] Database service initialized successfully
[2025-06-17 17:43:46.698 +10:00 INF] Checking for pending work...
[2025-06-17 17:43:46.701 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 17:43:47.688 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 17:43:47.693 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 17:43:47.709 +10:00 INF] Found 0 credits to invoice
[2025-06-17 17:43:47.714 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 17:43:47.718 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 17:43:47.722 +10:00 INF] No pending work found
[2025-06-17 17:43:47.723 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 17:43:47.738 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 17:43:47.843 +10:00 INF] LEP Invoicer completed successfully in 1274ms. No work to process.
[2025-06-17 17:43:47.849 +10:00 INF] Database connection disposed
[2025-06-17 17:43:47.852 +10:00 INF] LEP Invoicer completed with result: 0
