[2025-06-17 18:27:20.578 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 18:27:20.721 +10:00 INF] Initializing FastReport...
[2025-06-17 18:27:20.791 +10:00 INF] FastReport initialized successfully
[2025-06-17 18:27:21.283 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 18:27:22.475 +10:00 INF] Starting LEP Invoicer at "2025-06-17T18:27:22.4747191+10:00"
[2025-06-17 18:27:22.479 +10:00 INF] Initializing database service...
[2025-06-17 18:27:22.482 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 18:27:22.593 +10:00 INF] Database connection established successfully
[2025-06-17 18:27:22.594 +10:00 INF] Database service initialized successfully
[2025-06-17 18:27:22.597 +10:00 INF] Checking for pending work...
[2025-06-17 18:27:22.600 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 18:27:23.529 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 18:27:23.531 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 18:27:23.544 +10:00 INF] Found 0 credits to invoice
[2025-06-17 18:27:23.546 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 18:27:23.551 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 18:27:23.552 +10:00 INF] No pending work found
[2025-06-17 18:27:23.554 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 18:27:23.555 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 18:27:23.651 +10:00 INF] LEP Invoicer completed successfully in 1176ms. No work to process.
[2025-06-17 18:27:23.657 +10:00 INF] Database connection disposed
[2025-06-17 18:27:23.659 +10:00 INF] LEP Invoicer completed with result: 0
