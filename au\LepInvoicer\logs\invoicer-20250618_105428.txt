[2025-06-18 10:54:28.710 +10:00 INF] Starting LEP Invoicer application
[2025-06-18 10:54:28.738 +10:00 INF] Initializing FastReport...
[2025-06-18 10:54:28.818 +10:00 INF] FastReport initialized successfully
[2025-06-18 10:54:29.298 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-18 10:54:30.702 +10:00 INF] Starting LEP Invoicer at "2025-06-18T10:54:30.7023062+10:00"
[2025-06-18 10:54:30.711 +10:00 INF] Initializing database service...
[2025-06-18 10:54:30.714 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-18 10:54:30.830 +10:00 INF] Database connection established successfully
[2025-06-18 10:54:30.831 +10:00 INF] Database service initialized successfully
[2025-06-18 10:54:30.834 +10:00 INF] Checking for pending work...
[2025-06-18 10:54:30.837 +10:00 INF] Getting 500 orders to invoice
[2025-06-18 10:54:31.513 +10:00 INF] Found 0 orders to invoice (marked 0 zero-priced as invoiced, filtered 0 candidates)
[2025-06-18 10:54:31.515 +10:00 INF] Getting 100 credits to invoice
[2025-06-18 10:54:31.528 +10:00 INF] Found 0 credits to invoice
[2025-06-18 10:54:31.529 +10:00 INF] Getting 100 refunds to invoice
[2025-06-18 10:54:31.534 +10:00 INF] Found 0 refunds to invoice
[2025-06-18 10:54:31.535 +10:00 INF] No pending work found
[2025-06-18 10:54:31.536 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-18 10:54:31.537 +10:00 INF] Cleaning up invoicer logs...
[2025-06-18 10:54:31.612 +10:00 INF] LEP Invoicer completed successfully in 910ms. No work to process.
[2025-06-18 10:54:31.620 +10:00 INF] Database connection disposed
[2025-06-18 10:54:31.622 +10:00 INF] LEP Invoicer completed with result: 0
