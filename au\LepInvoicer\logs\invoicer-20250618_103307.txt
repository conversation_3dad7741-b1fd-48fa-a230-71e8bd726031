[2025-06-18 10:33:07.502 +10:00 INF] Starting LEP Invoicer application
[2025-06-18 10:33:07.531 +10:00 INF] Initializing FastReport...
[2025-06-18 10:33:07.627 +10:00 INF] FastReport initialized successfully
[2025-06-18 10:33:08.185 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-18 10:33:11.941 +10:00 INF] Starting LEP Invoicer at "2025-06-18T10:33:11.9414641+10:00"
[2025-06-18 10:33:11.960 +10:00 INF] Initializing database service...
[2025-06-18 10:33:11.963 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-18 10:33:12.079 +10:00 INF] Database connection established successfully
[2025-06-18 10:33:12.082 +10:00 INF] Database service initialized successfully
[2025-06-18 10:33:12.084 +10:00 INF] Checking for pending work...
[2025-06-18 10:33:12.087 +10:00 INF] Getting 500 orders to invoice
[2025-06-18 10:33:13.004 +10:00 INF] Found 0 orders to invoice (filtered 17 candidates)
[2025-06-18 10:33:13.007 +10:00 INF] Getting 100 credits to invoice
[2025-06-18 10:33:13.020 +10:00 INF] Found 0 credits to invoice
[2025-06-18 10:33:13.022 +10:00 INF] Getting 100 refunds to invoice
[2025-06-18 10:33:13.025 +10:00 INF] Found 0 refunds to invoice
[2025-06-18 10:33:13.027 +10:00 INF] No pending work found
[2025-06-18 10:33:13.028 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-18 10:33:13.030 +10:00 INF] Cleaning up invoicer logs...
[2025-06-18 10:33:13.119 +10:00 INF] LEP Invoicer completed successfully in 1177ms. No work to process.
[2025-06-18 10:33:13.125 +10:00 INF] Database connection disposed
[2025-06-18 10:33:13.127 +10:00 INF] LEP Invoicer completed with result: 0
