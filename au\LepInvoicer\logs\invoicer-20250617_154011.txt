[2025-06-17 15:40:11.476 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 15:40:11.505 +10:00 INF] Initializing FastReport...
[2025-06-17 15:40:11.579 +10:00 INF] FastReport initialized successfully
[2025-06-17 15:40:12.038 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 15:40:13.363 +10:00 INF] Starting LEP Invoicer at "2025-06-17T15:40:13.3629697+10:00"
[2025-06-17 15:40:13.367 +10:00 INF] Initializing database service...
[2025-06-17 15:40:13.370 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 15:40:13.468 +10:00 INF] Database connection established successfully
[2025-06-17 15:40:13.469 +10:00 INF] Database service initialized successfully
[2025-06-17 15:40:13.471 +10:00 INF] Checking for pending work...
[2025-06-17 15:40:13.474 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 15:40:14.366 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 15:40:14.369 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 15:40:14.381 +10:00 INF] Found 0 credits to invoice
[2025-06-17 15:40:14.383 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 15:40:14.388 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 15:40:14.389 +10:00 INF] No pending work found
[2025-06-17 15:40:14.390 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 15:40:14.392 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 15:40:14.465 +10:00 INF] LEP Invoicer completed successfully in 1102ms. No work to process.
[2025-06-17 15:40:14.472 +10:00 INF] Database connection disposed
[2025-06-17 15:40:14.474 +10:00 INF] LEP Invoicer completed with result: 0
