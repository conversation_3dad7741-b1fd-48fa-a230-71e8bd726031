[2025-06-17 18:28:26.536 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 18:28:26.576 +10:00 INF] Initializing FastReport...
[2025-06-17 18:28:26.664 +10:00 INF] FastReport initialized successfully
[2025-06-17 18:28:27.191 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 18:28:28.479 +10:00 INF] Starting LEP Invoicer at "2025-06-17T18:28:28.4789602+10:00"
[2025-06-17 18:28:28.482 +10:00 INF] Initializing database service...
[2025-06-17 18:28:28.485 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 18:28:28.586 +10:00 INF] Database connection established successfully
[2025-06-17 18:28:28.588 +10:00 INF] Database service initialized successfully
[2025-06-17 18:28:28.591 +10:00 INF] Checking for pending work...
[2025-06-17 18:28:28.594 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 18:28:29.451 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 18:28:29.455 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 18:28:29.467 +10:00 INF] Found 0 credits to invoice
[2025-06-17 18:28:29.469 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 18:28:29.474 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 18:28:29.475 +10:00 INF] No pending work found
[2025-06-17 18:28:29.476 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 18:28:29.478 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 18:28:29.551 +10:00 INF] LEP Invoicer completed successfully in 1072ms. No work to process.
[2025-06-17 18:28:29.557 +10:00 INF] Database connection disposed
[2025-06-17 18:28:29.559 +10:00 INF] LEP Invoicer completed with result: 0
