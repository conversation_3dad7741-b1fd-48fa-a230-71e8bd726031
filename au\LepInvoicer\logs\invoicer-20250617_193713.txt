[2025-06-17 19:37:13.682 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 19:37:13.711 +10:00 INF] Initializing FastReport...
[2025-06-17 19:37:13.798 +10:00 INF] FastReport initialized successfully
[2025-06-17 19:37:14.472 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 19:37:15.759 +10:00 INF] Starting LEP Invoicer at "2025-06-17T19:37:15.7593185+10:00"
[2025-06-17 19:37:15.763 +10:00 INF] Initializing database service...
[2025-06-17 19:37:15.766 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 19:37:15.878 +10:00 INF] Database connection established successfully
[2025-06-17 19:37:15.879 +10:00 INF] Database service initialized successfully
[2025-06-17 19:37:15.882 +10:00 INF] Checking for pending work...
[2025-06-17 19:37:15.885 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 19:37:16.791 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 19:37:16.794 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 19:37:16.806 +10:00 INF] Found 0 credits to invoice
[2025-06-17 19:37:16.809 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 19:37:16.813 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 19:37:16.814 +10:00 INF] No pending work found
[2025-06-17 19:37:16.825 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 19:37:16.828 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 19:37:16.907 +10:00 INF] LEP Invoicer completed successfully in 1147ms. No work to process.
[2025-06-17 19:37:16.914 +10:00 INF] Database connection disposed
[2025-06-17 19:37:16.918 +10:00 INF] LEP Invoicer completed with result: 0
