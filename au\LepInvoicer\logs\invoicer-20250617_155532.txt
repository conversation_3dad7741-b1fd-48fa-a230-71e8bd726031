[2025-06-17 15:55:32.472 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 15:55:32.503 +10:00 INF] Initializing FastReport...
[2025-06-17 15:55:32.576 +10:00 INF] FastReport initialized successfully
[2025-06-17 15:55:32.986 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 15:55:34.362 +10:00 INF] Starting LEP Invoicer at "2025-06-17T15:55:34.3621401+10:00"
[2025-06-17 15:55:34.367 +10:00 INF] Initializing database service...
[2025-06-17 15:55:34.370 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 15:55:34.466 +10:00 INF] Database connection established successfully
[2025-06-17 15:55:34.468 +10:00 INF] Database service initialized successfully
[2025-06-17 15:55:34.470 +10:00 INF] Checking for pending work...
[2025-06-17 15:55:34.473 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 15:55:35.375 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 15:55:35.378 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 15:55:35.392 +10:00 INF] Found 0 credits to invoice
[2025-06-17 15:55:35.395 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 15:55:35.398 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 15:55:35.400 +10:00 INF] No pending work found
[2025-06-17 15:55:35.401 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 15:55:35.402 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 15:55:35.477 +10:00 INF] LEP Invoicer completed successfully in 1114ms. No work to process.
[2025-06-17 15:55:35.483 +10:00 INF] Database connection disposed
[2025-06-17 15:55:35.485 +10:00 INF] LEP Invoicer completed with result: 0
