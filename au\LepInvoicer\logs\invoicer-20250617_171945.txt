[2025-06-17 17:19:45.450 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 17:19:45.478 +10:00 INF] Initializing FastReport...
[2025-06-17 17:19:45.555 +10:00 INF] FastReport initialized successfully
[2025-06-17 17:19:45.975 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 17:19:47.189 +10:00 INF] Starting LEP Invoicer at "2025-06-17T17:19:47.1894288+10:00"
[2025-06-17 17:19:47.193 +10:00 INF] Initializing database service...
[2025-06-17 17:19:47.196 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 17:19:47.300 +10:00 INF] Database connection established successfully
[2025-06-17 17:19:47.301 +10:00 INF] Database service initialized successfully
[2025-06-17 17:19:47.304 +10:00 INF] Checking for pending work...
[2025-06-17 17:19:47.307 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 17:19:48.154 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 17:19:48.157 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 17:19:48.169 +10:00 INF] Found 0 credits to invoice
[2025-06-17 17:19:48.171 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 17:19:48.174 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 17:19:48.176 +10:00 INF] No pending work found
[2025-06-17 17:19:48.177 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 17:19:48.179 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 17:19:48.251 +10:00 INF] LEP Invoicer completed successfully in 1061ms. No work to process.
[2025-06-17 17:19:48.257 +10:00 INF] Database connection disposed
[2025-06-17 17:19:48.259 +10:00 INF] LEP Invoicer completed with result: 0
