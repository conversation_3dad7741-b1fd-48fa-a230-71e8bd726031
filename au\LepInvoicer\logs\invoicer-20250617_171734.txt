[2025-06-17 17:17:34.603 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 17:17:34.651 +10:00 INF] Initializing FastReport...
[2025-06-17 17:17:34.723 +10:00 INF] FastReport initialized successfully
[2025-06-17 17:17:35.154 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 17:17:36.437 +10:00 INF] Starting LEP Invoicer at "2025-06-17T17:17:36.4371229+10:00"
[2025-06-17 17:17:36.441 +10:00 INF] Initializing database service...
[2025-06-17 17:17:36.444 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 17:17:36.572 +10:00 INF] Database connection established successfully
[2025-06-17 17:17:36.578 +10:00 INF] Database service initialized successfully
[2025-06-17 17:17:36.581 +10:00 INF] Checking for pending work...
[2025-06-17 17:17:36.585 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 17:17:37.520 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 17:17:37.523 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 17:17:37.538 +10:00 INF] Found 0 credits to invoice
[2025-06-17 17:17:37.551 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 17:17:37.555 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 17:17:37.556 +10:00 INF] No pending work found
[2025-06-17 17:17:37.558 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 17:17:37.565 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 17:17:37.656 +10:00 INF] LEP Invoicer completed successfully in 1219ms. No work to process.
[2025-06-17 17:17:37.664 +10:00 INF] Database connection disposed
[2025-06-17 17:17:37.672 +10:00 INF] LEP Invoicer completed with result: 0
