using System;

namespace lep.user
{
	public interface IUser
    {
        int Id { get; set; }
        String Username { get; set; }
        String HashPassword { get; set; }
        Boolean IsEnabled { get; set; }
        Boolean IsStaff { get; set; }
        String Email { get; set; }
        String FirstName { get; set; }
        String LastName { get; set; }
        String Phone { get; set; }
        String AreaCode { get; set; }
        String Mobile { get; set; }
        DateTime DateCreated { get; set; }
        DateTime DateModified { get; set; }
        DateTime? LastLogin { get; set; }
    }
}
