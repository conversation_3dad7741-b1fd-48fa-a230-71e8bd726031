[2025-06-17 17:38:16.568 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 17:38:16.596 +10:00 INF] Initializing FastReport...
[2025-06-17 17:38:16.697 +10:00 INF] FastReport initialized successfully
[2025-06-17 17:38:17.170 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 17:38:18.433 +10:00 INF] Starting LEP Invoicer at "2025-06-17T17:38:18.4330610+10:00"
[2025-06-17 17:38:18.436 +10:00 INF] Initializing database service...
[2025-06-17 17:38:18.439 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 17:38:18.540 +10:00 INF] Database connection established successfully
[2025-06-17 17:38:18.541 +10:00 INF] Database service initialized successfully
[2025-06-17 17:38:18.544 +10:00 INF] Checking for pending work...
[2025-06-17 17:38:18.547 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 17:38:19.450 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 17:38:19.453 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 17:38:19.465 +10:00 INF] Found 0 credits to invoice
[2025-06-17 17:38:19.467 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 17:38:19.470 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 17:38:19.472 +10:00 INF] No pending work found
[2025-06-17 17:38:19.474 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 17:38:19.475 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 17:38:19.555 +10:00 INF] LEP Invoicer completed successfully in 1122ms. No work to process.
[2025-06-17 17:38:19.563 +10:00 INF] Database connection disposed
[2025-06-17 17:38:19.568 +10:00 INF] LEP Invoicer completed with result: 0
