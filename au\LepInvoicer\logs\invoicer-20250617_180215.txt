[2025-06-17 18:02:15.491 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 18:02:15.517 +10:00 INF] Initializing FastReport...
[2025-06-17 18:02:15.588 +10:00 INF] FastReport initialized successfully
[2025-06-17 18:02:16.029 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 18:02:17.284 +10:00 INF] Starting LEP Invoicer at "2025-06-17T18:02:17.2836588+10:00"
[2025-06-17 18:02:17.287 +10:00 INF] Initializing database service...
[2025-06-17 18:02:17.291 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 18:02:17.395 +10:00 INF] Database connection established successfully
[2025-06-17 18:02:17.396 +10:00 INF] Database service initialized successfully
[2025-06-17 18:02:17.400 +10:00 INF] Checking for pending work...
[2025-06-17 18:02:17.403 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 18:02:18.282 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 18:02:18.287 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 18:02:18.302 +10:00 INF] Found 0 credits to invoice
[2025-06-17 18:02:18.305 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 18:02:18.311 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 18:02:18.317 +10:00 INF] No pending work found
[2025-06-17 18:02:18.320 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 18:02:18.323 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 18:02:18.438 +10:00 INF] LEP Invoicer completed successfully in 1154ms. No work to process.
[2025-06-17 18:02:18.445 +10:00 INF] Database connection disposed
[2025-06-17 18:02:18.447 +10:00 INF] LEP Invoicer completed with result: 0
