[2025-06-18 09:21:53.595 +10:00 INF] Starting LEP Invoicer application
[2025-06-18 09:21:53.628 +10:00 INF] Initializing FastReport...
[2025-06-18 09:21:53.703 +10:00 INF] FastReport initialized successfully
[2025-06-18 09:21:54.177 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-18 09:21:55.617 +10:00 INF] Starting LEP Invoicer at "2025-06-18T09:21:55.6172446+10:00"
[2025-06-18 09:21:55.621 +10:00 INF] Initializing database service...
[2025-06-18 09:21:55.627 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-18 09:21:55.754 +10:00 INF] Database connection established successfully
[2025-06-18 09:21:55.755 +10:00 INF] Database service initialized successfully
[2025-06-18 09:21:55.763 +10:00 INF] Checking for pending work...
[2025-06-18 09:21:55.768 +10:00 INF] Getting 500 orders to invoice
[2025-06-18 09:21:56.678 +10:00 INF] Found 0 orders to invoice (filtered 16 candidates)
[2025-06-18 09:21:56.680 +10:00 INF] Getting 100 credits to invoice
[2025-06-18 09:21:56.692 +10:00 INF] Found 0 credits to invoice
[2025-06-18 09:21:56.694 +10:00 INF] Getting 100 refunds to invoice
[2025-06-18 09:21:56.698 +10:00 INF] Found 0 refunds to invoice
[2025-06-18 09:21:56.699 +10:00 INF] No pending work found
[2025-06-18 09:21:56.700 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-18 09:21:56.702 +10:00 INF] Cleaning up invoicer logs...
[2025-06-18 09:21:56.801 +10:00 INF] LEP Invoicer completed successfully in 1183ms. No work to process.
[2025-06-18 09:21:56.808 +10:00 INF] Database connection disposed
[2025-06-18 09:21:56.818 +10:00 INF] LEP Invoicer completed with result: 0
