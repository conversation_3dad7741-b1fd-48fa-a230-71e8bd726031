[2025-06-18 10:00:16.669 +10:00 INF] Starting LEP Invoicer application
[2025-06-18 10:00:16.699 +10:00 INF] Initializing FastReport...
[2025-06-18 10:00:16.796 +10:00 INF] FastReport initialized successfully
[2025-06-18 10:00:17.320 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-18 10:00:18.754 +10:00 INF] Starting LEP Invoicer at "2025-06-18T10:00:18.7544704+10:00"
[2025-06-18 10:00:18.758 +10:00 INF] Initializing database service...
[2025-06-18 10:00:18.761 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-18 10:00:18.866 +10:00 INF] Database connection established successfully
[2025-06-18 10:00:18.867 +10:00 INF] Database service initialized successfully
[2025-06-18 10:00:18.870 +10:00 INF] Checking for pending work...
[2025-06-18 10:00:18.872 +10:00 INF] Getting 500 orders to invoice
[2025-06-18 10:00:19.722 +10:00 INF] Found 0 orders to invoice (filtered 16 candidates)
[2025-06-18 10:00:19.731 +10:00 INF] Getting 100 credits to invoice
[2025-06-18 10:00:19.751 +10:00 INF] Found 0 credits to invoice
[2025-06-18 10:00:19.757 +10:00 INF] Getting 100 refunds to invoice
[2025-06-18 10:00:19.769 +10:00 INF] Found 0 refunds to invoice
[2025-06-18 10:00:19.774 +10:00 INF] No pending work found
[2025-06-18 10:00:19.777 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-18 10:00:19.778 +10:00 INF] Cleaning up invoicer logs...
[2025-06-18 10:00:19.868 +10:00 INF] LEP Invoicer completed successfully in 1114ms. No work to process.
[2025-06-18 10:00:19.879 +10:00 INF] Database connection disposed
[2025-06-18 10:00:19.881 +10:00 INF] LEP Invoicer completed with result: 0
