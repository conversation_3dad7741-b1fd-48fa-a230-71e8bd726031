[2025-06-18 08:57:17.917 +10:00 INF] Starting LEP Invoicer application
[2025-06-18 08:57:17.950 +10:00 INF] Initializing FastReport...
[2025-06-18 08:57:18.045 +10:00 INF] FastReport initialized successfully
[2025-06-18 08:57:18.581 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-18 08:57:20.078 +10:00 INF] Starting LEP Invoicer at "2025-06-18T08:57:20.0780417+10:00"
[2025-06-18 08:57:20.081 +10:00 INF] Initializing database service...
[2025-06-18 08:57:20.084 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-18 08:57:20.191 +10:00 INF] Database connection established successfully
[2025-06-18 08:57:20.193 +10:00 INF] Database service initialized successfully
[2025-06-18 08:57:20.198 +10:00 INF] Checking for pending work...
[2025-06-18 08:57:20.208 +10:00 INF] Getting 500 orders to invoice
[2025-06-18 08:57:21.135 +10:00 INF] Found 0 orders to invoice (filtered 16 candidates)
[2025-06-18 08:57:21.140 +10:00 INF] Getting 100 credits to invoice
[2025-06-18 08:57:21.161 +10:00 INF] Found 0 credits to invoice
[2025-06-18 08:57:21.165 +10:00 INF] Getting 100 refunds to invoice
[2025-06-18 08:57:21.174 +10:00 INF] Found 0 refunds to invoice
[2025-06-18 08:57:21.175 +10:00 INF] No pending work found
[2025-06-18 08:57:21.181 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-18 08:57:21.197 +10:00 INF] Cleaning up invoicer logs...
[2025-06-18 08:57:21.290 +10:00 INF] LEP Invoicer completed successfully in 1211ms. No work to process.
[2025-06-18 08:57:21.296 +10:00 INF] Database connection disposed
[2025-06-18 08:57:21.298 +10:00 INF] LEP Invoicer completed with result: 0
