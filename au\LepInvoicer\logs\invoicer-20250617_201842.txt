[2025-06-17 20:18:42.571 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 20:18:42.597 +10:00 INF] Initializing FastReport...
[2025-06-17 20:18:42.684 +10:00 INF] FastReport initialized successfully
[2025-06-17 20:18:43.064 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 20:18:44.298 +10:00 INF] Starting LEP Invoicer at "2025-06-17T20:18:44.2981280+10:00"
[2025-06-17 20:18:44.301 +10:00 INF] Initializing database service...
[2025-06-17 20:18:44.304 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 20:18:44.405 +10:00 INF] Database connection established successfully
[2025-06-17 20:18:44.406 +10:00 INF] Database service initialized successfully
[2025-06-17 20:18:44.409 +10:00 INF] Checking for pending work...
[2025-06-17 20:18:44.413 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 20:18:45.300 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 20:18:45.302 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 20:18:45.314 +10:00 INF] Found 0 credits to invoice
[2025-06-17 20:18:45.316 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 20:18:45.319 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 20:18:45.322 +10:00 INF] No pending work found
[2025-06-17 20:18:45.323 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 20:18:45.325 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 20:18:45.407 +10:00 INF] LEP Invoicer completed successfully in 1108ms. No work to process.
[2025-06-17 20:18:45.414 +10:00 INF] Database connection disposed
[2025-06-17 20:18:45.416 +10:00 INF] LEP Invoicer completed with result: 0
