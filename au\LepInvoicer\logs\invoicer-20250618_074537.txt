[2025-06-18 07:45:37.512 +10:00 INF] Starting LEP Invoicer application
[2025-06-18 07:45:37.558 +10:00 INF] Initializing FastReport...
[2025-06-18 07:45:37.794 +10:00 INF] FastReport initialized successfully
[2025-06-18 07:45:38.196 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-18 07:45:39.471 +10:00 INF] Starting LEP Invoicer at "2025-06-18T07:45:39.4708391+10:00"
[2025-06-18 07:45:39.483 +10:00 INF] Initializing database service...
[2025-06-18 07:45:39.496 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-18 07:45:39.642 +10:00 INF] Database connection established successfully
[2025-06-18 07:45:39.644 +10:00 INF] Database service initialized successfully
[2025-06-18 07:45:39.646 +10:00 INF] Checking for pending work...
[2025-06-18 07:45:39.649 +10:00 INF] Getting 500 orders to invoice
[2025-06-18 07:45:40.689 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-18 07:45:40.696 +10:00 INF] Getting 100 credits to invoice
[2025-06-18 07:45:40.709 +10:00 INF] Found 0 credits to invoice
[2025-06-18 07:45:40.711 +10:00 INF] Getting 100 refunds to invoice
[2025-06-18 07:45:40.715 +10:00 INF] Found 0 refunds to invoice
[2025-06-18 07:45:40.717 +10:00 INF] No pending work found
[2025-06-18 07:45:40.718 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-18 07:45:40.721 +10:00 INF] Cleaning up invoicer logs...
[2025-06-18 07:45:40.797 +10:00 INF] LEP Invoicer completed successfully in 1326ms. No work to process.
[2025-06-18 07:45:40.803 +10:00 INF] Database connection disposed
[2025-06-18 07:45:40.805 +10:00 INF] LEP Invoicer completed with result: 0
