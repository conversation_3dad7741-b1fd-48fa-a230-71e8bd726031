[2025-06-18 09:43:55.655 +10:00 INF] Starting LEP Invoicer application
[2025-06-18 09:43:55.685 +10:00 INF] Initializing FastReport...
[2025-06-18 09:43:55.754 +10:00 INF] FastReport initialized successfully
[2025-06-18 09:43:56.163 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-18 09:43:57.511 +10:00 INF] Starting LEP Invoicer at "2025-06-18T09:43:57.5107149+10:00"
[2025-06-18 09:43:57.515 +10:00 INF] Initializing database service...
[2025-06-18 09:43:57.518 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-18 09:43:57.637 +10:00 INF] Database connection established successfully
[2025-06-18 09:43:57.638 +10:00 INF] Database service initialized successfully
[2025-06-18 09:43:57.641 +10:00 INF] Checking for pending work...
[2025-06-18 09:43:57.644 +10:00 INF] Getting 500 orders to invoice
[2025-06-18 09:43:58.529 +10:00 INF] Found 0 orders to invoice (filtered 16 candidates)
[2025-06-18 09:43:58.532 +10:00 INF] Getting 100 credits to invoice
[2025-06-18 09:43:58.546 +10:00 INF] Found 0 credits to invoice
[2025-06-18 09:43:58.549 +10:00 INF] Getting 100 refunds to invoice
[2025-06-18 09:43:58.553 +10:00 INF] Found 0 refunds to invoice
[2025-06-18 09:43:58.554 +10:00 INF] No pending work found
[2025-06-18 09:43:58.561 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-18 09:43:58.574 +10:00 INF] Cleaning up invoicer logs...
[2025-06-18 09:43:58.655 +10:00 INF] LEP Invoicer completed successfully in 1144ms. No work to process.
[2025-06-18 09:43:58.662 +10:00 INF] Database connection disposed
[2025-06-18 09:43:58.664 +10:00 INF] LEP Invoicer completed with result: 0
