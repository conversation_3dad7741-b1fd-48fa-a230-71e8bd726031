[2025-06-17 15:46:46.595 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 15:46:46.622 +10:00 INF] Initializing FastReport...
[2025-06-17 15:46:46.704 +10:00 INF] FastReport initialized successfully
[2025-06-17 15:46:47.217 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 15:46:48.878 +10:00 INF] Starting LEP Invoicer at "2025-06-17T15:46:48.8781616+10:00"
[2025-06-17 15:46:48.882 +10:00 INF] Initializing database service...
[2025-06-17 15:46:48.884 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 15:46:49.003 +10:00 INF] Database connection established successfully
[2025-06-17 15:46:49.005 +10:00 INF] Database service initialized successfully
[2025-06-17 15:46:49.008 +10:00 INF] Checking for pending work...
[2025-06-17 15:46:49.011 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 15:46:49.936 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 15:46:49.939 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 15:46:49.954 +10:00 INF] Found 0 credits to invoice
[2025-06-17 15:46:49.956 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 15:46:49.960 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 15:46:49.962 +10:00 INF] No pending work found
[2025-06-17 15:46:49.963 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 15:46:49.965 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 15:46:50.053 +10:00 INF] LEP Invoicer completed successfully in 1174ms. No work to process.
[2025-06-17 15:46:50.061 +10:00 INF] Database connection disposed
[2025-06-17 15:46:50.063 +10:00 INF] LEP Invoicer completed with result: 0
