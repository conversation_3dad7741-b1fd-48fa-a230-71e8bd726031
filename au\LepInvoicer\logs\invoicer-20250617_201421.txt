[2025-06-17 20:14:21.585 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 20:14:21.613 +10:00 INF] Initializing FastReport...
[2025-06-17 20:14:21.682 +10:00 INF] FastReport initialized successfully
[2025-06-17 20:14:22.055 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 20:14:23.264 +10:00 INF] Starting LEP Invoicer at "2025-06-17T20:14:23.2643893+10:00"
[2025-06-17 20:14:23.268 +10:00 INF] Initializing database service...
[2025-06-17 20:14:23.270 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 20:14:23.371 +10:00 INF] Database connection established successfully
[2025-06-17 20:14:23.372 +10:00 INF] Database service initialized successfully
[2025-06-17 20:14:23.375 +10:00 INF] Checking for pending work...
[2025-06-17 20:14:23.378 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 20:14:24.230 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 20:14:24.233 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 20:14:24.246 +10:00 INF] Found 0 credits to invoice
[2025-06-17 20:14:24.249 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 20:14:24.254 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 20:14:24.256 +10:00 INF] No pending work found
[2025-06-17 20:14:24.257 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 20:14:24.267 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 20:14:24.354 +10:00 INF] LEP Invoicer completed successfully in 1090ms. No work to process.
[2025-06-17 20:14:24.361 +10:00 INF] Database connection disposed
[2025-06-17 20:14:24.363 +10:00 INF] LEP Invoicer completed with result: 0
