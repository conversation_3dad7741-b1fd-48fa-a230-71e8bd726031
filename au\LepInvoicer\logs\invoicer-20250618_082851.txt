[2025-06-18 08:28:51.427 +10:00 INF] Starting LEP Invoicer application
[2025-06-18 08:28:51.454 +10:00 INF] Initializing FastReport...
[2025-06-18 08:28:51.528 +10:00 INF] FastReport initialized successfully
[2025-06-18 08:28:51.949 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-18 08:28:53.242 +10:00 INF] Starting LEP Invoicer at "2025-06-18T08:28:53.2418949+10:00"
[2025-06-18 08:28:53.245 +10:00 INF] Initializing database service...
[2025-06-18 08:28:53.248 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-18 08:28:53.351 +10:00 INF] Database connection established successfully
[2025-06-18 08:28:53.352 +10:00 INF] Database service initialized successfully
[2025-06-18 08:28:53.355 +10:00 INF] Checking for pending work...
[2025-06-18 08:28:53.358 +10:00 INF] Getting 500 orders to invoice
[2025-06-18 08:28:54.294 +10:00 INF] Found 0 orders to invoice (filtered 16 candidates)
[2025-06-18 08:28:54.296 +10:00 INF] Getting 100 credits to invoice
[2025-06-18 08:28:54.310 +10:00 INF] Found 0 credits to invoice
[2025-06-18 08:28:54.312 +10:00 INF] Getting 100 refunds to invoice
[2025-06-18 08:28:54.326 +10:00 INF] Found 0 refunds to invoice
[2025-06-18 08:28:54.328 +10:00 INF] No pending work found
[2025-06-18 08:28:54.330 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-18 08:28:54.331 +10:00 INF] Cleaning up invoicer logs...
[2025-06-18 08:28:54.418 +10:00 INF] LEP Invoicer completed successfully in 1176ms. No work to process.
[2025-06-18 08:28:54.424 +10:00 INF] Database connection disposed
[2025-06-18 08:28:54.425 +10:00 INF] LEP Invoicer completed with result: 0
