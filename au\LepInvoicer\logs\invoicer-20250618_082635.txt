[2025-06-18 08:26:35.659 +10:00 INF] Starting LEP Invoicer application
[2025-06-18 08:26:35.699 +10:00 INF] Initializing FastReport...
[2025-06-18 08:26:35.794 +10:00 INF] FastReport initialized successfully
[2025-06-18 08:26:36.228 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-18 08:26:37.480 +10:00 INF] Starting LEP Invoicer at "2025-06-18T08:26:37.4799650+10:00"
[2025-06-18 08:26:37.483 +10:00 INF] Initializing database service...
[2025-06-18 08:26:37.486 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-18 08:26:37.608 +10:00 INF] Database connection established successfully
[2025-06-18 08:26:37.612 +10:00 INF] Database service initialized successfully
[2025-06-18 08:26:37.616 +10:00 INF] Checking for pending work...
[2025-06-18 08:26:37.622 +10:00 INF] Getting 500 orders to invoice
[2025-06-18 08:26:38.544 +10:00 INF] Found 0 orders to invoice (filtered 16 candidates)
[2025-06-18 08:26:38.549 +10:00 INF] Getting 100 credits to invoice
[2025-06-18 08:26:38.563 +10:00 INF] Found 0 credits to invoice
[2025-06-18 08:26:38.567 +10:00 INF] Getting 100 refunds to invoice
[2025-06-18 08:26:38.571 +10:00 INF] Found 0 refunds to invoice
[2025-06-18 08:26:38.572 +10:00 INF] No pending work found
[2025-06-18 08:26:38.574 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-18 08:26:38.577 +10:00 INF] Cleaning up invoicer logs...
[2025-06-18 08:26:38.678 +10:00 INF] LEP Invoicer completed successfully in 1198ms. No work to process.
[2025-06-18 08:26:38.684 +10:00 INF] Database connection disposed
[2025-06-18 08:26:38.690 +10:00 INF] LEP Invoicer completed with result: 0
