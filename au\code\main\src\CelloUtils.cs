using lep.job;
using lep.run;
using JC = lep.job.JobCelloglazeOptions;
using RC = lep.run.RunCelloglazeOptions;
using System;
using System.Collections.Generic;

namespace lep
{

	public class JobOption
	{
		
	}


    public class CelloUtils
    {
        public static JobCelloglazeOptions[] ToJobCelloGlaze(RunCelloglazeOptions runCello)
        {
            JobCelloglazeOptions[] result = new[] { JC.None, JC.None };
            switch (runCello)
            {
                case RC.None:
                    result = new[] { JC.None, JC.None };
                    break;

                case RC.GlossFront:
                    result = new[] { JC.Gloss, JC.None };
                    break;

                case RC.GlossBoth:
                    result = new[] { JC.Gloss, JC.Gloss };
                    break;

				case RC.GlossFrontMattBack:
					result = new[] { JC.Gloss, JC.Matt };
					break;

				case RC.MattFront:
                    result = new[] { JC.<PERSON>, JC.None };
                    break;

                case RC.MattBoth:
                    result = new[] { J<PERSON><PERSON><PERSON>, J<PERSON><PERSON><PERSON> };
                    break;

                case RC.VelvetFront:
                    result = new[] { J<PERSON>.<PERSON>, JC.None };
                    break;

                case RC.VelvetBoth:
                    result = new[] { J<PERSON>.Velvet, JC.Velvet };
                    break;

                case RC.FoilFrontMattBoth:
                    result = new[] { JC.Foil, JC.Matt };
                    break;

                case RC.EmbossGlossFront:
                    result = new[] { JC.EmbossedGlossFront, JC.None };
                    break;

                case RC.EmbossMattFront:
                    result = new[] { JC.EmbossedMattFront, JC.None };
                    break;

                case RC.SpotUVFrontMattBoth:
                    result = new[] { JC.SpotUV, JC.Matt };
                    break;

				case RC.SpotUVFrontMattFront:
					result = new[] { JC.SpotUVFrontMattFront, JC.None };
					break;

				case RC.MattAntiScuffFront:
					result = new[] { JC.MattAntiScuff, JC.None };
					break;

				case RC.MattAntiScuffBoth:
					result = new[] { JC.MattAntiScuff, JC.MattAntiScuff };
					break;

			}

            return result;
        }

        public static string GetCelloName(JobCelloglazeOptions celloFront, JobCelloglazeOptions celloBack, bool isNormal)
        {
            if (celloFront == JC.None && celloBack == JC.None)
            {
                return "None";
            }
            else if (celloFront == JC.Gloss && celloBack == JC.None)
            {
                return isNormal ? "Gloss Cello front only" : "Gloss Cello Outside only";
            }
            else if (celloFront == JC.Matt && celloBack == JC.None)
            {
                return isNormal ? "Matt Cello front only" : "Matt Cello Outside only";
            }
            else if (celloFront == JC.Gloss && celloBack == JC.Gloss)
            {
                return isNormal ? "Gloss Cello front & back" : "Gloss Cello Outside & Inside";
            }
            else if (celloFront == JC.Matt && celloBack == JC.Matt)
            {
                return isNormal ? "Matt Cello front & back" : "Matt Cello Outside & Inside";
            }
            else if (celloFront == JC.Gloss && celloBack == JC.Matt)
            {
                return isNormal ? "Gloss Cello front, Matt Cello back" : "Gloss Cello outside, Matt Cello inside";
            }
            else if (celloFront == JC.Velvet && celloBack == JC.None)
            {
                return "Velvet Cello front only";
            }
            else if (celloFront == JC.Velvet && celloBack == JC.Velvet)
            {
                return "Velvet Cello front & back";
            }
            else if (celloFront == JC.Emboss && celloBack == JC.Gloss)
            {
                return "Embossed front, Gloss Cello back";
            }
            else if (celloFront == JC.Foil && celloBack == JC.Matt)
            {
                return "Foil front, Matt Cello both sides";
            }
            else if (celloFront == JC.SpotUV && celloBack == JC.Matt)
            {
                return "Spot UV front, Matt Cello both sides";
            }
			else if (celloFront == JC.SpotUVFrontMattFront && celloBack == JC.None)
			{
				return "Spot UV front, Matt Cello outside";
			}
			else if (celloFront == JC.Emboss && celloBack == JC.None)
            {
                return "Emboss front only";
            }
            else if (celloFront == JC.Foil && celloBack == JC.None)
            {
                return "Foil front only";
            }
            else if (celloFront == JC.EmbossFoil && celloBack == JC.None)
            {
                return "Emboss & Foil front only";
            }
            else if (celloFront == JC.EmbossedGlossFront && celloBack == JC.None)
            {
                return "Embossed front, Gloss Cello front";
            }
            else if (celloFront == JC.EmbossedMattFront && celloBack == JC.None)
            {
                return "Embossed front, Matt Cello front";
            }
			else if (celloFront == JC.MattAntiScuff && celloBack == JC.None)
			{
				return "Matt AntiScuff front only";
			}
			else if (celloFront == JC.MattAntiScuff && celloBack == JC.MattAntiScuff)
			{
				return "Matt AntiScuff front & back";
			}
            else
            {
                return celloFront.ToString() + ", " + celloBack.ToString();
            }
        }

 
        public static Dictionary<string, string> GetAllPossibleCelloOptions(bool isNormal = true)
            {
                var celloOptions = new Dictionary<string, string>();
                foreach (RunCelloglazeOptions runCello in Enum.GetValues(typeof(RunCelloglazeOptions)))
                {
                    JobCelloglazeOptions[] jobCello = ToJobCelloGlaze(runCello);
					string key = ((int)jobCello[0]).ToString() + "/" + ((int)jobCello[1]).ToString();

					string name = GetCelloName(jobCello[0], jobCello[1], isNormal);
                    celloOptions[key] = name;
                }
                return celloOptions;
            }

        


        //public ICelloOption GetCelloOption(string name)
        //{
        //	switch (name) {
        //		case "Gloss front only":
        //		case "Gloss Outside only":
        //		case "Gloss Cello front only":
        //		case "Gloss Cello Outside only":
        //			return new CelloOption(jc.Gloss, jc.None);

        //		case "Matt front only":
        //		case "Matt Outside only":
        //		case "Matt Cello front only":
        //		case "Matt Cello Outside only":
        //			return new CelloOption(jc.Matt, jc.None);

        //		case "Gloss front & back":
        //		case "Gloss Outside & Inside":
        //		case "Gloss Cello front & back":
        //		case "Gloss Cello Outside & Inside":
        //			return new CelloOption(jc.Gloss, jc.Gloss);
        //		case "Matt front & back":
        //		case "Matt Outside & Inside":
        //			return new CelloOption(jc.Matt, jc.Matt);
        //		//MikeGreenan adding new cello type
        //		case "Gloss front, Matt back":
        //		case "Gloss outer, Matt inner":
        //			return new CelloOption(jc.Gloss, jc.Matt);

        //		case "Velvet front only":
        //			return new CelloOption(jc.Velvet, jc.None);
        //		case "Velvet front & back":
        //			return new CelloOption(jc.Velvet, jc.Velvet);

        //		case "Embossed front, Gloss back":
        //			return new CelloOption(jc.Emboss, jc.Gloss);
        //		case "Foil front, Matt both sides":
        //			return new CelloOption(jc.Foil, jc.Matt);

        //		case "Spot UV front, Matt both sides":
        //			return new CelloOption(jc.SpotUV, jc.Matt);
        //		case "Emboss front only":
        //			return new CelloOption(jc.Emboss, jc.None);
        //		case "Foil front only":
        //			return new CelloOption(jc.Foil, jc.None);

        //		case "Emboss & Foil front only":
        //			return new CelloOption(jc.EmbossFoil, jc.None);

        //		case "Embossed front, Gloss front":
        //			return new CelloOption(jc.EmbossedGlossFront, jc.None);
        //		case "Embossed front, Matt front":
        //			return new CelloOption(jc.EmbossedMattFront, jc.None);

        //		default:
        //			return new CelloOption(jc.None, jc.None);
        //	}
        //}
    }
}
