[2025-06-17 18:03:20.595 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 18:03:20.691 +10:00 INF] Initializing FastReport...
[2025-06-17 18:03:20.993 +10:00 INF] FastReport initialized successfully
[2025-06-17 18:03:22.046 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 18:03:24.456 +10:00 INF] Starting LEP Invoicer at "2025-06-17T18:03:24.4557306+10:00"
[2025-06-17 18:03:24.461 +10:00 INF] Initializing database service...
[2025-06-17 18:03:24.513 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 18:03:24.637 +10:00 INF] Database connection established successfully
[2025-06-17 18:03:24.640 +10:00 INF] Database service initialized successfully
[2025-06-17 18:03:24.643 +10:00 INF] Checking for pending work...
[2025-06-17 18:03:24.646 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 18:03:25.604 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 18:03:25.607 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 18:03:25.619 +10:00 INF] Found 0 credits to invoice
[2025-06-17 18:03:25.621 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 18:03:25.624 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 18:03:25.626 +10:00 INF] No pending work found
[2025-06-17 18:03:25.643 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 18:03:25.647 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 18:03:25.743 +10:00 INF] LEP Invoicer completed successfully in 1288ms. No work to process.
[2025-06-17 18:03:25.751 +10:00 INF] Database connection disposed
[2025-06-17 18:03:25.754 +10:00 INF] LEP Invoicer completed with result: 0
