[2025-06-18 09:45:01.659 +10:00 INF] Starting LEP Invoicer application
[2025-06-18 09:45:01.690 +10:00 INF] Initializing FastReport...
[2025-06-18 09:45:01.770 +10:00 INF] FastReport initialized successfully
[2025-06-18 09:45:02.232 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-18 09:45:03.719 +10:00 INF] Starting LEP Invoicer at "2025-06-18T09:45:03.7195779+10:00"
[2025-06-18 09:45:03.723 +10:00 INF] Initializing database service...
[2025-06-18 09:45:03.726 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-18 09:45:03.847 +10:00 INF] Database connection established successfully
[2025-06-18 09:45:03.848 +10:00 INF] Database service initialized successfully
[2025-06-18 09:45:03.850 +10:00 INF] Checking for pending work...
[2025-06-18 09:45:03.853 +10:00 INF] Getting 500 orders to invoice
[2025-06-18 09:45:04.722 +10:00 INF] Found 0 orders to invoice (filtered 16 candidates)
[2025-06-18 09:45:04.725 +10:00 INF] Getting 100 credits to invoice
[2025-06-18 09:45:04.737 +10:00 INF] Found 0 credits to invoice
[2025-06-18 09:45:04.739 +10:00 INF] Getting 100 refunds to invoice
[2025-06-18 09:45:04.742 +10:00 INF] Found 0 refunds to invoice
[2025-06-18 09:45:04.744 +10:00 INF] No pending work found
[2025-06-18 09:45:04.745 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-18 09:45:04.746 +10:00 INF] Cleaning up invoicer logs...
[2025-06-18 09:45:04.824 +10:00 INF] LEP Invoicer completed successfully in 1104ms. No work to process.
[2025-06-18 09:45:04.830 +10:00 INF] Database connection disposed
[2025-06-18 09:45:04.832 +10:00 INF] LEP Invoicer completed with result: 0
