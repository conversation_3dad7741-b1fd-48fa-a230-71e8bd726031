[2025-06-17 19:16:29.607 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 19:16:29.637 +10:00 INF] Initializing FastReport...
[2025-06-17 19:16:29.712 +10:00 INF] FastReport initialized successfully
[2025-06-17 19:16:30.133 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 19:16:31.372 +10:00 INF] Starting LEP Invoicer at "2025-06-17T19:16:31.3719646+10:00"
[2025-06-17 19:16:31.375 +10:00 INF] Initializing database service...
[2025-06-17 19:16:31.378 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 19:16:31.477 +10:00 INF] Database connection established successfully
[2025-06-17 19:16:31.479 +10:00 INF] Database service initialized successfully
[2025-06-17 19:16:31.481 +10:00 INF] Checking for pending work...
[2025-06-17 19:16:31.484 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 19:16:32.379 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 19:16:32.382 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 19:16:32.400 +10:00 INF] Found 0 credits to invoice
[2025-06-17 19:16:32.402 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 19:16:32.406 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 19:16:32.408 +10:00 INF] No pending work found
[2025-06-17 19:16:32.409 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 19:16:32.413 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 19:16:32.498 +10:00 INF] LEP Invoicer completed successfully in 1126ms. No work to process.
[2025-06-17 19:16:32.505 +10:00 INF] Database connection disposed
[2025-06-17 19:16:32.507 +10:00 INF] LEP Invoicer completed with result: 0
