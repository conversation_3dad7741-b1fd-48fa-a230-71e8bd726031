[2025-06-17 20:00:08.571 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 20:00:08.597 +10:00 INF] Initializing FastReport...
[2025-06-17 20:00:08.670 +10:00 INF] FastReport initialized successfully
[2025-06-17 20:00:09.091 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 20:00:10.333 +10:00 INF] Starting LEP Invoicer at "2025-06-17T20:00:10.3331278+10:00"
[2025-06-17 20:00:10.337 +10:00 INF] Initializing database service...
[2025-06-17 20:00:10.341 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 20:00:10.451 +10:00 INF] Database connection established successfully
[2025-06-17 20:00:10.452 +10:00 INF] Database service initialized successfully
[2025-06-17 20:00:10.455 +10:00 INF] Checking for pending work...
[2025-06-17 20:00:10.459 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 20:00:11.329 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 20:00:11.332 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 20:00:11.345 +10:00 INF] Found 0 credits to invoice
[2025-06-17 20:00:11.347 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 20:00:11.352 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 20:00:11.353 +10:00 INF] No pending work found
[2025-06-17 20:00:11.355 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 20:00:11.356 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 20:00:11.436 +10:00 INF] LEP Invoicer completed successfully in 1103ms. No work to process.
[2025-06-17 20:00:11.442 +10:00 INF] Database connection disposed
[2025-06-17 20:00:11.444 +10:00 INF] LEP Invoicer completed with result: 0
