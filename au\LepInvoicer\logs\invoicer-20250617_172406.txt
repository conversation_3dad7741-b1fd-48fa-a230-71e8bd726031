[2025-06-17 17:24:06.514 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 17:24:06.549 +10:00 INF] Initializing FastReport...
[2025-06-17 17:24:06.625 +10:00 INF] FastReport initialized successfully
[2025-06-17 17:24:07.003 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 17:24:08.195 +10:00 INF] Starting LEP Invoicer at "2025-06-17T17:24:08.1954417+10:00"
[2025-06-17 17:24:08.199 +10:00 INF] Initializing database service...
[2025-06-17 17:24:08.203 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 17:24:08.301 +10:00 INF] Database connection established successfully
[2025-06-17 17:24:08.302 +10:00 INF] Database service initialized successfully
[2025-06-17 17:24:08.305 +10:00 INF] Checking for pending work...
[2025-06-17 17:24:08.307 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 17:24:09.168 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 17:24:09.172 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 17:24:09.185 +10:00 INF] Found 0 credits to invoice
[2025-06-17 17:24:09.187 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 17:24:09.191 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 17:24:09.193 +10:00 INF] No pending work found
[2025-06-17 17:24:09.194 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 17:24:09.196 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 17:24:09.280 +10:00 INF] LEP Invoicer completed successfully in 1084ms. No work to process.
[2025-06-17 17:24:09.287 +10:00 INF] Database connection disposed
[2025-06-17 17:24:09.289 +10:00 INF] LEP Invoicer completed with result: 0
