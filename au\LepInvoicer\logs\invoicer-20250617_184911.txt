[2025-06-17 18:49:11.593 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 18:49:11.620 +10:00 INF] Initializing FastReport...
[2025-06-17 18:49:11.714 +10:00 INF] FastReport initialized successfully
[2025-06-17 18:49:12.181 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 18:49:13.379 +10:00 INF] Starting LEP Invoicer at "2025-06-17T18:49:13.3792545+10:00"
[2025-06-17 18:49:13.383 +10:00 INF] Initializing database service...
[2025-06-17 18:49:13.386 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 18:49:13.483 +10:00 INF] Database connection established successfully
[2025-06-17 18:49:13.484 +10:00 INF] Database service initialized successfully
[2025-06-17 18:49:13.487 +10:00 INF] Checking for pending work...
[2025-06-17 18:49:13.489 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 18:49:14.347 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 18:49:14.350 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 18:49:14.362 +10:00 INF] Found 0 credits to invoice
[2025-06-17 18:49:14.370 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 18:49:14.375 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 18:49:14.377 +10:00 INF] No pending work found
[2025-06-17 18:49:14.379 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 18:49:14.380 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 18:49:14.456 +10:00 INF] LEP Invoicer completed successfully in 1076ms. No work to process.
[2025-06-17 18:49:14.462 +10:00 INF] Database connection disposed
[2025-06-17 18:49:14.464 +10:00 INF] LEP Invoicer completed with result: 0
