using lep.job;
using lep.run;
using System;

namespace lep.user.impl
{
	[Serializable]
	public class OrderSearchCriteria : IOrderSearchCriteria
	{
		public OrderSearchCriteria()
		{
		}

		public virtual string SortField { get; set; }
		public virtual string SortDir { get; set; }

		public virtual int Id { get; set; }
		public virtual bool IsSearchPanelOpen { get; set; }
		public virtual OrderStatusOptions? OrderStatus { get; set; }
		public virtual JobTypeOptions? JobType { get; set; }
		public virtual RunCelloglazeOptions? Celloglaze { get; set; }
		public virtual IPaperSize Size { get; set; }
		public virtual IStock Stock { get; set; }
		public virtual string Customer { get; set; }
		public virtual string OrderNr { get; set; }
		public virtual string JobNr { get; set; }
		public virtual bool IsWaitingApproval { get; set; }
		public virtual bool IsWithdraw { get; set; }
		public virtual bool IsNewOrder { get; set; }
		public virtual bool IsOnlyUrgentOrder { get; set; }
		public virtual bool IsOnPrepay { get; set; }
		public virtual bool IsOnhold { get; set; }
		public virtual bool IsAwaitingPayment { get; set; }
		public virtual bool IsQuoteRequired { get; set; }
		public virtual bool IsRejected { get; set; }
		public virtual bool IsCorrectedOrder { get; set; }
		public virtual bool IsOpenOrder { get; set; }
		public virtual bool IsNonBusinessCard { get; set; }
		public virtual bool IsPaidFor { get; set; }
		public virtual int Page { get; set; }
		public virtual string Ordering { get; set; }
		public virtual IUser Staff { get; set; }
		public virtual bool IsOrderWithDigitalJob { get; set; }
		public virtual bool IsOrderWithOutworkJob { get; set; }
		public virtual bool HideOnHoldOrders { get; set; }
		public virtual bool ShowOnlyOnHoldOrders { get; set; }
	}
}
