[2025-06-10 19:54:39.454 +10:00 INF] Starting LEP Invoicer application
[2025-06-10 19:54:40.182 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-10 19:54:42.478 +10:00 INF] Starting LEP Invoicer at "2025-06-10T19:54:42.4782826+10:00"
[2025-06-10 19:54:42.483 +10:00 INF] Initializing database service...
[2025-06-10 19:54:42.488 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-10 19:54:42.650 +10:00 INF] Database connection established successfully
[2025-06-10 19:54:42.652 +10:00 INF] Database service initialized successfully
[2025-06-10 19:54:42.656 +10:00 INF] Checking for pending work...
[2025-06-10 19:54:42.660 +10:00 INF] Getting 500 orders to invoice
[2025-06-10 19:54:43.659 +10:00 INF] Found 2 orders to invoice
[2025-06-10 19:54:43.664 +10:00 INF] Getting 100 credits to invoice
[2025-06-10 19:54:43.711 +10:00 INF] Found 0 credits to invoice
[2025-06-10 19:54:43.715 +10:00 INF] Getting 100 refunds to invoice
[2025-06-10 19:54:43.730 +10:00 INF] Found 0 refunds to invoice
[2025-06-10 19:54:43.732 +10:00 INF] Found pending work: 2 orders
[2025-06-10 19:54:43.736 +10:00 INF] Initializing MYOB and other services...
[2025-06-10 19:54:43.740 +10:00 INF] Initializing MYOB service...
[2025-06-10 19:54:43.744 +10:00 INF] Initializing MYOB with OAuth authentication
[2025-06-10 19:54:43.748 +10:00 INF] Starting OAuth authentication flow
[2025-06-10 19:54:43.860 +10:00 INF] Using existing OAuth tokens
[2025-06-10 19:54:43.862 +10:00 INF] Initializing MYOB services with OAuth keystore
[2025-06-10 19:54:43.867 +10:00 INF] MYOB services initialized successfully
[2025-06-10 19:54:43.869 +10:00 INF] OAuth keystore set for API calls
[2025-06-10 19:54:43.871 +10:00 INF] Getting company files from MYOB
[2025-06-10 19:54:44.199 +10:00 INF] MYOB service initialized with OAuth credentials. Using company file: LEP Colour Printers Pty Ltd
[2025-06-10 19:54:44.211 +10:00 INF] Current cache stats: {"AccountsCount":269,"TaxCodesCount":18,"CustomersCount":393,"LastCacheLoad":"2025-06-10T19:31:07.5727667+10:00","CacheAge":"00:23:36.6329956","IsExpired":false,"CacheDirectory":"C:\\Users\\<USER>\\AppData\\Local\\LepInvoicer\\cache"}
[2025-06-10 19:54:44.234 +10:00 INF] Using cached MYOB data: {"AccountsCount":269,"TaxCodesCount":18,"CustomersCount":393,"LastCacheLoad":"2025-06-10T19:31:07.5727667+10:00","CacheAge":"00:23:36.6329956","IsExpired":false,"CacheDirectory":"C:\\Users\\<USER>\\AppData\\Local\\LepInvoicer\\cache"}
[2025-06-10 19:54:45.425 +10:00 INF] Using cached GST tax code
[2025-06-10 19:54:45.427 +10:00 INF] Using cached freight account
[2025-06-10 19:54:45.431 +10:00 INF] Using cached discounts account
[2025-06-10 19:54:45.433 +10:00 INF] MYOB cache initialization complete: {"AccountsCount":269,"TaxCodesCount":18,"CustomersCount":393,"LastCacheLoad":"2025-06-10T19:31:07.5727667+10:00","CacheAge":"00:23:37.8607671","IsExpired":false,"CacheDirectory":"C:\\Users\\<USER>\\AppData\\Local\\LepInvoicer\\cache"}
[2025-06-10 19:54:45.450 +10:00 INF] MYOB service initialized successfully with company file: LEP Colour Printers Pty Ltd
[2025-06-10 19:54:45.452 +10:00 INF] All services initialized successfully
[2025-06-10 19:54:45.456 +10:00 INF] Processing order invoices...
[2025-06-10 19:54:45.458 +10:00 INF] Getting 500 orders to invoice
[2025-06-10 19:54:45.776 +10:00 INF] Found 2 orders to invoice
[2025-06-10 19:54:45.778 +10:00 INF] Found 2 orders to process
[2025-06-10 19:54:45.781 +10:00 INF] Getting order 1417006
[2025-06-10 19:54:46.129 +10:00 INF] Processing order 1417006 with 1 jobs, total: ¤0.00
[2025-06-10 19:54:46.136 +10:00 WRN] Order 1417006 has zero price, marking as invoiced
[2025-06-10 19:54:46.139 +10:00 INF] Getting order 1416838
[2025-06-10 19:54:46.149 +10:00 INF] Processing order 1416838 with 1 jobs, total: ¤0.00
[2025-06-10 19:54:46.154 +10:00 WRN] Order 1416838 has zero price, marking as invoiced
[2025-06-10 19:54:46.161 +10:00 INF] Order processing completed. Processed: 2, Success: 0, Failed: 2
[2025-06-10 19:54:46.167 +10:00 INF] Processing credit invoices...
[2025-06-10 19:54:46.169 +10:00 INF] Getting 100 credits to invoice
[2025-06-10 19:54:46.178 +10:00 INF] Found 0 credits to invoice
[2025-06-10 19:54:46.185 +10:00 INF] Processing refund invoices...
[2025-06-10 19:54:46.187 +10:00 INF] Getting 100 refunds to invoice
[2025-06-10 19:54:46.195 +10:00 INF] Found 0 refunds to invoice
[2025-06-10 19:54:46.200 +10:00 INF] Cleaning up invoicer logs...
[2025-06-10 19:54:46.276 +10:00 INF] LEP Invoicer completed successfully in 3798ms. Orders: 2, Credits: 0, Refunds: 0
[2025-06-10 19:54:46.289 +10:00 INF] Database connection disposed
[2025-06-10 19:54:46.292 +10:00 INF] LEP Invoicer completed with result: 0
