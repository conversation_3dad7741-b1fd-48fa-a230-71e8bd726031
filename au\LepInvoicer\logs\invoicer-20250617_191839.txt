[2025-06-17 19:18:39.623 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 19:18:39.652 +10:00 INF] Initializing FastReport...
[2025-06-17 19:18:39.727 +10:00 INF] FastReport initialized successfully
[2025-06-17 19:18:40.189 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 19:18:41.456 +10:00 INF] Starting LEP Invoicer at "2025-06-17T19:18:41.4566109+10:00"
[2025-06-17 19:18:41.460 +10:00 INF] Initializing database service...
[2025-06-17 19:18:41.462 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 19:18:41.563 +10:00 INF] Database connection established successfully
[2025-06-17 19:18:41.565 +10:00 INF] Database service initialized successfully
[2025-06-17 19:18:41.568 +10:00 INF] Checking for pending work...
[2025-06-17 19:18:41.571 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 19:18:42.513 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 19:18:42.516 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 19:18:42.528 +10:00 INF] Found 0 credits to invoice
[2025-06-17 19:18:42.530 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 19:18:42.536 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 19:18:42.539 +10:00 INF] No pending work found
[2025-06-17 19:18:42.540 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 19:18:42.542 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 19:18:42.632 +10:00 INF] LEP Invoicer completed successfully in 1175ms. No work to process.
[2025-06-17 19:18:42.638 +10:00 INF] Database connection disposed
[2025-06-17 19:18:42.640 +10:00 INF] LEP Invoicer completed with result: 0
