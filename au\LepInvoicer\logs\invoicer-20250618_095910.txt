[2025-06-18 09:59:10.576 +10:00 INF] Starting LEP Invoicer application
[2025-06-18 09:59:10.619 +10:00 INF] Initializing FastReport...
[2025-06-18 09:59:10.720 +10:00 INF] FastReport initialized successfully
[2025-06-18 09:59:11.205 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-18 09:59:12.573 +10:00 INF] Starting LEP Invoicer at "2025-06-18T09:59:12.5727282+10:00"
[2025-06-18 09:59:12.577 +10:00 INF] Initializing database service...
[2025-06-18 09:59:12.589 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-18 09:59:12.689 +10:00 INF] Database connection established successfully
[2025-06-18 09:59:12.691 +10:00 INF] Database service initialized successfully
[2025-06-18 09:59:12.693 +10:00 INF] Checking for pending work...
[2025-06-18 09:59:12.696 +10:00 INF] Getting 500 orders to invoice
[2025-06-18 09:59:13.592 +10:00 INF] Found 0 orders to invoice (filtered 16 candidates)
[2025-06-18 09:59:13.595 +10:00 INF] Getting 100 credits to invoice
[2025-06-18 09:59:13.612 +10:00 INF] Found 0 credits to invoice
[2025-06-18 09:59:13.615 +10:00 INF] Getting 100 refunds to invoice
[2025-06-18 09:59:13.620 +10:00 INF] Found 0 refunds to invoice
[2025-06-18 09:59:13.624 +10:00 INF] No pending work found
[2025-06-18 09:59:13.626 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-18 09:59:13.627 +10:00 INF] Cleaning up invoicer logs...
[2025-06-18 09:59:13.724 +10:00 INF] LEP Invoicer completed successfully in 1152ms. No work to process.
[2025-06-18 09:59:13.731 +10:00 INF] Database connection disposed
[2025-06-18 09:59:13.737 +10:00 INF] LEP Invoicer completed with result: 0
