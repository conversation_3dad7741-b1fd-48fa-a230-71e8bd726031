[2025-06-17 19:32:50.536 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 19:32:50.567 +10:00 INF] Initializing FastReport...
[2025-06-17 19:32:50.653 +10:00 INF] FastReport initialized successfully
[2025-06-17 19:32:51.095 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 19:32:52.413 +10:00 INF] Starting LEP Invoicer at "2025-06-17T19:32:52.4132845+10:00"
[2025-06-17 19:32:52.417 +10:00 INF] Initializing database service...
[2025-06-17 19:32:52.419 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 19:32:52.517 +10:00 INF] Database connection established successfully
[2025-06-17 19:32:52.518 +10:00 INF] Database service initialized successfully
[2025-06-17 19:32:52.521 +10:00 INF] Checking for pending work...
[2025-06-17 19:32:52.523 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 19:32:53.447 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 19:32:53.449 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 19:32:53.462 +10:00 INF] Found 0 credits to invoice
[2025-06-17 19:32:53.464 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 19:32:53.468 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 19:32:53.469 +10:00 INF] No pending work found
[2025-06-17 19:32:53.470 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 19:32:53.474 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 19:32:53.547 +10:00 INF] LEP Invoicer completed successfully in 1133ms. No work to process.
[2025-06-17 19:32:53.555 +10:00 INF] Database connection disposed
[2025-06-17 19:32:53.557 +10:00 INF] LEP Invoicer completed with result: 0
