[2025-06-17 18:38:17.758 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 18:38:17.786 +10:00 INF] Initializing FastReport...
[2025-06-17 18:38:17.865 +10:00 INF] FastReport initialized successfully
[2025-06-17 18:38:18.330 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 18:38:19.649 +10:00 INF] Starting LEP Invoicer at "2025-06-17T18:38:19.6495875+10:00"
[2025-06-17 18:38:19.653 +10:00 INF] Initializing database service...
[2025-06-17 18:38:19.656 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 18:38:19.765 +10:00 INF] Database connection established successfully
[2025-06-17 18:38:19.767 +10:00 INF] Database service initialized successfully
[2025-06-17 18:38:19.769 +10:00 INF] Checking for pending work...
[2025-06-17 18:38:19.772 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 18:38:20.641 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 18:38:20.646 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 18:38:20.659 +10:00 INF] Found 0 credits to invoice
[2025-06-17 18:38:20.662 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 18:38:20.667 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 18:38:20.668 +10:00 INF] No pending work found
[2025-06-17 18:38:20.670 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 18:38:20.671 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 18:38:20.753 +10:00 INF] LEP Invoicer completed successfully in 1103ms. No work to process.
[2025-06-17 18:38:20.765 +10:00 INF] Database connection disposed
[2025-06-17 18:38:20.767 +10:00 INF] LEP Invoicer completed with result: 0
