[2025-06-17 17:06:41.428 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 17:06:41.456 +10:00 INF] Initializing FastReport...
[2025-06-17 17:06:41.532 +10:00 INF] FastReport initialized successfully
[2025-06-17 17:06:41.983 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 17:06:43.270 +10:00 INF] Starting LEP Invoicer at "2025-06-17T17:06:43.2698266+10:00"
[2025-06-17 17:06:43.273 +10:00 INF] Initializing database service...
[2025-06-17 17:06:43.276 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 17:06:43.382 +10:00 INF] Database connection established successfully
[2025-06-17 17:06:43.384 +10:00 INF] Database service initialized successfully
[2025-06-17 17:06:43.388 +10:00 INF] Checking for pending work...
[2025-06-17 17:06:43.395 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 17:06:44.278 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 17:06:44.283 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 17:06:44.296 +10:00 INF] Found 0 credits to invoice
[2025-06-17 17:06:44.298 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 17:06:44.301 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 17:06:44.302 +10:00 INF] No pending work found
[2025-06-17 17:06:44.303 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 17:06:44.304 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 17:06:44.376 +10:00 INF] LEP Invoicer completed successfully in 1106ms. No work to process.
[2025-06-17 17:06:44.382 +10:00 INF] Database connection disposed
[2025-06-17 17:06:44.384 +10:00 INF] LEP Invoicer completed with result: 0
