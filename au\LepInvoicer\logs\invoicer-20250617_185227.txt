[2025-06-17 18:52:27.618 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 18:52:27.648 +10:00 INF] Initializing FastReport...
[2025-06-17 18:52:27.727 +10:00 INF] FastReport initialized successfully
[2025-06-17 18:52:28.153 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 18:52:29.464 +10:00 INF] Starting LEP Invoicer at "2025-06-17T18:52:29.4644581+10:00"
[2025-06-17 18:52:29.469 +10:00 INF] Initializing database service...
[2025-06-17 18:52:29.472 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 18:52:29.574 +10:00 INF] Database connection established successfully
[2025-06-17 18:52:29.576 +10:00 INF] Database service initialized successfully
[2025-06-17 18:52:29.578 +10:00 INF] Checking for pending work...
[2025-06-17 18:52:29.581 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 18:52:30.454 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 18:52:30.456 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 18:52:30.468 +10:00 INF] Found 0 credits to invoice
[2025-06-17 18:52:30.471 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 18:52:30.475 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 18:52:30.494 +10:00 INF] No pending work found
[2025-06-17 18:52:30.496 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 18:52:30.498 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 18:52:30.575 +10:00 INF] LEP Invoicer completed successfully in 1110ms. No work to process.
[2025-06-17 18:52:30.582 +10:00 INF] Database connection disposed
[2025-06-17 18:52:30.594 +10:00 INF] LEP Invoicer completed with result: 0
