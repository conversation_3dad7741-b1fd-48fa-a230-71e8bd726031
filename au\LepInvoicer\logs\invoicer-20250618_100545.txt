[2025-06-18 10:05:45.515 +10:00 INF] Starting LEP Invoicer application
[2025-06-18 10:05:45.547 +10:00 INF] Initializing FastReport...
[2025-06-18 10:05:45.624 +10:00 INF] FastReport initialized successfully
[2025-06-18 10:05:46.024 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-18 10:05:47.360 +10:00 INF] Starting LEP Invoicer at "2025-06-18T10:05:47.3593246+10:00"
[2025-06-18 10:05:47.364 +10:00 INF] Initializing database service...
[2025-06-18 10:05:47.366 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-18 10:05:47.470 +10:00 INF] Database connection established successfully
[2025-06-18 10:05:47.473 +10:00 INF] Database service initialized successfully
[2025-06-18 10:05:47.475 +10:00 INF] Checking for pending work...
[2025-06-18 10:05:47.479 +10:00 INF] Getting 500 orders to invoice
[2025-06-18 10:05:48.397 +10:00 INF] Found 0 orders to invoice (filtered 16 candidates)
[2025-06-18 10:05:48.400 +10:00 INF] Getting 100 credits to invoice
[2025-06-18 10:05:48.415 +10:00 INF] Found 0 credits to invoice
[2025-06-18 10:05:48.420 +10:00 INF] Getting 100 refunds to invoice
[2025-06-18 10:05:48.427 +10:00 INF] Found 0 refunds to invoice
[2025-06-18 10:05:48.428 +10:00 INF] No pending work found
[2025-06-18 10:05:48.433 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-18 10:05:48.435 +10:00 INF] Cleaning up invoicer logs...
[2025-06-18 10:05:48.521 +10:00 INF] LEP Invoicer completed successfully in 1161ms. No work to process.
[2025-06-18 10:05:48.527 +10:00 INF] Database connection disposed
[2025-06-18 10:05:48.529 +10:00 INF] LEP Invoicer completed with result: 0
