[2025-06-17 16:39:24.508 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 16:39:24.536 +10:00 INF] Initializing FastReport...
[2025-06-17 16:39:24.606 +10:00 INF] FastReport initialized successfully
[2025-06-17 16:39:25.093 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 16:39:26.418 +10:00 INF] Starting LEP Invoicer at "2025-06-17T16:39:26.4176037+10:00"
[2025-06-17 16:39:26.423 +10:00 INF] Initializing database service...
[2025-06-17 16:39:26.425 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 16:39:26.521 +10:00 INF] Database connection established successfully
[2025-06-17 16:39:26.522 +10:00 INF] Database service initialized successfully
[2025-06-17 16:39:26.525 +10:00 INF] Checking for pending work...
[2025-06-17 16:39:26.528 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 16:39:27.398 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 16:39:27.400 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 16:39:27.413 +10:00 INF] Found 0 credits to invoice
[2025-06-17 16:39:27.416 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 16:39:27.421 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 16:39:27.422 +10:00 INF] No pending work found
[2025-06-17 16:39:27.423 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 16:39:27.424 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 16:39:27.496 +10:00 INF] LEP Invoicer completed successfully in 1078ms. No work to process.
[2025-06-17 16:39:27.502 +10:00 INF] Database connection disposed
[2025-06-17 16:39:27.504 +10:00 INF] LEP Invoicer completed with result: 0
