[2025-06-18 09:03:49.778 +10:00 INF] Starting LEP Invoicer application
[2025-06-18 09:03:49.807 +10:00 INF] Initializing FastReport...
[2025-06-18 09:03:49.879 +10:00 INF] FastReport initialized successfully
[2025-06-18 09:03:50.319 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-18 09:03:51.641 +10:00 INF] Starting LEP Invoicer at "2025-06-18T09:03:51.6410838+10:00"
[2025-06-18 09:03:51.645 +10:00 INF] Initializing database service...
[2025-06-18 09:03:51.649 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-18 09:03:51.754 +10:00 INF] Database connection established successfully
[2025-06-18 09:03:51.755 +10:00 INF] Database service initialized successfully
[2025-06-18 09:03:51.758 +10:00 INF] Checking for pending work...
[2025-06-18 09:03:51.761 +10:00 INF] Getting 500 orders to invoice
[2025-06-18 09:03:52.693 +10:00 INF] Found 0 orders to invoice (filtered 16 candidates)
[2025-06-18 09:03:52.696 +10:00 INF] Getting 100 credits to invoice
[2025-06-18 09:03:52.709 +10:00 INF] Found 0 credits to invoice
[2025-06-18 09:03:52.711 +10:00 INF] Getting 100 refunds to invoice
[2025-06-18 09:03:52.716 +10:00 INF] Found 0 refunds to invoice
[2025-06-18 09:03:52.718 +10:00 INF] No pending work found
[2025-06-18 09:03:52.719 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-18 09:03:52.720 +10:00 INF] Cleaning up invoicer logs...
[2025-06-18 09:03:52.811 +10:00 INF] LEP Invoicer completed successfully in 1169ms. No work to process.
[2025-06-18 09:03:52.819 +10:00 INF] Database connection disposed
[2025-06-18 09:03:52.832 +10:00 INF] LEP Invoicer completed with result: 0
