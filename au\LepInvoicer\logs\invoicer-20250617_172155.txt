[2025-06-17 17:21:55.560 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 17:21:55.587 +10:00 INF] Initializing FastReport...
[2025-06-17 17:21:55.659 +10:00 INF] FastReport initialized successfully
[2025-06-17 17:21:56.114 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 17:21:57.405 +10:00 INF] Starting LEP Invoicer at "2025-06-17T17:21:57.4052867+10:00"
[2025-06-17 17:21:57.416 +10:00 INF] Initializing database service...
[2025-06-17 17:21:57.418 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 17:21:57.520 +10:00 INF] Database connection established successfully
[2025-06-17 17:21:57.522 +10:00 INF] Database service initialized successfully
[2025-06-17 17:21:57.524 +10:00 INF] Checking for pending work...
[2025-06-17 17:21:57.527 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 17:21:58.413 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 17:21:58.424 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 17:21:58.439 +10:00 INF] Found 0 credits to invoice
[2025-06-17 17:21:58.441 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 17:21:58.445 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 17:21:58.446 +10:00 INF] No pending work found
[2025-06-17 17:21:58.448 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 17:21:58.449 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 17:21:58.529 +10:00 INF] LEP Invoicer completed successfully in 1123ms. No work to process.
[2025-06-17 17:21:58.535 +10:00 INF] Database connection disposed
[2025-06-17 17:21:58.537 +10:00 INF] LEP Invoicer completed with result: 0
