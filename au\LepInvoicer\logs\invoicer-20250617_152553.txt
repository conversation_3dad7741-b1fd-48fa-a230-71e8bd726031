[2025-06-17 15:25:53.655 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 15:25:53.690 +10:00 INF] Initializing FastReport...
[2025-06-17 15:25:53.768 +10:00 INF] FastReport initialized successfully
[2025-06-17 15:25:54.206 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 15:25:55.676 +10:00 INF] Starting LEP Invoicer at "2025-06-17T15:25:55.6754473+10:00"
[2025-06-17 15:25:55.679 +10:00 INF] Initializing database service...
[2025-06-17 15:25:55.687 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 15:25:55.793 +10:00 INF] Database connection established successfully
[2025-06-17 15:25:55.795 +10:00 INF] Database service initialized successfully
[2025-06-17 15:25:55.802 +10:00 INF] Checking for pending work...
[2025-06-17 15:25:55.806 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 15:25:56.702 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 15:25:56.705 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 15:25:56.719 +10:00 INF] Found 0 credits to invoice
[2025-06-17 15:25:56.721 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 15:25:56.726 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 15:25:56.727 +10:00 INF] No pending work found
[2025-06-17 15:25:56.729 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 15:25:56.730 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 15:25:56.806 +10:00 INF] LEP Invoicer completed successfully in 1130ms. No work to process.
[2025-06-17 15:25:56.813 +10:00 INF] Database connection disposed
[2025-06-17 15:25:56.815 +10:00 INF] LEP Invoicer completed with result: 0
