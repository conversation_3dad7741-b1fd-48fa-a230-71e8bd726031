[2025-06-17 17:02:21.597 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 17:02:21.625 +10:00 INF] Initializing FastReport...
[2025-06-17 17:02:21.701 +10:00 INF] FastReport initialized successfully
[2025-06-17 17:02:22.124 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 17:02:23.393 +10:00 INF] Starting LEP Invoicer at "2025-06-17T17:02:23.3935462+10:00"
[2025-06-17 17:02:23.399 +10:00 INF] Initializing database service...
[2025-06-17 17:02:23.402 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 17:02:23.517 +10:00 INF] Database connection established successfully
[2025-06-17 17:02:23.519 +10:00 INF] Database service initialized successfully
[2025-06-17 17:02:23.523 +10:00 INF] Checking for pending work...
[2025-06-17 17:02:23.526 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 17:02:24.467 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 17:02:24.470 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 17:02:24.482 +10:00 INF] Found 0 credits to invoice
[2025-06-17 17:02:24.484 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 17:02:24.492 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 17:02:24.493 +10:00 INF] No pending work found
[2025-06-17 17:02:24.495 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 17:02:24.496 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 17:02:24.569 +10:00 INF] LEP Invoicer completed successfully in 1176ms. No work to process.
[2025-06-17 17:02:24.576 +10:00 INF] Database connection disposed
[2025-06-17 17:02:24.577 +10:00 INF] LEP Invoicer completed with result: 0
