[2025-06-18 08:59:28.681 +10:00 INF] Starting LEP Invoicer application
[2025-06-18 08:59:28.714 +10:00 INF] Initializing FastReport...
[2025-06-18 08:59:28.812 +10:00 INF] FastReport initialized successfully
[2025-06-18 08:59:29.212 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-18 08:59:30.463 +10:00 INF] Starting LEP Invoicer at "2025-06-18T08:59:30.4636123+10:00"
[2025-06-18 08:59:30.467 +10:00 INF] Initializing database service...
[2025-06-18 08:59:30.469 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-18 08:59:30.569 +10:00 INF] Database connection established successfully
[2025-06-18 08:59:30.571 +10:00 INF] Database service initialized successfully
[2025-06-18 08:59:30.574 +10:00 INF] Checking for pending work...
[2025-06-18 08:59:30.577 +10:00 INF] Getting 500 orders to invoice
[2025-06-18 08:59:31.486 +10:00 INF] Found 0 orders to invoice (filtered 16 candidates)
[2025-06-18 08:59:31.489 +10:00 INF] Getting 100 credits to invoice
[2025-06-18 08:59:31.502 +10:00 INF] Found 0 credits to invoice
[2025-06-18 08:59:31.504 +10:00 INF] Getting 100 refunds to invoice
[2025-06-18 08:59:31.511 +10:00 INF] Found 0 refunds to invoice
[2025-06-18 08:59:31.512 +10:00 INF] No pending work found
[2025-06-18 08:59:31.514 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-18 08:59:31.515 +10:00 INF] Cleaning up invoicer logs...
[2025-06-18 08:59:31.617 +10:00 INF] LEP Invoicer completed successfully in 1153ms. No work to process.
[2025-06-18 08:59:31.629 +10:00 INF] Database connection disposed
[2025-06-18 08:59:31.633 +10:00 INF] LEP Invoicer completed with result: 0
