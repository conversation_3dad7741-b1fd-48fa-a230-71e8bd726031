[2025-06-17 13:01:08.885 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 13:01:08.915 +10:00 INF] Initializing FastReport...
[2025-06-17 13:01:08.995 +10:00 INF] FastReport initialized successfully
[2025-06-17 13:01:09.469 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 13:01:10.745 +10:00 INF] Starting LEP Invoicer at "2025-06-17T13:01:10.7452917+10:00"
[2025-06-17 13:01:10.749 +10:00 INF] Initializing database service...
[2025-06-17 13:01:10.752 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 13:01:10.855 +10:00 INF] Database connection established successfully
[2025-06-17 13:01:10.857 +10:00 INF] Database service initialized successfully
[2025-06-17 13:01:10.861 +10:00 INF] Checking for pending work...
[2025-06-17 13:01:10.864 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 13:01:11.892 +10:00 INF] Found 10 orders to invoice (filtered 24 candidates)
[2025-06-17 13:01:11.896 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 13:01:11.929 +10:00 INF] Found 0 credits to invoice
[2025-06-17 13:01:11.932 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 13:01:11.952 +10:00 INF] Found 16 refunds to invoice
[2025-06-17 13:01:11.955 +10:00 INF] Found pending work: 10 orders, 16 refunds
[2025-06-17 13:01:11.957 +10:00 INF] Initializing MYOB and other services...
[2025-06-17 13:01:11.961 +10:00 INF] Initializing MYOB service...
[2025-06-17 13:01:11.965 +10:00 INF] Initializing MYOB with OAuth authentication
[2025-06-17 13:01:11.968 +10:00 INF] Starting OAuth authentication flow
[2025-06-17 13:01:11.975 +10:00 INF] Using existing OAuth tokens
[2025-06-17 13:01:11.976 +10:00 INF] Initializing MYOB services with OAuth keystore
[2025-06-17 13:01:11.980 +10:00 INF] MYOB services initialized successfully
[2025-06-17 13:01:11.981 +10:00 INF] OAuth keystore set for API calls
[2025-06-17 13:01:11.984 +10:00 INF] Getting company files from MYOB
[2025-06-17 13:01:12.758 +10:00 INF] MYOB service initialized with OAuth credentials. Using company file: LEP Colour Printers Pty Ltd
[2025-06-17 13:01:13.729 +10:00 INF] MYOB service initialized successfully with company file: LEP Colour Printers Pty Ltd
[2025-06-17 13:01:13.731 +10:00 INF] All services initialized successfully
[2025-06-17 13:01:13.734 +10:00 INF] Processing order invoices...
[2025-06-17 13:01:13.736 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 13:01:14.088 +10:00 INF] Found 10 orders to invoice (filtered 24 candidates)
[2025-06-17 13:01:14.090 +10:00 INF] Found 10 orders to process
[2025-06-17 13:01:14.094 +10:00 INF] Getting order 1418298
[2025-06-17 13:01:14.126 +10:00 INF] Processing order 1418298 with 1 jobs, total: ¤56.25
[2025-06-17 13:01:14.143 +10:00 INF] Creating order invoice for order 1418298
[2025-06-17 13:01:15.617 +10:00 INF] Successfully created MYOB invoice O1418298 for order 1418298
[2025-06-17 13:01:15.619 +10:00 INF] Successfully created MYOB invoice for order 1418298
[2025-06-17 13:01:15.653 +10:00 INF] Generating PDF invoice for order 1418298 at \\dfs01\resource\invoices\2025/Jun/17\O1418298.pdf
[2025-06-17 13:01:19.424 +10:00 INF] Successfully generated PDF for order 1418298
[2025-06-17 13:01:19.449 +10:00 WRN] No email address found for order 1418298
[2025-06-17 13:01:19.456 +10:00 INF] Successfully processed order 1418298 (MYOB + PDF)
[2025-06-17 13:01:19.459 +10:00 INF] Getting order 1418153
[2025-06-17 13:01:19.464 +10:00 INF] Processing order 1418153 with 2 jobs, total: ¤215.61
[2025-06-17 13:01:19.467 +10:00 INF] Creating order invoice for order 1418153
[2025-06-17 13:02:19.916 +10:00 ERR] Failed to create MYOB invoice for order 1418153
MYOB.AccountRight.SDK.ApiCommunicationException: Encountered a communication error (https://arl2.api.myob.com/accountright/b2f20bad-65c4-4dd3-a921-0a20ccfffb73/Sale/Invoice/Service/?returnBody=true)
 ---> System.Net.WebException: The remote server returned an error: (504) Gateway Timeout.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiPostRequestSync[TRequest,TResponse](Uri uri, TRequest entity, ICompanyFileCredentials credentials)
   at MYOB.AccountRight.SDK.Services.MutableService`1.InsertEx(CompanyFile cf, T entity, ICompanyFileCredentials credentials, ErrorLevel errorLevel)
   at LepInvoicer.Implementations.MYOBService.CreateOrderInvoice(IOrder order) in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 213
[2025-06-17 13:02:19.953 +10:00 WRN] Failed to create MYOB invoice for order 1418153: Type: ApiCommunicationException | Message: Encountered a communication error (https://arl2.api.myob.com/accountright/b2f20bad-65c4-4dd3-a921-0a20ccfffb73/Sale/Invoice/Service/?returnBody=true) | API Endpoint: https://arl2.api.myob.com/accountright/b2f20bad-65c4-4dd3-a921-0a20ccfffb73/Sale/Invoice/Service/?returnBody=true - continuing with PDF generation
[2025-06-17 13:02:19.962 +10:00 INF] Generating PDF invoice for order 1418153 at \\dfs01\resource\invoices\2025/Jun/17\O1418153.pdf
[2025-06-17 13:02:20.374 +10:00 INF] Successfully generated PDF for order 1418153
[2025-06-17 13:02:20.395 +10:00 WRN] No email address found for order 1418153
[2025-06-17 13:02:20.398 +10:00 INF] Partially processed order 1418153 (PDF only - MYOB failed)
[2025-06-17 13:02:20.401 +10:00 INF] Getting order 1418069
[2025-06-17 13:02:20.406 +10:00 INF] Processing order 1418069 with 2 jobs, total: ¤81.40
[2025-06-17 13:02:20.408 +10:00 INF] Creating order invoice for order 1418069
[2025-06-17 13:02:23.465 +10:00 INF] Successfully created MYOB invoice O1418069 for order 1418069
[2025-06-17 13:02:23.467 +10:00 INF] Successfully created MYOB invoice for order 1418069
[2025-06-17 13:02:23.469 +10:00 INF] Generating PDF invoice for order 1418069 at \\dfs01\resource\invoices\2025/Jun/17\O1418069.pdf
[2025-06-17 13:02:23.867 +10:00 INF] Successfully generated PDF for order 1418069
[2025-06-17 13:02:23.909 +10:00 WRN] No email address found for order 1418069
[2025-06-17 13:02:23.912 +10:00 INF] Successfully processed order 1418069 (MYOB + PDF)
[2025-06-17 13:02:23.915 +10:00 INF] Getting order 1417924
[2025-06-17 13:02:23.919 +10:00 INF] Processing order 1417924 with 1 jobs, total: ¤52.81
[2025-06-17 13:02:23.922 +10:00 INF] Creating order invoice for order 1417924
[2025-06-17 13:02:25.724 +10:00 INF] Successfully created MYOB invoice O1417924 for order 1417924
[2025-06-17 13:02:25.726 +10:00 INF] Successfully created MYOB invoice for order 1417924
[2025-06-17 13:02:25.727 +10:00 INF] Generating PDF invoice for order 1417924 at \\dfs01\resource\invoices\2025/Jun/17\O1417924.pdf
[2025-06-17 13:02:26.059 +10:00 INF] Successfully generated PDF for order 1417924
[2025-06-17 13:02:26.086 +10:00 WRN] No email address found for order 1417924
[2025-06-17 13:02:26.098 +10:00 INF] Successfully processed order 1417924 (MYOB + PDF)
[2025-06-17 13:02:26.101 +10:00 INF] Getting order 1417917
[2025-06-17 13:02:26.110 +10:00 INF] Processing order 1417917 with 1 jobs, total: ¤61.10
[2025-06-17 13:02:26.112 +10:00 INF] Creating order invoice for order 1417917
[2025-06-17 13:02:27.329 +10:00 INF] Successfully created MYOB invoice O1417917 for order 1417917
[2025-06-17 13:02:27.330 +10:00 INF] Successfully created MYOB invoice for order 1417917
[2025-06-17 13:02:27.332 +10:00 INF] Generating PDF invoice for order 1417917 at \\dfs01\resource\invoices\2025/Jun/17\O1417917.pdf
[2025-06-17 13:02:27.636 +10:00 INF] Successfully generated PDF for order 1417917
[2025-06-17 13:02:27.660 +10:00 WRN] No email address found for order 1417917
[2025-06-17 13:02:27.666 +10:00 INF] Successfully processed order 1417917 (MYOB + PDF)
[2025-06-17 13:02:27.669 +10:00 INF] Getting order 1417841
[2025-06-17 13:02:27.685 +10:00 INF] Processing order 1417841 with 5 jobs, total: ¤272.53
[2025-06-17 13:02:27.688 +10:00 INF] Creating order invoice for order 1417841
[2025-06-17 13:02:29.162 +10:00 INF] Successfully created MYOB invoice O1417841 for order 1417841
[2025-06-17 13:02:29.163 +10:00 INF] Successfully created MYOB invoice for order 1417841
[2025-06-17 13:02:29.165 +10:00 INF] Generating PDF invoice for order 1417841 at \\dfs01\resource\invoices\2025/Jun/17\O1417841.pdf
[2025-06-17 13:02:29.512 +10:00 INF] Successfully generated PDF for order 1417841
[2025-06-17 13:02:29.534 +10:00 WRN] No email address found for order 1417841
[2025-06-17 13:02:29.538 +10:00 INF] Successfully processed order 1417841 (MYOB + PDF)
[2025-06-17 13:02:29.542 +10:00 INF] Getting order 1417819
[2025-06-17 13:02:29.546 +10:00 INF] Processing order 1417819 with 1 jobs, total: ¤72.48
[2025-06-17 13:02:29.548 +10:00 INF] Creating order invoice for order 1417819
[2025-06-17 13:02:30.991 +10:00 INF] Successfully created MYOB invoice O1417819 for order 1417819
[2025-06-17 13:02:30.992 +10:00 INF] Successfully created MYOB invoice for order 1417819
[2025-06-17 13:02:30.995 +10:00 INF] Generating PDF invoice for order 1417819 at \\dfs01\resource\invoices\2025/Jun/17\O1417819.pdf
[2025-06-17 13:02:31.315 +10:00 INF] Successfully generated PDF for order 1417819
[2025-06-17 13:02:31.342 +10:00 WRN] No email address found for order 1417819
[2025-06-17 13:02:31.345 +10:00 INF] Successfully processed order 1417819 (MYOB + PDF)
[2025-06-17 13:02:31.349 +10:00 INF] Getting order 1417679
[2025-06-17 13:02:31.353 +10:00 INF] Processing order 1417679 with 1 jobs, total: ¤47.70
[2025-06-17 13:02:31.360 +10:00 INF] Creating order invoice for order 1417679
[2025-06-17 13:02:33.958 +10:00 INF] Successfully created MYOB invoice O1417679 for order 1417679
[2025-06-17 13:02:33.960 +10:00 INF] Successfully created MYOB invoice for order 1417679
[2025-06-17 13:02:33.963 +10:00 INF] Generating PDF invoice for order 1417679 at \\dfs01\resource\invoices\2025/Jun/17\O1417679.pdf
[2025-06-17 13:02:34.263 +10:00 INF] Successfully generated PDF for order 1417679
[2025-06-17 13:02:34.292 +10:00 WRN] No email address found for order 1417679
[2025-06-17 13:02:34.296 +10:00 INF] Successfully processed order 1417679 (MYOB + PDF)
[2025-06-17 13:02:34.299 +10:00 INF] Getting order 1416784
[2025-06-17 13:02:34.303 +10:00 INF] Processing order 1416784 with 1 jobs, total: ¤74.99
[2025-06-17 13:02:34.305 +10:00 INF] Creating order invoice for order 1416784
[2025-06-17 13:02:35.698 +10:00 INF] Successfully created MYOB invoice O1416784 for order 1416784
[2025-06-17 13:02:35.700 +10:00 INF] Successfully created MYOB invoice for order 1416784
[2025-06-17 13:02:35.702 +10:00 INF] Generating PDF invoice for order 1416784 at \\dfs01\resource\invoices\2025/Jun/17\O1416784.pdf
[2025-06-17 13:02:36.013 +10:00 INF] Successfully generated PDF for order 1416784
[2025-06-17 13:02:36.046 +10:00 WRN] No email address found for order 1416784
[2025-06-17 13:02:36.050 +10:00 INF] Successfully processed order 1416784 (MYOB + PDF)
[2025-06-17 13:02:36.053 +10:00 INF] Getting order 1416774
[2025-06-17 13:02:36.057 +10:00 INF] Processing order 1416774 with 4 jobs, total: ¤205.53
[2025-06-17 13:02:36.059 +10:00 INF] Creating order invoice for order 1416774
[2025-06-17 13:02:37.691 +10:00 INF] Successfully created MYOB invoice O1416774 for order 1416774
[2025-06-17 13:02:37.693 +10:00 INF] Successfully created MYOB invoice for order 1416774
[2025-06-17 13:02:37.695 +10:00 INF] Generating PDF invoice for order 1416774 at \\dfs01\resource\invoices\2025/Jun/17\O1416774.pdf
[2025-06-17 13:02:38.016 +10:00 INF] Successfully generated PDF for order 1416774
[2025-06-17 13:02:38.041 +10:00 WRN] No email address found for order 1416774
[2025-06-17 13:02:38.053 +10:00 INF] Successfully processed order 1416774 (MYOB + PDF)
[2025-06-17 13:02:38.068 +10:00 INF] Order processing completed. Processed: 10, Success: 10, Failed: 0
[2025-06-17 13:02:38.084 +10:00 INF] Processing credit invoices...
[2025-06-17 13:02:38.086 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 13:02:38.094 +10:00 INF] Found 0 credits to invoice
[2025-06-17 13:02:38.098 +10:00 INF] Processing refund invoices...
[2025-06-17 13:02:38.099 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 13:02:38.107 +10:00 INF] Found 16 refunds to invoice
[2025-06-17 13:02:38.111 +10:00 INF] Creating refund invoice for refund 1801, Amount: ¤1.69
[2025-06-17 13:02:38.531 +10:00 INF] Deleting existing invoice S248751801
[2025-06-17 13:02:38.873 +10:00 WRN] Failed to delete existing invoice S248751801 - invoice may be locked/paid in MYOB
MYOB.AccountRight.SDK.ApiValidationException: Encountered a validation error (https://arl2.api.myob.com/accountright/b2f20bad-65c4-4dd3-a921-0a20ccfffb73/Sale/Invoice/Service/ae7fcbd9-f0a0-4771-83a9-3dcdea23f5cb)
 ---> System.Net.WebException: The remote server returned an error: (400) Bad Request.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiDeleteRequestSync(Uri uri, ICompanyFileCredentials credentials)
   at MYOB.AccountRight.SDK.Services.MutableService`1.Delete(CompanyFile cf, Guid uid, ICompanyFileCredentials credentials, ErrorLevel errorLevel)
   at LepInvoicer.Implementations.MYOBService.<>c__DisplayClass33_1.<DeleteExistingInvoice>b__1() in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 875
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Implementations.MYOBService.DeleteExistingInvoice(String invoiceNumber) in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 874
[2025-06-17 13:02:38.893 +10:00 WRN] Skipping refund 1801 - cannot delete existing invoice S248751801
[2025-06-17 13:02:38.895 +10:00 INF] Creating refund invoice for refund 1802, Amount: ¤4.59
[2025-06-17 13:02:39.195 +10:00 INF] Deleting existing invoice S139771802
[2025-06-17 13:02:39.523 +10:00 WRN] Failed to delete existing invoice S139771802 - invoice may be locked/paid in MYOB
MYOB.AccountRight.SDK.ApiValidationException: Encountered a validation error (https://arl2.api.myob.com/accountright/b2f20bad-65c4-4dd3-a921-0a20ccfffb73/Sale/Invoice/Service/61295f40-7707-4574-825e-84da93a4b016)
 ---> System.Net.WebException: The remote server returned an error: (400) Bad Request.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiDeleteRequestSync(Uri uri, ICompanyFileCredentials credentials)
   at MYOB.AccountRight.SDK.Services.MutableService`1.Delete(CompanyFile cf, Guid uid, ICompanyFileCredentials credentials, ErrorLevel errorLevel)
   at LepInvoicer.Implementations.MYOBService.<>c__DisplayClass33_1.<DeleteExistingInvoice>b__1() in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 875
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Implementations.MYOBService.DeleteExistingInvoice(String invoiceNumber) in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 874
[2025-06-17 13:02:39.541 +10:00 WRN] Skipping refund 1802 - cannot delete existing invoice S139771802
[2025-06-17 13:02:39.542 +10:00 INF] Creating refund invoice for refund 1803, Amount: ¤9.27
[2025-06-17 13:02:40.004 +10:00 INF] Deleting existing invoice S150791803
[2025-06-17 13:02:40.436 +10:00 WRN] Failed to delete existing invoice S150791803 - invoice may be locked/paid in MYOB
MYOB.AccountRight.SDK.ApiValidationException: Encountered a validation error (https://arl2.api.myob.com/accountright/b2f20bad-65c4-4dd3-a921-0a20ccfffb73/Sale/Invoice/Service/ef2d3cc0-43a4-4c63-95e9-76389d058325)
 ---> System.Net.WebException: The remote server returned an error: (400) Bad Request.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiDeleteRequestSync(Uri uri, ICompanyFileCredentials credentials)
   at MYOB.AccountRight.SDK.Services.MutableService`1.Delete(CompanyFile cf, Guid uid, ICompanyFileCredentials credentials, ErrorLevel errorLevel)
   at LepInvoicer.Implementations.MYOBService.<>c__DisplayClass33_1.<DeleteExistingInvoice>b__1() in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 875
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Implementations.MYOBService.DeleteExistingInvoice(String invoiceNumber) in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 874
[2025-06-17 13:02:40.452 +10:00 WRN] Skipping refund 1803 - cannot delete existing invoice S150791803
[2025-06-17 13:02:40.459 +10:00 INF] Creating refund invoice for refund 1804, Amount: ¤11.23
[2025-06-17 13:02:40.891 +10:00 INF] Deleting existing invoice S150791804
[2025-06-17 13:02:41.212 +10:00 WRN] Failed to delete existing invoice S150791804 - invoice may be locked/paid in MYOB
MYOB.AccountRight.SDK.ApiValidationException: Encountered a validation error (https://arl2.api.myob.com/accountright/b2f20bad-65c4-4dd3-a921-0a20ccfffb73/Sale/Invoice/Service/903aeac6-182f-4aae-b4c4-d8e81708ec76)
 ---> System.Net.WebException: The remote server returned an error: (400) Bad Request.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiDeleteRequestSync(Uri uri, ICompanyFileCredentials credentials)
   at MYOB.AccountRight.SDK.Services.MutableService`1.Delete(CompanyFile cf, Guid uid, ICompanyFileCredentials credentials, ErrorLevel errorLevel)
   at LepInvoicer.Implementations.MYOBService.<>c__DisplayClass33_1.<DeleteExistingInvoice>b__1() in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 875
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Implementations.MYOBService.DeleteExistingInvoice(String invoiceNumber) in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 874
[2025-06-17 13:02:41.228 +10:00 WRN] Skipping refund 1804 - cannot delete existing invoice S150791804
[2025-06-17 13:02:41.229 +10:00 INF] Creating refund invoice for refund 1805, Amount: ¤32.62
[2025-06-17 13:02:44.023 +10:00 INF] Deleting existing invoice S252801805
[2025-06-17 13:02:45.427 +10:00 WRN] Failed to delete existing invoice S252801805 - invoice may be locked/paid in MYOB
MYOB.AccountRight.SDK.ApiValidationException: Encountered a validation error (https://arl2.api.myob.com/accountright/b2f20bad-65c4-4dd3-a921-0a20ccfffb73/Sale/Invoice/Service/6942f99f-e33c-424d-861a-2215ef9a1e09)
 ---> System.Net.WebException: The remote server returned an error: (400) Bad Request.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiDeleteRequestSync(Uri uri, ICompanyFileCredentials credentials)
   at MYOB.AccountRight.SDK.Services.MutableService`1.Delete(CompanyFile cf, Guid uid, ICompanyFileCredentials credentials, ErrorLevel errorLevel)
   at LepInvoicer.Implementations.MYOBService.<>c__DisplayClass33_1.<DeleteExistingInvoice>b__1() in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 875
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Implementations.MYOBService.DeleteExistingInvoice(String invoiceNumber) in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 874
[2025-06-17 13:02:45.448 +10:00 WRN] Skipping refund 1805 - cannot delete existing invoice S252801805
[2025-06-17 13:02:45.449 +10:00 INF] Creating refund invoice for refund 1806, Amount: ¤36.12
[2025-06-17 13:02:45.878 +10:00 INF] Deleting existing invoice S141881806
[2025-06-17 13:02:46.391 +10:00 WRN] Failed to delete existing invoice S141881806 - invoice may be locked/paid in MYOB
MYOB.AccountRight.SDK.ApiValidationException: Encountered a validation error (https://arl2.api.myob.com/accountright/b2f20bad-65c4-4dd3-a921-0a20ccfffb73/Sale/Invoice/Service/818eaa9f-5e51-4882-ab77-6b4bb0624735)
 ---> System.Net.WebException: The remote server returned an error: (400) Bad Request.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiDeleteRequestSync(Uri uri, ICompanyFileCredentials credentials)
   at MYOB.AccountRight.SDK.Services.MutableService`1.Delete(CompanyFile cf, Guid uid, ICompanyFileCredentials credentials, ErrorLevel errorLevel)
   at LepInvoicer.Implementations.MYOBService.<>c__DisplayClass33_1.<DeleteExistingInvoice>b__1() in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 875
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Implementations.MYOBService.DeleteExistingInvoice(String invoiceNumber) in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 874
[2025-06-17 13:02:46.432 +10:00 WRN] Skipping refund 1806 - cannot delete existing invoice S141881806
[2025-06-17 13:02:46.434 +10:00 INF] Creating refund invoice for refund 1807, Amount: ¤1.77
[2025-06-17 13:02:46.812 +10:00 INF] Deleting existing invoice S152161807
[2025-06-17 13:02:47.130 +10:00 WRN] Failed to delete existing invoice S152161807 - invoice may be locked/paid in MYOB
MYOB.AccountRight.SDK.ApiValidationException: Encountered a validation error (https://arl2.api.myob.com/accountright/b2f20bad-65c4-4dd3-a921-0a20ccfffb73/Sale/Invoice/Service/793efdaa-dcb5-422e-9ef2-69458a598c33)
 ---> System.Net.WebException: The remote server returned an error: (400) Bad Request.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiDeleteRequestSync(Uri uri, ICompanyFileCredentials credentials)
   at MYOB.AccountRight.SDK.Services.MutableService`1.Delete(CompanyFile cf, Guid uid, ICompanyFileCredentials credentials, ErrorLevel errorLevel)
   at LepInvoicer.Implementations.MYOBService.<>c__DisplayClass33_1.<DeleteExistingInvoice>b__1() in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 875
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Implementations.MYOBService.DeleteExistingInvoice(String invoiceNumber) in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 874
[2025-06-17 13:02:47.147 +10:00 WRN] Skipping refund 1807 - cannot delete existing invoice S152161807
[2025-06-17 13:02:47.148 +10:00 INF] Creating refund invoice for refund 1812, Amount: ¤1,312.65
[2025-06-17 13:02:48.787 +10:00 INF] Deleting existing invoice S251951812
[2025-06-17 13:02:49.370 +10:00 WRN] Failed to delete existing invoice S251951812 - invoice may be locked/paid in MYOB
MYOB.AccountRight.SDK.ApiValidationException: Encountered a validation error (https://arl2.api.myob.com/accountright/b2f20bad-65c4-4dd3-a921-0a20ccfffb73/Sale/Invoice/Service/a713338d-93e4-4f31-ab91-4947eb95c2d0)
 ---> System.Net.WebException: The remote server returned an error: (400) Bad Request.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiDeleteRequestSync(Uri uri, ICompanyFileCredentials credentials)
   at MYOB.AccountRight.SDK.Services.MutableService`1.Delete(CompanyFile cf, Guid uid, ICompanyFileCredentials credentials, ErrorLevel errorLevel)
   at LepInvoicer.Implementations.MYOBService.<>c__DisplayClass33_1.<DeleteExistingInvoice>b__1() in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 875
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Implementations.MYOBService.DeleteExistingInvoice(String invoiceNumber) in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 874
[2025-06-17 13:02:49.400 +10:00 WRN] Skipping refund 1812 - cannot delete existing invoice S251951812
[2025-06-17 13:02:49.407 +10:00 INF] Creating refund invoice for refund 1814, Amount: ¤1.37
[2025-06-17 13:02:49.698 +10:00 INF] Deleting existing invoice S137511814
[2025-06-17 13:03:44.202 +10:00 WRN] Failed to delete existing invoice S137511814 - invoice may be locked/paid in MYOB
MYOB.AccountRight.SDK.ApiValidationException: Encountered a validation error (https://arl2.api.myob.com/accountright/b2f20bad-65c4-4dd3-a921-0a20ccfffb73/Sale/Invoice/Service/ae7cf4da-1e73-4681-98b6-92685935c4b0)
 ---> System.Net.WebException: The remote server returned an error: (400) Bad Request.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiDeleteRequestSync(Uri uri, ICompanyFileCredentials credentials)
   at MYOB.AccountRight.SDK.Services.MutableService`1.Delete(CompanyFile cf, Guid uid, ICompanyFileCredentials credentials, ErrorLevel errorLevel)
   at LepInvoicer.Implementations.MYOBService.<>c__DisplayClass33_1.<DeleteExistingInvoice>b__1() in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 875
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Implementations.MYOBService.DeleteExistingInvoice(String invoiceNumber) in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 874
[2025-06-17 13:03:57.034 +10:00 WRN] Skipping refund 1814 - cannot delete existing invoice S137511814
[2025-06-17 13:03:57.053 +10:00 INF] Creating refund invoice for refund 1815, Amount: ¤3.83
[2025-06-17 13:03:57.741 +10:00 INF] Deleting existing invoice S138991815
[2025-06-17 13:03:58.843 +10:00 WRN] Failed to delete existing invoice S138991815 - invoice may be locked/paid in MYOB
MYOB.AccountRight.SDK.ApiValidationException: Encountered a validation error (https://arl2.api.myob.com/accountright/b2f20bad-65c4-4dd3-a921-0a20ccfffb73/Sale/Invoice/Service/0aaeec22-6d8e-4d71-87bd-33f814e5f06f)
 ---> System.Net.WebException: The remote server returned an error: (400) Bad Request.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiDeleteRequestSync(Uri uri, ICompanyFileCredentials credentials)
   at MYOB.AccountRight.SDK.Services.MutableService`1.Delete(CompanyFile cf, Guid uid, ICompanyFileCredentials credentials, ErrorLevel errorLevel)
   at LepInvoicer.Implementations.MYOBService.<>c__DisplayClass33_1.<DeleteExistingInvoice>b__1() in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 875
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Implementations.MYOBService.DeleteExistingInvoice(String invoiceNumber) in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 874
[2025-06-17 13:03:58.923 +10:00 WRN] Skipping refund 1815 - cannot delete existing invoice S138991815
[2025-06-17 13:03:58.925 +10:00 INF] Creating refund invoice for refund 1816, Amount: ¤6.80
[2025-06-17 13:04:02.108 +10:00 INF] Deleting existing invoice S143701816
[2025-06-17 13:04:02.561 +10:00 WRN] Failed to delete existing invoice S143701816 - invoice may be locked/paid in MYOB
MYOB.AccountRight.SDK.ApiValidationException: Encountered a validation error (https://arl2.api.myob.com/accountright/b2f20bad-65c4-4dd3-a921-0a20ccfffb73/Sale/Invoice/Service/5ac7ff46-5d70-47ca-a353-44c57a10c35c)
 ---> System.Net.WebException: The remote server returned an error: (400) Bad Request.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiDeleteRequestSync(Uri uri, ICompanyFileCredentials credentials)
   at MYOB.AccountRight.SDK.Services.MutableService`1.Delete(CompanyFile cf, Guid uid, ICompanyFileCredentials credentials, ErrorLevel errorLevel)
   at LepInvoicer.Implementations.MYOBService.<>c__DisplayClass33_1.<DeleteExistingInvoice>b__1() in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 875
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Implementations.MYOBService.DeleteExistingInvoice(String invoiceNumber) in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 874
[2025-06-17 13:04:02.582 +10:00 WRN] Skipping refund 1816 - cannot delete existing invoice S143701816
[2025-06-17 13:04:02.593 +10:00 INF] Creating refund invoice for refund 1817, Amount: ¤19.84
[2025-06-17 13:04:03.364 +10:00 INF] Deleting existing invoice S143701817
[2025-06-17 13:04:03.731 +10:00 WRN] Failed to delete existing invoice S143701817 - invoice may be locked/paid in MYOB
MYOB.AccountRight.SDK.ApiValidationException: Encountered a validation error (https://arl2.api.myob.com/accountright/b2f20bad-65c4-4dd3-a921-0a20ccfffb73/Sale/Invoice/Service/2e674898-d09f-451c-921f-b6054b956d02)
 ---> System.Net.WebException: The remote server returned an error: (400) Bad Request.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiDeleteRequestSync(Uri uri, ICompanyFileCredentials credentials)
   at MYOB.AccountRight.SDK.Services.MutableService`1.Delete(CompanyFile cf, Guid uid, ICompanyFileCredentials credentials, ErrorLevel errorLevel)
   at LepInvoicer.Implementations.MYOBService.<>c__DisplayClass33_1.<DeleteExistingInvoice>b__1() in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 875
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Implementations.MYOBService.DeleteExistingInvoice(String invoiceNumber) in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 874
[2025-06-17 13:04:03.770 +10:00 WRN] Skipping refund 1817 - cannot delete existing invoice S143701817
[2025-06-17 13:04:03.772 +10:00 INF] Creating refund invoice for refund 1818, Amount: ¤61.92
[2025-06-17 13:04:04.093 +10:00 INF] Deleting existing invoice S1531791818
[2025-06-17 13:04:04.540 +10:00 WRN] Failed to delete existing invoice S1531791818 - invoice may be locked/paid in MYOB
MYOB.AccountRight.SDK.ApiValidationException: Encountered a validation error (https://arl2.api.myob.com/accountright/b2f20bad-65c4-4dd3-a921-0a20ccfffb73/Sale/Invoice/Service/f2d3d1a7-f2e9-4aee-b0fe-cf5f0c77f7bc)
 ---> System.Net.WebException: The remote server returned an error: (400) Bad Request.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiDeleteRequestSync(Uri uri, ICompanyFileCredentials credentials)
   at MYOB.AccountRight.SDK.Services.MutableService`1.Delete(CompanyFile cf, Guid uid, ICompanyFileCredentials credentials, ErrorLevel errorLevel)
   at LepInvoicer.Implementations.MYOBService.<>c__DisplayClass33_1.<DeleteExistingInvoice>b__1() in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 875
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Implementations.MYOBService.DeleteExistingInvoice(String invoiceNumber) in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 874
[2025-06-17 13:04:04.703 +10:00 WRN] Skipping refund 1818 - cannot delete existing invoice S1531791818
[2025-06-17 13:04:04.704 +10:00 INF] Creating refund invoice for refund 1824, Amount: ¤70.04
[2025-06-17 13:04:07.471 +10:00 INF] Deleting existing invoice S189871824
[2025-06-17 13:04:07.842 +10:00 WRN] Failed to delete existing invoice S189871824 - invoice may be locked/paid in MYOB
MYOB.AccountRight.SDK.ApiValidationException: Encountered a validation error (https://arl2.api.myob.com/accountright/b2f20bad-65c4-4dd3-a921-0a20ccfffb73/Sale/Invoice/Service/3366c74b-aec7-4ee3-87d0-4ed666a81e70)
 ---> System.Net.WebException: The remote server returned an error: (400) Bad Request.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiDeleteRequestSync(Uri uri, ICompanyFileCredentials credentials)
   at MYOB.AccountRight.SDK.Services.MutableService`1.Delete(CompanyFile cf, Guid uid, ICompanyFileCredentials credentials, ErrorLevel errorLevel)
   at LepInvoicer.Implementations.MYOBService.<>c__DisplayClass33_1.<DeleteExistingInvoice>b__1() in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 875
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Implementations.MYOBService.DeleteExistingInvoice(String invoiceNumber) in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 874
[2025-06-17 13:04:07.903 +10:00 WRN] Skipping refund 1824 - cannot delete existing invoice S189871824
[2025-06-17 13:04:07.904 +10:00 INF] Creating refund invoice for refund 1836, Amount: ¤32.32
[2025-06-17 13:04:08.303 +10:00 INF] Deleting existing invoice S252801836
[2025-06-17 13:04:08.561 +10:00 WRN] Failed to delete existing invoice S252801836 - invoice may be locked/paid in MYOB
MYOB.AccountRight.SDK.ApiValidationException: Encountered a validation error (https://arl2.api.myob.com/accountright/b2f20bad-65c4-4dd3-a921-0a20ccfffb73/Sale/Invoice/Service/4b9f90a9-fe2b-4248-bce3-1aa759007fcd)
 ---> System.Net.WebException: The remote server returned an error: (400) Bad Request.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiDeleteRequestSync(Uri uri, ICompanyFileCredentials credentials)
   at MYOB.AccountRight.SDK.Services.MutableService`1.Delete(CompanyFile cf, Guid uid, ICompanyFileCredentials credentials, ErrorLevel errorLevel)
   at LepInvoicer.Implementations.MYOBService.<>c__DisplayClass33_1.<DeleteExistingInvoice>b__1() in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 875
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Implementations.MYOBService.DeleteExistingInvoice(String invoiceNumber) in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 874
[2025-06-17 13:04:08.591 +10:00 WRN] Skipping refund 1836 - cannot delete existing invoice S252801836
[2025-06-17 13:04:08.593 +10:00 INF] Creating refund invoice for refund 1837, Amount: ¤26.00
[2025-06-17 13:04:09.094 +10:00 INF] Deleting existing invoice S191871837
[2025-06-17 13:04:09.470 +10:00 WRN] Failed to delete existing invoice S191871837 - invoice may be locked/paid in MYOB
MYOB.AccountRight.SDK.ApiValidationException: Encountered a validation error (https://arl2.api.myob.com/accountright/b2f20bad-65c4-4dd3-a921-0a20ccfffb73/Sale/Invoice/Service/27c54bcf-d87b-45d8-a5e4-997d4b831e2e)
 ---> System.Net.WebException: The remote server returned an error: (400) Bad Request.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiDeleteRequestSync(Uri uri, ICompanyFileCredentials credentials)
   at MYOB.AccountRight.SDK.Services.MutableService`1.Delete(CompanyFile cf, Guid uid, ICompanyFileCredentials credentials, ErrorLevel errorLevel)
   at LepInvoicer.Implementations.MYOBService.<>c__DisplayClass33_1.<DeleteExistingInvoice>b__1() in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 875
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Implementations.MYOBService.DeleteExistingInvoice(String invoiceNumber) in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 874
[2025-06-17 13:04:09.495 +10:00 WRN] Skipping refund 1837 - cannot delete existing invoice S191871837
[2025-06-17 13:04:09.497 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 13:04:09.579 +10:00 INF] LEP Invoicer completed successfully in 178829ms. Orders: 10, Credits: 0, Refunds: 16
[2025-06-17 13:04:09.588 +10:00 INF] Database connection disposed
[2025-06-17 13:04:09.593 +10:00 INF] LEP Invoicer completed with result: 0
