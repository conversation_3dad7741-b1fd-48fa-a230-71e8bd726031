[2025-06-18 09:26:24.585 +10:00 INF] Starting LEP Invoicer application
[2025-06-18 09:26:24.613 +10:00 INF] Initializing FastReport...
[2025-06-18 09:26:24.700 +10:00 INF] FastReport initialized successfully
[2025-06-18 09:26:25.165 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-18 09:26:26.460 +10:00 INF] Starting LEP Invoicer at "2025-06-18T09:26:26.4597222+10:00"
[2025-06-18 09:26:26.463 +10:00 INF] Initializing database service...
[2025-06-18 09:26:26.466 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-18 09:26:26.639 +10:00 INF] Database connection established successfully
[2025-06-18 09:26:26.640 +10:00 INF] Database service initialized successfully
[2025-06-18 09:26:26.643 +10:00 INF] Checking for pending work...
[2025-06-18 09:26:26.647 +10:00 INF] Getting 500 orders to invoice
[2025-06-18 09:26:27.704 +10:00 INF] Found 0 orders to invoice (filtered 16 candidates)
[2025-06-18 09:26:27.709 +10:00 INF] Getting 100 credits to invoice
[2025-06-18 09:26:27.723 +10:00 INF] Found 0 credits to invoice
[2025-06-18 09:26:27.726 +10:00 INF] Getting 100 refunds to invoice
[2025-06-18 09:26:27.730 +10:00 INF] Found 0 refunds to invoice
[2025-06-18 09:26:27.731 +10:00 INF] No pending work found
[2025-06-18 09:26:27.732 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-18 09:26:27.733 +10:00 INF] Cleaning up invoicer logs...
[2025-06-18 09:26:27.862 +10:00 INF] LEP Invoicer completed successfully in 1402ms. No work to process.
[2025-06-18 09:26:27.868 +10:00 INF] Database connection disposed
[2025-06-18 09:26:27.870 +10:00 INF] LEP Invoicer completed with result: 0
