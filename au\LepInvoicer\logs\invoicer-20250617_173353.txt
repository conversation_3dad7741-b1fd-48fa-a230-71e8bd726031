[2025-06-17 17:33:53.569 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 17:33:53.600 +10:00 INF] Initializing FastReport...
[2025-06-17 17:33:53.676 +10:00 INF] FastReport initialized successfully
[2025-06-17 17:33:54.069 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 17:33:55.346 +10:00 INF] Starting LEP Invoicer at "2025-06-17T17:33:55.3456574+10:00"
[2025-06-17 17:33:55.352 +10:00 INF] Initializing database service...
[2025-06-17 17:33:55.355 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 17:33:55.465 +10:00 INF] Database connection established successfully
[2025-06-17 17:33:55.467 +10:00 INF] Database service initialized successfully
[2025-06-17 17:33:55.469 +10:00 INF] Checking for pending work...
[2025-06-17 17:33:55.472 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 17:33:56.378 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 17:33:56.382 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 17:33:56.394 +10:00 INF] Found 0 credits to invoice
[2025-06-17 17:33:56.396 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 17:33:56.402 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 17:33:56.404 +10:00 INF] No pending work found
[2025-06-17 17:33:56.421 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 17:33:56.425 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 17:33:56.508 +10:00 INF] LEP Invoicer completed successfully in 1162ms. No work to process.
[2025-06-17 17:33:56.523 +10:00 INF] Database connection disposed
[2025-06-17 17:33:56.525 +10:00 INF] LEP Invoicer completed with result: 0
