[2025-06-18 08:21:52.695 +10:00 INF] Starting LEP Invoicer application
[2025-06-18 08:21:52.724 +10:00 INF] Initializing FastReport...
[2025-06-18 08:21:52.798 +10:00 INF] FastReport initialized successfully
[2025-06-18 08:21:53.199 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-18 08:21:54.499 +10:00 INF] Starting LEP Invoicer at "2025-06-18T08:21:54.4994875+10:00"
[2025-06-18 08:21:54.503 +10:00 INF] Initializing database service...
[2025-06-18 08:21:54.505 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-18 08:21:54.606 +10:00 INF] Database connection established successfully
[2025-06-18 08:21:54.607 +10:00 INF] Database service initialized successfully
[2025-06-18 08:21:54.611 +10:00 INF] Checking for pending work...
[2025-06-18 08:21:54.617 +10:00 INF] Getting 500 orders to invoice
[2025-06-18 08:21:55.534 +10:00 INF] Found 0 orders to invoice (filtered 16 candidates)
[2025-06-18 08:21:55.537 +10:00 INF] Getting 100 credits to invoice
[2025-06-18 08:21:55.553 +10:00 INF] Found 0 credits to invoice
[2025-06-18 08:21:55.555 +10:00 INF] Getting 100 refunds to invoice
[2025-06-18 08:21:55.559 +10:00 INF] Found 0 refunds to invoice
[2025-06-18 08:21:55.573 +10:00 INF] No pending work found
[2025-06-18 08:21:55.575 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-18 08:21:55.577 +10:00 INF] Cleaning up invoicer logs...
[2025-06-18 08:21:55.663 +10:00 INF] LEP Invoicer completed successfully in 1164ms. No work to process.
[2025-06-18 08:21:55.669 +10:00 INF] Database connection disposed
[2025-06-18 08:21:55.671 +10:00 INF] LEP Invoicer completed with result: 0
