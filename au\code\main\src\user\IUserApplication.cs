using lumen.csv;
using NHibernate;
using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;

namespace lep.user
{
	public interface IUserApplication
	{
		IStaff NewStaff();

		ICustomerUser NewCustomerUser();

		IUser GetUser(int id);

		IUser Refresh(IUser user);

		IStaff GetSystemUser();

		ICustomerUser GetCustomerUser(string username);

		ICustomerUser GetCustomerUser(int customerId);

		void Save(IUser user);

		IUser AttemptLogin(string username, string password);

		ICriteria StaffCriteria(string username, string firstname, string lastname, List<Role> roles);

		ICriteria CustomerCriteria(string customer, int customerid, int orderid, int jobid, bool? systemAccess,
			PaymentTermsOptions? paymentTermsOptions, bool IsPrintPortalEnabled = false, bool showArchived = false);

		ICriteria CustomerCriteria2(string customer, int customerid, int orderid, int jobid, bool? systemAccess,
			PaymentTermsOptions? paymentTermsOptions, bool IsPrintPortalEnabled = false, bool showArchived = false,
			string PostalPostCode = null, string SalesConsultant = null, string CustomerStatus = null,
			string RegionLep = null, string FranchiseCode = null, string BusinessType=null, string notes=null);

		ICriteria CustomerCriteria2WithProjection(string customer, int customerid, int orderid, int jobid,
			bool? systemAccess, PaymentTermsOptions? paymentTermsOptions, bool IsPrintPortalEnabled = false,
			bool showArchived = false, string PostalPostCode = null, string SalesConsultant = null,
			string CustomerStatus = null, string RegionLep = null, string FranchiseCode = null,
			string BusinessType = null, string notes = null);

		IStaff GetStaffByIP(string ip);

		bool IsValidUsername(string username, IUser user);

		bool CheckBusinessName(ICustomerUser customer);

		IList<ICustomerUser> FindOnHoldCustomer();

		void ExportOnHold(CsvSerialiser serial);

		void UpdateOnHold(List<string> customers);

		void MergeCustomer(ICustomerUser from, ICustomerUser to);

		Decimal GetCustomerLEPOnlineBalance(int UserID);

		Decimal GetCustomerAvailableBalance(int UserID);

		bool NeedMergeCustomer(ICustomerUser customer);

		void Delete(ICustomerUser customer);

		void Delete(IUser user);

		bool IsUniqueMYOB(string myob, ICustomerUser customer);

		IList<IUser> GetUserByName(string username);

		void SetPassword(IUser user, string password);

		FileInfo GetCustomerLogo(string customerUsername);

		void SaveCustomerLogo(string customerUsername, Stream file, string extension);

		IList GetCustomerProductPricing();

		IList GetCustomerFreightPricing();

		IList<ICustomerUser> GetSubCustomers(ICustomerUser parent);
	}
}
