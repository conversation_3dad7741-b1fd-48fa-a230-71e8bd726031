[2025-06-18 10:04:39.703 +10:00 INF] Starting LEP Invoicer application
[2025-06-18 10:04:39.743 +10:00 INF] Initializing FastReport...
[2025-06-18 10:04:39.821 +10:00 INF] FastReport initialized successfully
[2025-06-18 10:04:40.246 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-18 10:04:41.773 +10:00 INF] Starting LEP Invoicer at "2025-06-18T10:04:41.7735520+10:00"
[2025-06-18 10:04:41.778 +10:00 INF] Initializing database service...
[2025-06-18 10:04:41.781 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-18 10:04:41.893 +10:00 INF] Database connection established successfully
[2025-06-18 10:04:41.894 +10:00 INF] Database service initialized successfully
[2025-06-18 10:04:41.898 +10:00 INF] Checking for pending work...
[2025-06-18 10:04:41.902 +10:00 INF] Getting 500 orders to invoice
[2025-06-18 10:04:42.876 +10:00 INF] Found 0 orders to invoice (filtered 16 candidates)
[2025-06-18 10:04:42.879 +10:00 INF] Getting 100 credits to invoice
[2025-06-18 10:04:42.890 +10:00 INF] Found 0 credits to invoice
[2025-06-18 10:04:42.892 +10:00 INF] Getting 100 refunds to invoice
[2025-06-18 10:04:42.895 +10:00 INF] Found 0 refunds to invoice
[2025-06-18 10:04:42.898 +10:00 INF] No pending work found
[2025-06-18 10:04:42.899 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-18 10:04:42.900 +10:00 INF] Cleaning up invoicer logs...
[2025-06-18 10:04:42.975 +10:00 INF] LEP Invoicer completed successfully in 1201ms. No work to process.
[2025-06-18 10:04:42.981 +10:00 INF] Database connection disposed
[2025-06-18 10:04:42.983 +10:00 INF] LEP Invoicer completed with result: 0
