[2025-06-17 17:14:18.504 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 17:14:18.530 +10:00 INF] Initializing FastReport...
[2025-06-17 17:14:18.608 +10:00 INF] FastReport initialized successfully
[2025-06-17 17:14:19.083 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 17:14:20.320 +10:00 INF] Starting LEP Invoicer at "2025-06-17T17:14:20.3202368+10:00"
[2025-06-17 17:14:20.324 +10:00 INF] Initializing database service...
[2025-06-17 17:14:20.327 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 17:14:20.431 +10:00 INF] Database connection established successfully
[2025-06-17 17:14:20.432 +10:00 INF] Database service initialized successfully
[2025-06-17 17:14:20.435 +10:00 INF] Checking for pending work...
[2025-06-17 17:14:20.438 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 17:14:21.370 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 17:14:21.373 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 17:14:21.386 +10:00 INF] Found 0 credits to invoice
[2025-06-17 17:14:21.388 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 17:14:21.392 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 17:14:21.393 +10:00 INF] No pending work found
[2025-06-17 17:14:21.396 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 17:14:21.397 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 17:14:21.476 +10:00 INF] LEP Invoicer completed successfully in 1155ms. No work to process.
[2025-06-17 17:14:21.484 +10:00 INF] Database connection disposed
[2025-06-17 17:14:21.487 +10:00 INF] LEP Invoicer completed with result: 0
