#region using

using lep.user.impl;
using System;

#endregion using

namespace lep.user
{
	public interface IStaff : IUser
    {
        Role Role { get; set; }
        String Barcode { get; set; }
        string IPAddress { get; set; }
        OrderSearchCriteria OrderSearchCriteria { get; set; }
        RunSearchCriteria RunSearchCriteria { get; set; }

        void CreateOrderSearchCriteria();

        void CreatRunSearchCriteria();
    }
}