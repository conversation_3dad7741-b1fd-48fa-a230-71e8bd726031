[2025-06-17 17:55:43.494 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 17:55:43.522 +10:00 INF] Initializing FastReport...
[2025-06-17 17:55:43.593 +10:00 INF] FastReport initialized successfully
[2025-06-17 17:55:44.029 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 17:55:45.306 +10:00 INF] Starting LEP Invoicer at "2025-06-17T17:55:45.3061340+10:00"
[2025-06-17 17:55:45.320 +10:00 INF] Initializing database service...
[2025-06-17 17:55:45.323 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 17:55:45.428 +10:00 INF] Database connection established successfully
[2025-06-17 17:55:45.429 +10:00 INF] Database service initialized successfully
[2025-06-17 17:55:45.432 +10:00 INF] Checking for pending work...
[2025-06-17 17:55:45.435 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 17:55:46.326 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 17:55:46.329 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 17:55:46.343 +10:00 INF] Found 0 credits to invoice
[2025-06-17 17:55:46.345 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 17:55:46.349 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 17:55:46.351 +10:00 INF] No pending work found
[2025-06-17 17:55:46.352 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 17:55:46.353 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 17:55:46.435 +10:00 INF] LEP Invoicer completed successfully in 1129ms. No work to process.
[2025-06-17 17:55:46.449 +10:00 INF] Database connection disposed
[2025-06-17 17:55:46.451 +10:00 INF] LEP Invoicer completed with result: 0
