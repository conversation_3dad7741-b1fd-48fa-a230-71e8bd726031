using lep.job;
using lep.run;
using System.Collections.Generic;

namespace lep.user
{
	public interface IRunSearchCriteria
    {
        int Id { get; set; }
        bool IsSearchPanelOpen { get; set; }
        HashSet<int> OpenRun { get; set; }

        //  string RunStatus { get; set; }
        HashSet<RunStatusOptions> RunStatus { get; set; }

        string Customer { get; set; }
        string OrderNr { get; set; }
        string JobNr { get; set; }
        string RunNr { get; set; }
        string Ordering { get; set; }
        string RunOrdering { get; set; }
        bool IsUrgent { get; set; }
        bool IsOnHold { get; set; }
        IPaperSize Size { get; set; }
        RunCelloglazeOptions? Cello { get; set; }
        int? Side { get; set; }
        IStock Stock { get; set; }
		string StockKind { get; set; }

		// New properties for Category and Template filters
		HashSet<int> JobTypes { get; set; }
		int? JobType { get; set; }

		IUser Staff { get; set; }
        RunSearchOptions RunSearchOption { get; set; }
        Facility Facility { get; set; }
    }
}
