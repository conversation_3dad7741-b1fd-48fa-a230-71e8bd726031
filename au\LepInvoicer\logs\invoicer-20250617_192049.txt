[2025-06-17 19:20:49.626 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 19:20:49.658 +10:00 INF] Initializing FastReport...
[2025-06-17 19:20:49.745 +10:00 INF] FastReport initialized successfully
[2025-06-17 19:20:50.194 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 19:20:51.562 +10:00 INF] Starting LEP Invoicer at "2025-06-17T19:20:51.5619390+10:00"
[2025-06-17 19:20:51.565 +10:00 INF] Initializing database service...
[2025-06-17 19:20:51.568 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 19:20:51.667 +10:00 INF] Database connection established successfully
[2025-06-17 19:20:51.668 +10:00 INF] Database service initialized successfully
[2025-06-17 19:20:51.671 +10:00 INF] Checking for pending work...
[2025-06-17 19:20:51.674 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 19:20:52.568 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 19:20:52.570 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 19:20:52.584 +10:00 INF] Found 0 credits to invoice
[2025-06-17 19:20:52.585 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 19:20:52.591 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 19:20:52.593 +10:00 INF] No pending work found
[2025-06-17 19:20:52.600 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 19:20:52.602 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 19:20:52.700 +10:00 INF] LEP Invoicer completed successfully in 1138ms. No work to process.
[2025-06-17 19:20:52.706 +10:00 INF] Database connection disposed
[2025-06-17 19:20:52.708 +10:00 INF] LEP Invoicer completed with result: 0
