[2025-06-17 19:23:00.542 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 19:23:00.570 +10:00 INF] Initializing FastReport...
[2025-06-17 19:23:00.644 +10:00 INF] FastReport initialized successfully
[2025-06-17 19:23:01.092 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 19:23:02.589 +10:00 INF] Starting LEP Invoicer at "2025-06-17T19:23:02.5887608+10:00"
[2025-06-17 19:23:02.608 +10:00 INF] Initializing database service...
[2025-06-17 19:23:02.613 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 19:23:02.739 +10:00 INF] Database connection established successfully
[2025-06-17 19:23:02.741 +10:00 INF] Database service initialized successfully
[2025-06-17 19:23:02.744 +10:00 INF] Checking for pending work...
[2025-06-17 19:23:02.747 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 19:23:03.642 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 19:23:03.645 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 19:23:03.658 +10:00 INF] Found 0 credits to invoice
[2025-06-17 19:23:03.660 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 19:23:03.665 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 19:23:03.666 +10:00 INF] No pending work found
[2025-06-17 19:23:03.667 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 19:23:03.669 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 19:23:03.743 +10:00 INF] LEP Invoicer completed successfully in 1154ms. No work to process.
[2025-06-17 19:23:03.750 +10:00 INF] Database connection disposed
[2025-06-17 19:23:03.752 +10:00 INF] LEP Invoicer completed with result: 0
