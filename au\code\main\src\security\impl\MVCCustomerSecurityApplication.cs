﻿using lep.security;
using lep.user;

using Microsoft.AspNetCore.Http;

using NHibernate;

using System;
using System.Collections.Generic;
using System.Linq;

using ISession = NHibernate.ISession;

namespace lep.security.impl
{
	public class MVCCustomerSecurityApplication : ISecurityApplication
	{

		public MVCCustomerSecurityApplication()
		{

		}

		private ISession session;


		public MVCCustomerSecurityApplication(IHttpContextAccessor contextAccessor, ISession session, IUserApplication userApplication)
		{
			this.HttpContextAccessor = contextAccessor;
			this.session = session;
			this.UserApplication = userApplication;
		}

	
		#region properties
		public IHttpContextAccessor HttpContextAccessor { get; set; }
		public IUserApplication UserApplication { get; set; }

		#endregion
		private static Dictionary<string, IDictionary<string, bool>> perms =
		new Dictionary<string, IDictionary<string, bool>>();

		private void CreateSecurityPermission()
		{
			//todo: object security check not added yet.
			IDictionary<string, bool> Public = new Dictionary<string, bool>();
			Public.Add("customer.order.add", true);
			Public.Add("job.comment.create", true);
			Public.Add("job.delete", true);
			Public.Add("job.read", true);
			Public.Add("job.search", true);
			Public.Add("job.submit", true);
			Public.Add("job.update", true);
			Public.Add("order.create", true);
			Public.Add("order.delete", true);
			Public.Add("order.jobs.add", true);
			Public.Add("order.jobs.delete", true);
			Public.Add("order.jobs.edit", true);
			Public.Add("order.jobs.list", true);
			Public.Add("order.read", true);
			Public.Add("order.submit", true);
			Public.Add("order.update", true);
			Public.Add("order.withdraw", true);
			Public.Add("configuration.read", true);
			Public.Add("customer.read", true);
			Public.Add("customer.update", true);

			IDictionary<string, bool> PreFlight = new Dictionary<string, bool>();
			foreach (string s in Public.Keys)
			{
				PreFlight.Add(s, true);
			}
			PreFlight.Add("job.approve", true);
			PreFlight.Add("job.preflight", true);
			PreFlight.Add("order.preflight", true);

			IDictionary<string, bool> PrePress = new Dictionary<string, bool>();
			foreach (string s in PreFlight.Keys)
			{
				PrePress.Add(s, true);
			}
			PrePress.Add("job.prepress", true);
			PrePress.Add("run.create", true);
			PrePress.Add("run.delete", true);
			PrePress.Add("run.read", true);
			PrePress.Add("run.search", true);
			PrePress.Add("run.update", true);

			IDictionary<string, bool> PressAndFinishing = new Dictionary<string, bool>();
			foreach (string s in PrePress.Keys)
			{
				PressAndFinishing.Add(s, true);
			}
			PressAndFinishing.Add("job.press", true);
			PressAndFinishing.Add("run.update.prepress", true);
			PressAndFinishing.Add("run.update.press", true);
			PressAndFinishing.Add("run.update.plating", true);
			PressAndFinishing.Add("run.update.finishing", true);

			IDictionary<string, bool> Invoice = new Dictionary<string, bool>();
			foreach (string s in PressAndFinishing.Keys)
			{
				Invoice.Add(s, true);
			}

			IDictionary<string, bool> Dispatch = new Dictionary<string, bool>();
			foreach (string s in Invoice.Keys)
			{
				Dispatch.Add(s, true);
			}
			Dispatch.Add("job.dispatch", true);
			Dispatch.Add("order.dispatch", true);

			IDictionary<string, bool> Administrator = new Dictionary<string, bool>();
			foreach (string s in Dispatch.Keys)
			{
				Administrator.Add(s, true);
			}
			Administrator.Add("configuration.update", true);
			Administrator.Add("customer.create", true);
			Administrator.Add("customer.delete", true);
			Administrator.Add("user.create", true);
			Administrator.Add("user.delete", true);
			Administrator.Add("user.update", true);

			IDictionary<string, bool> Manager = new Dictionary<string, bool>();
			foreach (string s in Administrator.Keys)
			{
				Manager.Add(s, true);
			}

			IDictionary<string, bool> System = new Dictionary<string, bool>();
			foreach (string s in Manager.Keys)
			{
				System.Add(s, true);
			}

		}

		public bool HasAnyTask(string app)
		{
			return HasTask(app, "");
		}

		public bool HasTask(string app, string task)
		{


			IUser user = Identity as IUser;
			if (user == null)
			{
				return false;
			}

			// whoever it is, they must be enabled
			if (!user.IsEnabled)
			{
				return false;
			}

			////todo : remove after object security check add
			//return true;


			if (user.IsStaff)
			{
				IStaff staff = user as IStaff;
				string perm = staff.Role.ToString();
				if (perms.ContainsKey(perm))
				{
					if (perms[perm].ContainsKey(task))
					{
						if (perms[perm][task] == true)
						{
							return true;
						}
					}
				}
			}
			else
			{
				// only users of Customers whose access hasn't been denied are permitted
				if (((ICustomerUser)user).HasSystemAccess == false)
				{
					return false;
				}
			}
			// test for non-privileged operations
			if (perms.ContainsKey("Public"))
			{
				if (perms["Public"].ContainsKey(task))
				{
					if (perms["Public"][task] == true)
					{
						return true;
					}
				}
			}
			return true;

		}

		public bool AttemptLogin(string username, string password)
		{
			IUser user = UserApplication.AttemptLogin(username, password);
			if (user != null)
			{
				user.LastLogin = DateTime.Now;
				UserApplication.Save(user);
				// System.Web.Security.FormsAuthentication.SetAuthCookie(user.Username, false);
				// FormsAuthentication.SetAuthCookie(user.Username, true);   this line throws null ref exception
				// HttpContext.Current.Items["CurrentUser"] = user;

				HttpContextAccessor.HttpContext.Items["CurrentUser"] = user;

				return true;
			}

			return false;
		}

		public bool HasSession()
		{
			return Identity != null;
		}

		public void AbandonSession()
		{
			//FormsAuthentication.SignOut();
			//HttpContext.Current.Items["CurrentUser"] = null;
		}

		public IUser Identity
		{
			get
			{
				try
				{
					if (HttpContextAccessor.HttpContext == null)
					{
						return (IUser)session.Get<IStaff>(1);
					}


					IUser user = HttpContextAccessor.HttpContext.Items["CurrentUser"] as IUser;
					if (user != null)
					{
						return user;
					}

					if (user == null && HttpContextAccessor.HttpContext.User.Identity.IsAuthenticated)
					{
						var identityName = HttpContextAccessor.HttpContext.User.Identity.Name;

						var userId = HttpContextAccessor.HttpContext.User.Claims.Where(c => c.Type == "UserId").Select(c => Convert.ToInt32(c.Value)).FirstOrDefault();
						user = session.Get<IUser>(userId);
						//.Where(u => u.Id == userId).List<IUser>();
						HttpContextAccessor.HttpContext.Items["CurrentUser"] = user;
						//UserApplication.GetUserByName(identityName);
						//if (users.Count > 0)
						//{
						//	user = (IUser)(users[0]);
						// NHibernateUtil.Initialize(user);
						//	var n = user.Username;
						//	HttpContextAccessor.HttpContext.Items["CurrentUser"] = user;
						//}
					}
					return (IUser)HttpContextAccessor.HttpContext.Items["CurrentUser"];

				}
				catch (Exception ex)
				{
					return null;
				}

			}
		}


		//public void AssertPermission (string operation)
		//{
		//    // TODO why is 'app' needed and what should it be?
		//    if (!HasTask("lep", operation)) {
		//        throw new LepSecurityException();
		//    }
		//}

		public void AssertIsCustomerUser(ICustomerUser customer)
		{
			IUser user = Identity as IUser;
			if (user == null)
			{
				throw new LepSecurityException("User is null");
			}

			if (!user.IsStaff && user.Id != customer.Id)
			{
				throw new LepSecurityException("Mismatched customer");
			}
		}

		public void DoPrivileged(PrivilegeDelegate func)
		{
			//var ctx = HttpContextAccessor.HttpContext;
			//var user = HttpContextAccessor.HttpContext != null ? ctx.User : Thread.CurrentPrincipal;
			//try {
			//    if (ctx != null) {
			//        ctx.User = (IPrincipal)GetSystemUser();
			//    } else {
			//        Thread.CurrentPrincipal = (IPrincipal)GetSystemUser();
			//    }

			//    Thread.CurrentPrincipal = (IPrincipal)GetSystemUser();
			func();
			//} finally {
			//    if (HttpContext.Current != null) {
			//        HttpContext.Current.User = user;
			//    } else {
			//        Thread.CurrentPrincipal = user;
			//    }
			//}
		}


		public IStaff GetSystemUser()
		{
			//return UserApplication.GetSystemUser();
			return (IStaff)session.Get<IStaff>(1);


		}


	}
}
