[2025-06-18 08:56:12.639 +10:00 INF] Starting LEP Invoicer application
[2025-06-18 08:56:12.667 +10:00 INF] Initializing FastReport...
[2025-06-18 08:56:12.741 +10:00 INF] FastReport initialized successfully
[2025-06-18 08:56:13.165 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-18 08:56:14.452 +10:00 INF] Starting LEP Invoicer at "2025-06-18T08:56:14.4520797+10:00"
[2025-06-18 08:56:14.455 +10:00 INF] Initializing database service...
[2025-06-18 08:56:14.458 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-18 08:56:14.558 +10:00 INF] Database connection established successfully
[2025-06-18 08:56:14.559 +10:00 INF] Database service initialized successfully
[2025-06-18 08:56:14.562 +10:00 INF] Checking for pending work...
[2025-06-18 08:56:14.566 +10:00 INF] Getting 500 orders to invoice
[2025-06-18 08:56:15.498 +10:00 INF] Found 0 orders to invoice (filtered 16 candidates)
[2025-06-18 08:56:15.501 +10:00 INF] Getting 100 credits to invoice
[2025-06-18 08:56:15.515 +10:00 INF] Found 0 credits to invoice
[2025-06-18 08:56:15.517 +10:00 INF] Getting 100 refunds to invoice
[2025-06-18 08:56:15.525 +10:00 INF] Found 0 refunds to invoice
[2025-06-18 08:56:15.528 +10:00 INF] No pending work found
[2025-06-18 08:56:15.530 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-18 08:56:15.532 +10:00 INF] Cleaning up invoicer logs...
[2025-06-18 08:56:15.615 +10:00 INF] LEP Invoicer completed successfully in 1162ms. No work to process.
[2025-06-18 08:56:15.621 +10:00 INF] Database connection disposed
[2025-06-18 08:56:15.623 +10:00 INF] LEP Invoicer completed with result: 0
