[2025-06-17 16:26:15.515 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 16:26:15.543 +10:00 INF] Initializing FastReport...
[2025-06-17 16:26:15.615 +10:00 INF] FastReport initialized successfully
[2025-06-17 16:26:16.019 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 16:26:17.352 +10:00 INF] Starting LEP Invoicer at "2025-06-17T16:26:17.3521554+10:00"
[2025-06-17 16:26:17.356 +10:00 INF] Initializing database service...
[2025-06-17 16:26:17.358 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 16:26:17.453 +10:00 INF] Database connection established successfully
[2025-06-17 16:26:17.454 +10:00 INF] Database service initialized successfully
[2025-06-17 16:26:17.457 +10:00 INF] Checking for pending work...
[2025-06-17 16:26:17.460 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 16:26:18.338 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 16:26:18.341 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 16:26:18.355 +10:00 INF] Found 0 credits to invoice
[2025-06-17 16:26:18.357 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 16:26:18.361 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 16:26:18.362 +10:00 INF] No pending work found
[2025-06-17 16:26:18.364 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 16:26:18.367 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 16:26:18.445 +10:00 INF] LEP Invoicer completed successfully in 1093ms. No work to process.
[2025-06-17 16:26:18.451 +10:00 INF] Database connection disposed
[2025-06-17 16:26:18.453 +10:00 INF] LEP Invoicer completed with result: 0
