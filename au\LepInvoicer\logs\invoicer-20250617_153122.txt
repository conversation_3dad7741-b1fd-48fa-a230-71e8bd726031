[2025-06-17 15:31:22.688 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 15:31:22.726 +10:00 INF] Initializing FastReport...
[2025-06-17 15:31:22.801 +10:00 INF] FastReport initialized successfully
[2025-06-17 15:31:23.251 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 15:31:24.514 +10:00 INF] Starting LEP Invoicer at "2025-06-17T15:31:24.5144498+10:00"
[2025-06-17 15:31:24.518 +10:00 INF] Initializing database service...
[2025-06-17 15:31:24.521 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 15:31:24.620 +10:00 INF] Database connection established successfully
[2025-06-17 15:31:24.622 +10:00 INF] Database service initialized successfully
[2025-06-17 15:31:24.624 +10:00 INF] Checking for pending work...
[2025-06-17 15:31:24.628 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 15:31:25.679 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 15:31:25.681 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 15:31:25.696 +10:00 INF] Found 0 credits to invoice
[2025-06-17 15:31:25.698 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 15:31:25.703 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 15:31:25.705 +10:00 INF] No pending work found
[2025-06-17 15:31:25.707 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 15:31:25.711 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 15:31:25.791 +10:00 INF] LEP Invoicer completed successfully in 1276ms. No work to process.
[2025-06-17 15:31:25.799 +10:00 INF] Database connection disposed
[2025-06-17 15:31:25.801 +10:00 INF] LEP Invoicer completed with result: 0
