using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using AutoMapper;
using Newtonsoft.Json;
using FastReport;
using FastReport.Export.PdfSimple;
using LepInvoicer.Implementations;

namespace LepInvoicer.Implementations;

/// <summary>
/// PDF generation service implementation
/// </summary>
public class PdfService : LepInvoicer.Interfaces.IPdfService
{
    private readonly ILogger<PdfService> _logger;
    private readonly InvoicerConfiguration _config;
    private readonly IMapper _mapper;

    public PdfService(ILogger<PdfService> logger, IOptions<InvoicerConfiguration> config, IMapper mapper)
    {
        _logger = logger;
        _config = config.Value;
        _mapper = mapper;

        // Configure FastReport settings (matching LinqPad script)
        // Note: Some settings may not be available in FastReport OpenSource
        try
        {
            FastReport.Utils.Config.FontListFolder = @"C:\LEPDATA\FONTS";
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to configure FastReport font folder - using defaults");
        }
    }

    public Task GenerateOrderInvoicePdf(IOrder order, string outputPath)
    {
        try
        {
            _logger.LogInformation("Generating PDF invoice for order {OrderId} at {OutputPath}", order.Id, outputPath);

            // Ensure directory exists
            InvoicerUtilities.EnsureDirectoryExists(Path.GetDirectoryName(outputPath));

            // Map order to DTO for FastReport (matching LinqPad script)
            var orderDto = _mapper.Map<OrderInvoiceDto>(order);
            var jsonStr = JsonConvert.SerializeObject(orderDto, Formatting.Indented);
            jsonStr = jsonStr.Replace("'", "''"); // Escape single quotes for SQL

            // Create FastReport instance
            var report = new Report();
            var templatePath = @"C:\LepData\Labels2\lep-invoice-order.frx";

            if (!File.Exists(templatePath))
            {
                throw new FileNotFoundException($"FastReport template not found: {templatePath}");
            }

            report.Load(templatePath);

            // Set JSON data source (matching LinqPad script)
            report.Dictionary.Connections[0].ConnectionString = $"Json='{jsonStr}'";
            report.Prepare();

            // Export to PDF
            var pdfExport = new PDFSimpleExport();
            report.Export(pdfExport, outputPath);

            _logger.LogInformation("Successfully generated PDF for order {OrderId}", order.Id);
            return Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to generate PDF for order {OrderId}", order.Id);
            throw;
        }
    }

    public Task<string> GenerateCreditInvoicePdfAsync(OrderCredit orderCredit, string outputPath)
    {
        try
        {
            _logger.LogInformation("Generating PDF credit invoice for credit {CreditId} at {OutputPath}", orderCredit.Id, outputPath);

            // Ensure directory exists
            InvoicerUtilities.EnsureDirectoryExists(Path.GetDirectoryName(outputPath));

            // Map credit to DTO for FastReport (matching LinqPad script)
            var creditDto = _mapper.Map<CustomerRefundDto>(orderCredit);
            var jsonStr = JsonConvert.SerializeObject(creditDto, Formatting.Indented);
            jsonStr = jsonStr.Replace("'", "''"); // Escape single quotes for SQL

            // Create FastReport instance
            var report = new Report();
            var templatePath = @"c:\LepData\Labels2\lep-invoice-refund.frx";

            if (!File.Exists(templatePath))
            {
                throw new FileNotFoundException($"FastReport template not found: {templatePath}");
            }

            report.Load(templatePath);

            // Set JSON data source (matching LinqPad script)
            report.Dictionary.Connections[0].ConnectionString = $"Json='{jsonStr}'";
            report.Prepare();

            // Export to PDF
            var pdfExport = new PDFSimpleExport();
            report.Export(pdfExport, outputPath);

            _logger.LogInformation("Successfully generated PDF for credit {CreditId}", orderCredit.Id);
            return Task.FromResult(outputPath);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to generate PDF for credit {CreditId}", orderCredit.Id);
            throw;
        }
    }
}
