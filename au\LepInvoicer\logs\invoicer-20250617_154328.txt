[2025-06-17 15:43:28.660 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 15:43:28.689 +10:00 INF] Initializing FastReport...
[2025-06-17 15:43:28.766 +10:00 INF] FastReport initialized successfully
[2025-06-17 15:43:29.252 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 15:43:30.620 +10:00 INF] Starting LEP Invoicer at "2025-06-17T15:43:30.6201247+10:00"
[2025-06-17 15:43:30.624 +10:00 INF] Initializing database service...
[2025-06-17 15:43:30.627 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 15:43:30.722 +10:00 INF] Database connection established successfully
[2025-06-17 15:43:30.724 +10:00 INF] Database service initialized successfully
[2025-06-17 15:43:30.726 +10:00 INF] Checking for pending work...
[2025-06-17 15:43:30.729 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 15:43:31.671 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 15:43:31.674 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 15:43:31.688 +10:00 INF] Found 0 credits to invoice
[2025-06-17 15:43:31.690 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 15:43:31.695 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 15:43:31.700 +10:00 INF] No pending work found
[2025-06-17 15:43:31.702 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 15:43:31.704 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 15:43:31.784 +10:00 INF] LEP Invoicer completed successfully in 1164ms. No work to process.
[2025-06-17 15:43:31.790 +10:00 INF] Database connection disposed
[2025-06-17 15:43:31.792 +10:00 INF] LEP Invoicer completed with result: 0
