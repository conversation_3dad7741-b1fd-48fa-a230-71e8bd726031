[2025-06-17 18:09:53.486 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 18:09:53.514 +10:00 INF] Initializing FastReport...
[2025-06-17 18:09:53.586 +10:00 INF] FastReport initialized successfully
[2025-06-17 18:09:53.990 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 18:09:55.351 +10:00 INF] Starting LEP Invoicer at "2025-06-17T18:09:55.3513966+10:00"
[2025-06-17 18:09:55.356 +10:00 INF] Initializing database service...
[2025-06-17 18:09:55.359 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 18:09:55.458 +10:00 INF] Database connection established successfully
[2025-06-17 18:09:55.459 +10:00 INF] Database service initialized successfully
[2025-06-17 18:09:55.462 +10:00 INF] Checking for pending work...
[2025-06-17 18:09:55.465 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 18:09:56.337 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 18:09:56.340 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 18:09:56.352 +10:00 INF] Found 0 credits to invoice
[2025-06-17 18:09:56.354 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 18:09:56.358 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 18:09:56.359 +10:00 INF] No pending work found
[2025-06-17 18:09:56.360 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 18:09:56.361 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 18:09:56.435 +10:00 INF] LEP Invoicer completed successfully in 1083ms. No work to process.
[2025-06-17 18:09:56.441 +10:00 INF] Database connection disposed
[2025-06-17 18:09:56.442 +10:00 INF] LEP Invoicer completed with result: 0
