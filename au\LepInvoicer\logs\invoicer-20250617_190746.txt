[2025-06-17 19:07:46.548 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 19:07:46.575 +10:00 INF] Initializing FastReport...
[2025-06-17 19:07:46.660 +10:00 INF] FastReport initialized successfully
[2025-06-17 19:07:47.073 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 19:07:48.421 +10:00 INF] Starting LEP Invoicer at "2025-06-17T19:07:48.4215932+10:00"
[2025-06-17 19:07:48.425 +10:00 INF] Initializing database service...
[2025-06-17 19:07:48.427 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 19:07:48.524 +10:00 INF] Database connection established successfully
[2025-06-17 19:07:48.525 +10:00 INF] Database service initialized successfully
[2025-06-17 19:07:48.528 +10:00 INF] Checking for pending work...
[2025-06-17 19:07:48.531 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 19:07:49.405 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 19:07:49.408 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 19:07:49.420 +10:00 INF] Found 0 credits to invoice
[2025-06-17 19:07:49.422 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 19:07:49.425 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 19:07:49.427 +10:00 INF] No pending work found
[2025-06-17 19:07:49.429 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 19:07:49.430 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 19:07:49.504 +10:00 INF] LEP Invoicer completed successfully in 1083ms. No work to process.
[2025-06-17 19:07:49.511 +10:00 INF] Database connection disposed
[2025-06-17 19:07:49.513 +10:00 INF] LEP Invoicer completed with result: 0
