[2025-06-17 18:43:44.474 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 18:43:44.504 +10:00 INF] Initializing FastReport...
[2025-06-17 18:43:44.577 +10:00 INF] FastReport initialized successfully
[2025-06-17 18:43:44.988 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 18:43:46.261 +10:00 INF] Starting LEP Invoicer at "2025-06-17T18:43:46.2609665+10:00"
[2025-06-17 18:43:46.264 +10:00 INF] Initializing database service...
[2025-06-17 18:43:46.277 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 18:43:46.402 +10:00 INF] Database connection established successfully
[2025-06-17 18:43:46.406 +10:00 INF] Database service initialized successfully
[2025-06-17 18:43:46.412 +10:00 INF] Checking for pending work...
[2025-06-17 18:43:46.418 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 18:43:47.364 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 18:43:47.366 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 18:43:47.378 +10:00 INF] Found 0 credits to invoice
[2025-06-17 18:43:47.380 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 18:43:47.384 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 18:43:47.386 +10:00 INF] No pending work found
[2025-06-17 18:43:47.388 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 18:43:47.389 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 18:43:47.492 +10:00 INF] LEP Invoicer completed successfully in 1231ms. No work to process.
[2025-06-17 18:43:47.498 +10:00 INF] Database connection disposed
[2025-06-17 18:43:47.500 +10:00 INF] LEP Invoicer completed with result: 0
