[2025-06-17 17:41:34.284 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 17:41:34.313 +10:00 INF] Initializing FastReport...
[2025-06-17 17:41:34.391 +10:00 INF] FastReport initialized successfully
[2025-06-17 17:41:34.888 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 17:41:36.354 +10:00 INF] Starting LEP Invoicer at "2025-06-17T17:41:36.3540100+10:00"
[2025-06-17 17:41:36.359 +10:00 INF] Initializing database service...
[2025-06-17 17:41:36.362 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 17:41:36.470 +10:00 INF] Database connection established successfully
[2025-06-17 17:41:36.473 +10:00 INF] Database service initialized successfully
[2025-06-17 17:41:36.476 +10:00 INF] Checking for pending work...
[2025-06-17 17:41:36.480 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 17:41:37.446 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 17:41:37.449 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 17:41:37.461 +10:00 INF] Found 0 credits to invoice
[2025-06-17 17:41:37.463 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 17:41:37.467 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 17:41:37.468 +10:00 INF] No pending work found
[2025-06-17 17:41:37.470 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 17:41:37.471 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 17:41:37.550 +10:00 INF] LEP Invoicer completed successfully in 1196ms. No work to process.
[2025-06-17 17:41:37.557 +10:00 INF] Database connection disposed
[2025-06-17 17:41:37.559 +10:00 INF] LEP Invoicer completed with result: 0
