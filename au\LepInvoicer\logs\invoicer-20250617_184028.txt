[2025-06-17 18:40:28.478 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 18:40:28.507 +10:00 INF] Initializing FastReport...
[2025-06-17 18:40:28.589 +10:00 INF] FastReport initialized successfully
[2025-06-17 18:40:29.016 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 18:40:30.394 +10:00 INF] Starting LEP Invoicer at "2025-06-17T18:40:30.3942593+10:00"
[2025-06-17 18:40:30.400 +10:00 INF] Initializing database service...
[2025-06-17 18:40:30.403 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 18:40:30.505 +10:00 INF] Database connection established successfully
[2025-06-17 18:40:30.507 +10:00 INF] Database service initialized successfully
[2025-06-17 18:40:30.510 +10:00 INF] Checking for pending work...
[2025-06-17 18:40:30.514 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 18:40:31.410 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 18:40:31.412 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 18:40:31.425 +10:00 INF] Found 0 credits to invoice
[2025-06-17 18:40:31.427 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 18:40:31.432 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 18:40:31.434 +10:00 INF] No pending work found
[2025-06-17 18:40:31.436 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 18:40:31.438 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 18:40:31.516 +10:00 INF] LEP Invoicer completed successfully in 1122ms. No work to process.
[2025-06-17 18:40:31.523 +10:00 INF] Database connection disposed
[2025-06-17 18:40:31.524 +10:00 INF] LEP Invoicer completed with result: 0
