namespace LepInvoicer.Implementations;

/// <summary>
/// Configuration settings for the LEP Invoicer application
/// </summary>
public class InvoicerConfiguration
{
	/// <summary>
	/// Database connection string for LEP system
	/// </summary>
	public string ConnectionString { get; set; } = "Data Source=SRV03;user id=sa; password=*************; Initial Catalog=PRD_AU";

	/// <summary>
	/// Number of orders to process in each batch
	/// </summary>
	public int InvoiceBatchSize { get; set; } = 20;

	/// <summary>
	/// Number of refunds to process in each batch
	/// </summary>
	public int RefundBatchSize { get; set; } = 10;

	/// <summary>
	/// Whether to create order invoices
	/// </summary>
	public bool CreateOrderInvoice { get; set; } = true;

	/// <summary>
	/// Whether to create refund invoices
	/// </summary>
	public bool CreateRefundInvoice { get; set; } = true;

	/// <summary>
	/// Whether to create PDF invoices
	/// </summary>
	public bool CreatePdfInvoice { get; set; } = true;

	/// <summary>
	/// Test mode - bypasses MYOB initialization for testing
	/// </summary>
	public bool TestMode { get; set; } = false;

	/// <summary>
	/// Whether to email PDF invoices
	/// </summary>
	public bool EmailPdfInvoice { get; set; } = true;

	/// <summary>
	/// Override email address for testing (optional)
	/// </summary>
	public string EmailPdfAddress { get; set; }

	/// <summary>
	/// Data directory for file storage
	/// </summary>
	public string DataDirectoryFullName { get; set; } = @"\\dfs01\resource";

	/// <summary>
	/// PDF folder for invoice storage
	/// </summary>
	public string PdfFolder { get; set; } = @"\\dfs01\resource\invoices";

	/// <summary>
	/// Font folder for FastReport
	/// </summary>
	public string FontListFolder { get; set; } = "C:\\LEPDATA\\FONTS";

	/// <summary>
	/// Date format for database operations
	/// </summary>
	public string DateFormat { get; set; } = "yyyy-MM-dd HH:mm:ss";

	/// <summary>
	/// SMTP configuration
	/// </summary>
	public SmtpConfiguration Smtp { get; set; } = new();

	/// <summary>
	/// MYOB configuration
	/// </summary>
	public MYOBConfiguration MYOB { get; set; } = new();

	/// <summary>
	/// List of customers to ignore during invoicing
	/// </summary>
	public List<string> IgnoreCustomers { get; set; } = new()
	{
		"LEP Colour Printers Pty Ltd",
		"LEP Marketing",
		"LEP TEST J",
		"LEP TEST T",
		"lepdemo"
	};

	/// <summary>
	/// Minimum finish date for orders to process
	/// </summary>
	public DateTime MinimumFinishDate { get; set; } = DateTime.Parse("2024/02/01");
}

/// <summary>
/// SMTP email configuration
/// </summary>
public class SmtpConfiguration
{
	public string Host { get; set; } = "smtp.office365.com";
	public int Port { get; set; } = 587;
	public bool EnableSsl { get; set; } = true;
	public string Username { get; set; } = "<EMAIL>";
	public string Password { get; set; } = "Kor72522";
	public string FromAddress { get; set; } = "<EMAIL>";
	public string FromName { get; set; } = "LEP Colour Printers";
}

/// <summary>
/// MYOB API configuration
/// </summary>
public class MYOBConfiguration
{
	public string DeveloperKey { get; set; } = "";
	public string DeveloperSecret { get; set; } = "";
	public string ConfirmationUrl { get; set; } = "";
	public string CompanyFileName { get; set; } = "LEP Colour Printers Pty Ltd";
}
