[2025-06-17 17:31:42.535 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 17:31:42.564 +10:00 INF] Initializing FastReport...
[2025-06-17 17:31:42.636 +10:00 INF] FastReport initialized successfully
[2025-06-17 17:31:43.066 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 17:31:44.330 +10:00 INF] Starting LEP Invoicer at "2025-06-17T17:31:44.3300889+10:00"
[2025-06-17 17:31:44.333 +10:00 INF] Initializing database service...
[2025-06-17 17:31:44.337 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 17:31:44.445 +10:00 INF] Database connection established successfully
[2025-06-17 17:31:44.447 +10:00 INF] Database service initialized successfully
[2025-06-17 17:31:44.449 +10:00 INF] Checking for pending work...
[2025-06-17 17:31:44.452 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 17:31:45.339 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 17:31:45.342 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 17:31:45.354 +10:00 INF] Found 0 credits to invoice
[2025-06-17 17:31:45.356 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 17:31:45.359 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 17:31:45.360 +10:00 INF] No pending work found
[2025-06-17 17:31:45.362 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 17:31:45.363 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 17:31:45.443 +10:00 INF] LEP Invoicer completed successfully in 1113ms. No work to process.
[2025-06-17 17:31:45.449 +10:00 INF] Database connection disposed
[2025-06-17 17:31:45.451 +10:00 INF] LEP Invoicer completed with result: 0
