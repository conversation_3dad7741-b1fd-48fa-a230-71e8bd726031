[2025-06-17 16:22:58.407 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 16:22:58.435 +10:00 INF] Initializing FastReport...
[2025-06-17 16:22:58.523 +10:00 INF] FastReport initialized successfully
[2025-06-17 16:22:58.890 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 16:23:00.093 +10:00 INF] Starting LEP Invoicer at "2025-06-17T16:23:00.0927328+10:00"
[2025-06-17 16:23:00.097 +10:00 INF] Initializing database service...
[2025-06-17 16:23:00.099 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 16:23:00.192 +10:00 INF] Database connection established successfully
[2025-06-17 16:23:00.194 +10:00 INF] Database service initialized successfully
[2025-06-17 16:23:00.196 +10:00 INF] Checking for pending work...
[2025-06-17 16:23:00.199 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 16:23:01.137 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 16:23:01.140 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 16:23:01.154 +10:00 INF] Found 0 credits to invoice
[2025-06-17 16:23:01.156 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 16:23:01.163 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 16:23:01.165 +10:00 INF] No pending work found
[2025-06-17 16:23:01.166 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 16:23:01.169 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 16:23:01.251 +10:00 INF] LEP Invoicer completed successfully in 1158ms. No work to process.
[2025-06-17 16:23:01.258 +10:00 INF] Database connection disposed
[2025-06-17 16:23:01.260 +10:00 INF] LEP Invoicer completed with result: 0
