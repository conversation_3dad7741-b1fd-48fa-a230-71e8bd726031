[2025-06-17 18:44:49.464 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 18:44:49.497 +10:00 INF] Initializing FastReport...
[2025-06-17 18:44:49.569 +10:00 INF] FastReport initialized successfully
[2025-06-17 18:44:49.995 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 18:44:51.289 +10:00 INF] Starting LEP Invoicer at "2025-06-17T18:44:51.2891801+10:00"
[2025-06-17 18:44:51.305 +10:00 INF] Initializing database service...
[2025-06-17 18:44:51.308 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 18:44:51.407 +10:00 INF] Database connection established successfully
[2025-06-17 18:44:51.408 +10:00 INF] Database service initialized successfully
[2025-06-17 18:44:51.412 +10:00 INF] Checking for pending work...
[2025-06-17 18:44:51.414 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 18:44:52.295 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 18:44:52.298 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 18:44:52.314 +10:00 INF] Found 0 credits to invoice
[2025-06-17 18:44:52.316 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 18:44:52.319 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 18:44:52.331 +10:00 INF] No pending work found
[2025-06-17 18:44:52.335 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 18:44:52.336 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 18:44:52.412 +10:00 INF] LEP Invoicer completed successfully in 1123ms. No work to process.
[2025-06-17 18:44:52.420 +10:00 INF] Database connection disposed
[2025-06-17 18:44:52.421 +10:00 INF] LEP Invoicer completed with result: 0
