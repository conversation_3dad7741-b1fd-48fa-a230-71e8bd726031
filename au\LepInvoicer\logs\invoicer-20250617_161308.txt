[2025-06-17 16:13:08.087 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 16:13:08.127 +10:00 INF] Initializing FastReport...
[2025-06-17 16:13:08.213 +10:00 INF] FastReport initialized successfully
[2025-06-17 16:13:08.821 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 16:13:10.832 +10:00 INF] Starting LEP Invoicer at "2025-06-17T16:13:10.8319738+10:00"
[2025-06-17 16:13:10.836 +10:00 INF] Initializing database service...
[2025-06-17 16:13:10.839 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 16:13:11.017 +10:00 INF] Database connection established successfully
[2025-06-17 16:13:11.019 +10:00 INF] Database service initialized successfully
[2025-06-17 16:13:11.022 +10:00 INF] Checking for pending work...
[2025-06-17 16:13:11.026 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 16:13:12.399 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 16:13:12.402 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 16:13:12.416 +10:00 INF] Found 0 credits to invoice
[2025-06-17 16:13:12.419 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 16:13:12.423 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 16:13:12.424 +10:00 INF] No pending work found
[2025-06-17 16:13:12.426 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 16:13:12.427 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 16:13:12.506 +10:00 INF] LEP Invoicer completed successfully in 1674ms. No work to process.
[2025-06-17 16:13:12.513 +10:00 INF] Database connection disposed
[2025-06-17 16:13:12.515 +10:00 INF] LEP Invoicer completed with result: 0
