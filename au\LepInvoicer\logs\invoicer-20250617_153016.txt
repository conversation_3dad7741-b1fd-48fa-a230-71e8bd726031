[2025-06-17 15:30:16.655 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 15:30:16.689 +10:00 INF] Initializing FastReport...
[2025-06-17 15:30:16.776 +10:00 INF] FastReport initialized successfully
[2025-06-17 15:30:17.258 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 15:30:18.589 +10:00 INF] Starting LEP Invoicer at "2025-06-17T15:30:18.5894217+10:00"
[2025-06-17 15:30:18.599 +10:00 INF] Initializing database service...
[2025-06-17 15:30:18.602 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 15:30:18.714 +10:00 INF] Database connection established successfully
[2025-06-17 15:30:18.715 +10:00 INF] Database service initialized successfully
[2025-06-17 15:30:18.718 +10:00 INF] Checking for pending work...
[2025-06-17 15:30:18.722 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 15:30:19.622 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 15:30:19.625 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 15:30:19.636 +10:00 INF] Found 0 credits to invoice
[2025-06-17 15:30:19.638 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 15:30:19.642 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 15:30:19.644 +10:00 INF] No pending work found
[2025-06-17 15:30:19.645 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 15:30:19.646 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 15:30:19.725 +10:00 INF] LEP Invoicer completed successfully in 1135ms. No work to process.
[2025-06-17 15:30:19.732 +10:00 INF] Database connection disposed
[2025-06-17 15:30:19.734 +10:00 INF] LEP Invoicer completed with result: 0
