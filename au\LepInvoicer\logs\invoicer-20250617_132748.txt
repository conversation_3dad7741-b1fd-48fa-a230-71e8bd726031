[2025-06-17 13:27:48.085 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 13:27:48.113 +10:00 INF] Initializing FastReport...
[2025-06-17 13:27:48.184 +10:00 INF] FastReport initialized successfully
[2025-06-17 13:27:48.731 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 13:27:50.212 +10:00 INF] Starting LEP Invoicer at "2025-06-17T13:27:50.2125058+10:00"
[2025-06-17 13:27:50.216 +10:00 INF] Initializing database service...
[2025-06-17 13:27:50.219 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 13:27:50.324 +10:00 INF] Database connection established successfully
[2025-06-17 13:27:50.325 +10:00 INF] Database service initialized successfully
[2025-06-17 13:27:50.328 +10:00 INF] Checking for pending work...
[2025-06-17 13:27:50.330 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 13:27:51.563 +10:00 INF] Found 17 orders to invoice (filtered 32 candidates)
[2025-06-17 13:27:51.566 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 13:27:51.637 +10:00 INF] Found 0 credits to invoice
[2025-06-17 13:27:51.644 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 13:27:51.721 +10:00 INF] Found 16 refunds to invoice
[2025-06-17 13:27:51.723 +10:00 INF] Found pending work: 17 orders, 16 refunds
[2025-06-17 13:27:51.726 +10:00 INF] Initializing MYOB and other services...
[2025-06-17 13:27:51.729 +10:00 INF] Initializing MYOB service...
[2025-06-17 13:27:51.733 +10:00 INF] Initializing MYOB with OAuth authentication
[2025-06-17 13:27:51.737 +10:00 INF] Starting OAuth authentication flow
[2025-06-17 13:27:51.751 +10:00 INF] Using existing OAuth tokens
[2025-06-17 13:27:51.755 +10:00 INF] Initializing MYOB services with OAuth keystore
[2025-06-17 13:27:51.761 +10:00 INF] MYOB services initialized successfully
[2025-06-17 13:27:51.764 +10:00 INF] OAuth keystore set for API calls
[2025-06-17 13:27:51.765 +10:00 INF] Getting company files from MYOB
[2025-06-17 13:27:52.383 +10:00 INF] MYOB service initialized with OAuth credentials. Using company file: LEP Colour Printers Pty Ltd
[2025-06-17 13:27:53.431 +10:00 INF] MYOB service initialized successfully with company file: LEP Colour Printers Pty Ltd
[2025-06-17 13:27:53.435 +10:00 INF] All services initialized successfully
[2025-06-17 13:27:53.445 +10:00 INF] Processing order invoices...
[2025-06-17 13:27:53.447 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 13:27:53.796 +10:00 INF] Found 17 orders to invoice (filtered 32 candidates)
[2025-06-17 13:27:53.799 +10:00 INF] Found 17 orders to process
[2025-06-17 13:27:53.803 +10:00 INF] Getting order 1418215
[2025-06-17 13:27:53.847 +10:00 INF] Processing order 1418215 with 1 jobs, total: ¤80.30
[2025-06-17 13:27:53.858 +10:00 INF] Creating order invoice for order 1418215
[2025-06-17 13:27:55.592 +10:00 INF] Successfully created MYOB invoice O1418215 for order 1418215
[2025-06-17 13:27:55.594 +10:00 INF] Successfully created MYOB invoice for order 1418215
[2025-06-17 13:27:55.599 +10:00 INF] Generating PDF invoice for order 1418215 at \\dfs01\resource\invoices\2025/Jun/17\O1418215.pdf
[2025-06-17 13:27:57.529 +10:00 INF] Successfully generated PDF for order 1418215
[2025-06-17 13:27:57.567 +10:00 WRN] No email address found for order 1418215
[2025-06-17 13:27:57.574 +10:00 INF] Successfully processed order 1418215 (MYOB + PDF)
[2025-06-17 13:27:57.578 +10:00 INF] Getting order 1418087
[2025-06-17 13:27:57.586 +10:00 INF] Processing order 1418087 with 1 jobs, total: ¤57.84
[2025-06-17 13:27:57.591 +10:00 INF] Creating order invoice for order 1418087
[2025-06-17 13:27:59.040 +10:00 INF] Successfully created MYOB invoice O1418087 for order 1418087
[2025-06-17 13:27:59.041 +10:00 INF] Successfully created MYOB invoice for order 1418087
[2025-06-17 13:27:59.043 +10:00 INF] Generating PDF invoice for order 1418087 at \\dfs01\resource\invoices\2025/Jun/17\O1418087.pdf
[2025-06-17 13:27:59.368 +10:00 INF] Successfully generated PDF for order 1418087
[2025-06-17 13:27:59.390 +10:00 WRN] No email address found for order 1418087
[2025-06-17 13:27:59.393 +10:00 INF] Successfully processed order 1418087 (MYOB + PDF)
[2025-06-17 13:27:59.396 +10:00 INF] Getting order 1418066
[2025-06-17 13:27:59.401 +10:00 INF] Processing order 1418066 with 1 jobs, total: ¤82.06
[2025-06-17 13:27:59.403 +10:00 INF] Creating order invoice for order 1418066
[2025-06-17 13:28:00.874 +10:00 INF] Successfully created MYOB invoice O1418066 for order 1418066
[2025-06-17 13:28:00.876 +10:00 INF] Successfully created MYOB invoice for order 1418066
[2025-06-17 13:28:00.878 +10:00 INF] Generating PDF invoice for order 1418066 at \\dfs01\resource\invoices\2025/Jun/17\O1418066.pdf
[2025-06-17 13:28:01.268 +10:00 INF] Successfully generated PDF for order 1418066
[2025-06-17 13:28:01.293 +10:00 WRN] No email address found for order 1418066
[2025-06-17 13:28:01.296 +10:00 INF] Successfully processed order 1418066 (MYOB + PDF)
[2025-06-17 13:28:01.326 +10:00 INF] Getting order 1418032
[2025-06-17 13:28:01.342 +10:00 INF] Processing order 1418032 with 1 jobs, total: ¤58.53
[2025-06-17 13:28:01.345 +10:00 INF] Creating order invoice for order 1418032
[2025-06-17 13:28:02.798 +10:00 INF] Successfully created MYOB invoice O1418032 for order 1418032
[2025-06-17 13:28:02.800 +10:00 INF] Successfully created MYOB invoice for order 1418032
[2025-06-17 13:28:02.802 +10:00 INF] Generating PDF invoice for order 1418032 at \\dfs01\resource\invoices\2025/Jun/17\O1418032.pdf
[2025-06-17 13:28:03.181 +10:00 INF] Successfully generated PDF for order 1418032
[2025-06-17 13:28:03.205 +10:00 WRN] No email address found for order 1418032
[2025-06-17 13:28:03.208 +10:00 INF] Successfully processed order 1418032 (MYOB + PDF)
[2025-06-17 13:28:03.216 +10:00 INF] Getting order 1417982
[2025-06-17 13:28:03.222 +10:00 INF] Processing order 1417982 with 2 jobs, total: ¤123.53
[2025-06-17 13:28:03.224 +10:00 INF] Creating order invoice for order 1417982
[2025-06-17 13:28:06.102 +10:00 INF] Successfully created MYOB invoice O1417982 for order 1417982
[2025-06-17 13:28:06.104 +10:00 INF] Successfully created MYOB invoice for order 1417982
[2025-06-17 13:28:06.106 +10:00 INF] Generating PDF invoice for order 1417982 at \\dfs01\resource\invoices\2025/Jun/17\O1417982.pdf
[2025-06-17 13:28:06.473 +10:00 INF] Successfully generated PDF for order 1417982
[2025-06-17 13:28:06.492 +10:00 WRN] No email address found for order 1417982
[2025-06-17 13:28:06.497 +10:00 INF] Successfully processed order 1417982 (MYOB + PDF)
[2025-06-17 13:28:06.499 +10:00 INF] Getting order 1417979
[2025-06-17 13:28:06.505 +10:00 INF] Processing order 1417979 with 3 jobs, total: ¤155.06
[2025-06-17 13:28:06.507 +10:00 INF] Creating order invoice for order 1417979
[2025-06-17 13:28:08.210 +10:00 INF] Successfully created MYOB invoice O1417979 for order 1417979
[2025-06-17 13:28:08.211 +10:00 INF] Successfully created MYOB invoice for order 1417979
[2025-06-17 13:28:08.213 +10:00 INF] Generating PDF invoice for order 1417979 at \\dfs01\resource\invoices\2025/Jun/17\O1417979.pdf
[2025-06-17 13:28:08.642 +10:00 INF] Successfully generated PDF for order 1417979
[2025-06-17 13:28:08.665 +10:00 WRN] No email address found for order 1417979
[2025-06-17 13:28:08.668 +10:00 INF] Successfully processed order 1417979 (MYOB + PDF)
[2025-06-17 13:28:08.707 +10:00 INF] Getting order 1417957
[2025-06-17 13:28:08.712 +10:00 INF] Processing order 1417957 with 2 jobs, total: ¤87.13
[2025-06-17 13:28:08.714 +10:00 INF] Creating order invoice for order 1417957
[2025-06-17 13:28:10.439 +10:00 INF] Successfully created MYOB invoice O1417957 for order 1417957
[2025-06-17 13:28:10.441 +10:00 INF] Successfully created MYOB invoice for order 1417957
[2025-06-17 13:28:10.448 +10:00 INF] Generating PDF invoice for order 1417957 at \\dfs01\resource\invoices\2025/Jun/17\O1417957.pdf
[2025-06-17 13:28:10.847 +10:00 INF] Successfully generated PDF for order 1417957
[2025-06-17 13:28:10.872 +10:00 WRN] No email address found for order 1417957
[2025-06-17 13:28:10.876 +10:00 INF] Successfully processed order 1417957 (MYOB + PDF)
[2025-06-17 13:28:10.879 +10:00 INF] Getting order 1417911
[2025-06-17 13:28:10.883 +10:00 INF] Processing order 1417911 with 1 jobs, total: ¤59.69
[2025-06-17 13:28:10.886 +10:00 INF] Creating order invoice for order 1417911
[2025-06-17 13:28:11.956 +10:00 INF] Successfully created MYOB invoice O1417911 for order 1417911
[2025-06-17 13:28:11.970 +10:00 INF] Successfully created MYOB invoice for order 1417911
[2025-06-17 13:28:11.971 +10:00 INF] Generating PDF invoice for order 1417911 at \\dfs01\resource\invoices\2025/Jun/17\O1417911.pdf
[2025-06-17 13:28:12.314 +10:00 INF] Successfully generated PDF for order 1417911
[2025-06-17 13:28:12.342 +10:00 WRN] No email address found for order 1417911
[2025-06-17 13:28:12.346 +10:00 INF] Successfully processed order 1417911 (MYOB + PDF)
[2025-06-17 13:28:12.349 +10:00 INF] Getting order 1417861
[2025-06-17 13:28:12.354 +10:00 INF] Processing order 1417861 with 1 jobs, total: ¤103.00
[2025-06-17 13:28:12.357 +10:00 INF] Creating order invoice for order 1417861
[2025-06-17 13:28:13.490 +10:00 INF] Successfully created MYOB invoice O1417861 for order 1417861
[2025-06-17 13:28:13.492 +10:00 INF] Successfully created MYOB invoice for order 1417861
[2025-06-17 13:28:13.494 +10:00 INF] Generating PDF invoice for order 1417861 at \\dfs01\resource\invoices\2025/Jun/17\O1417861.pdf
[2025-06-17 13:28:13.845 +10:00 INF] Successfully generated PDF for order 1417861
[2025-06-17 13:28:13.869 +10:00 WRN] No email address found for order 1417861
[2025-06-17 13:28:13.872 +10:00 INF] Successfully processed order 1417861 (MYOB + PDF)
[2025-06-17 13:28:13.875 +10:00 INF] Getting order 1417856
[2025-06-17 13:28:13.879 +10:00 INF] Processing order 1417856 with 1 jobs, total: ¤62.21
[2025-06-17 13:28:13.882 +10:00 INF] Creating order invoice for order 1417856
[2025-06-17 13:28:15.866 +10:00 INF] Successfully created MYOB invoice O1417856 for order 1417856
[2025-06-17 13:28:15.867 +10:00 INF] Successfully created MYOB invoice for order 1417856
[2025-06-17 13:28:15.870 +10:00 INF] Generating PDF invoice for order 1417856 at \\dfs01\resource\invoices\2025/Jun/17\O1417856.pdf
[2025-06-17 13:28:16.199 +10:00 INF] Successfully generated PDF for order 1417856
[2025-06-17 13:28:16.250 +10:00 WRN] No email address found for order 1417856
[2025-06-17 13:28:16.254 +10:00 INF] Successfully processed order 1417856 (MYOB + PDF)
[2025-06-17 13:28:16.261 +10:00 INF] Getting order 1417834
[2025-06-17 13:28:16.267 +10:00 INF] Processing order 1417834 with 1 jobs, total: ¤36.56
[2025-06-17 13:28:16.271 +10:00 INF] Creating order invoice for order 1417834
[2025-06-17 13:28:21.573 +10:00 INF] Successfully created MYOB invoice O1417834 for order 1417834
[2025-06-17 13:28:21.589 +10:00 INF] Successfully created MYOB invoice for order 1417834
[2025-06-17 13:28:21.593 +10:00 INF] Generating PDF invoice for order 1417834 at \\dfs01\resource\invoices\2025/Jun/17\O1417834.pdf
[2025-06-17 13:28:22.020 +10:00 INF] Successfully generated PDF for order 1417834
[2025-06-17 13:28:22.057 +10:00 WRN] No email address found for order 1417834
[2025-06-17 13:28:22.061 +10:00 INF] Successfully processed order 1417834 (MYOB + PDF)
[2025-06-17 13:28:22.064 +10:00 INF] Getting order 1417742
[2025-06-17 13:28:22.068 +10:00 INF] Processing order 1417742 with 1 jobs, total: ¤34.50
[2025-06-17 13:28:22.072 +10:00 INF] Creating order invoice for order 1417742
[2025-06-17 13:28:23.296 +10:00 INF] Successfully created MYOB invoice O1417742 for order 1417742
[2025-06-17 13:28:23.299 +10:00 INF] Successfully created MYOB invoice for order 1417742
[2025-06-17 13:28:23.302 +10:00 INF] Generating PDF invoice for order 1417742 at \\dfs01\resource\invoices\2025/Jun/17\O1417742.pdf
[2025-06-17 13:28:23.755 +10:00 INF] Successfully generated PDF for order 1417742
[2025-06-17 13:28:24.121 +10:00 WRN] No email address found for order 1417742
[2025-06-17 13:28:24.124 +10:00 INF] Successfully processed order 1417742 (MYOB + PDF)
[2025-06-17 13:28:24.127 +10:00 INF] Getting order 1417733
[2025-06-17 13:28:24.131 +10:00 INF] Processing order 1417733 with 1 jobs, total: ¤30.00
[2025-06-17 13:28:24.133 +10:00 INF] Creating order invoice for order 1417733
[2025-06-17 13:28:25.531 +10:00 INF] Successfully created MYOB invoice O1417733 for order 1417733
[2025-06-17 13:28:25.533 +10:00 INF] Successfully created MYOB invoice for order 1417733
[2025-06-17 13:28:25.535 +10:00 INF] Generating PDF invoice for order 1417733 at \\dfs01\resource\invoices\2025/Jun/17\O1417733.pdf
[2025-06-17 13:28:25.994 +10:00 INF] Successfully generated PDF for order 1417733
[2025-06-17 13:28:26.033 +10:00 WRN] No email address found for order 1417733
[2025-06-17 13:28:26.049 +10:00 INF] Successfully processed order 1417733 (MYOB + PDF)
[2025-06-17 13:28:26.053 +10:00 INF] Getting order 1417663
[2025-06-17 13:28:26.057 +10:00 INF] Processing order 1417663 with 1 jobs, total: ¤37.00
[2025-06-17 13:28:26.061 +10:00 INF] Creating order invoice for order 1417663
[2025-06-17 13:28:27.761 +10:00 INF] Successfully created MYOB invoice O1417663 for order 1417663
[2025-06-17 13:28:27.763 +10:00 INF] Successfully created MYOB invoice for order 1417663
[2025-06-17 13:28:27.765 +10:00 INF] Generating PDF invoice for order 1417663 at \\dfs01\resource\invoices\2025/Jun/17\O1417663.pdf
[2025-06-17 13:28:28.094 +10:00 INF] Successfully generated PDF for order 1417663
[2025-06-17 13:28:28.123 +10:00 WRN] No email address found for order 1417663
[2025-06-17 13:28:28.126 +10:00 INF] Successfully processed order 1417663 (MYOB + PDF)
[2025-06-17 13:28:28.129 +10:00 INF] Getting order 1417219
[2025-06-17 13:28:28.133 +10:00 INF] Processing order 1417219 with 2 jobs, total: ¤87.06
[2025-06-17 13:28:28.135 +10:00 INF] Creating order invoice for order 1417219
[2025-06-17 13:28:29.440 +10:00 INF] Successfully created MYOB invoice O1417219 for order 1417219
[2025-06-17 13:28:29.442 +10:00 INF] Successfully created MYOB invoice for order 1417219
[2025-06-17 13:28:29.444 +10:00 INF] Generating PDF invoice for order 1417219 at \\dfs01\resource\invoices\2025/Jun/17\O1417219.pdf
[2025-06-17 13:28:29.895 +10:00 INF] Successfully generated PDF for order 1417219
[2025-06-17 13:28:29.944 +10:00 WRN] No email address found for order 1417219
[2025-06-17 13:28:29.948 +10:00 INF] Successfully processed order 1417219 (MYOB + PDF)
[2025-06-17 13:28:29.952 +10:00 INF] Getting order 1417000
[2025-06-17 13:28:29.957 +10:00 INF] Processing order 1417000 with 1 jobs, total: ¤52.23
[2025-06-17 13:28:29.959 +10:00 INF] Creating order invoice for order 1417000
[2025-06-17 13:28:32.203 +10:00 INF] Successfully created MYOB invoice O1417000 for order 1417000
[2025-06-17 13:28:32.205 +10:00 INF] Successfully created MYOB invoice for order 1417000
[2025-06-17 13:28:32.207 +10:00 INF] Generating PDF invoice for order 1417000 at \\dfs01\resource\invoices\2025/Jun/17\O1417000.pdf
[2025-06-17 13:28:33.332 +10:00 INF] Successfully generated PDF for order 1417000
[2025-06-17 13:28:33.363 +10:00 WRN] No email address found for order 1417000
[2025-06-17 13:28:33.367 +10:00 INF] Successfully processed order 1417000 (MYOB + PDF)
[2025-06-17 13:28:33.371 +10:00 INF] Getting order 1413622
[2025-06-17 13:28:33.378 +10:00 INF] Processing order 1413622 with 6 jobs, total: ¤254.37
[2025-06-17 13:28:33.384 +10:00 INF] Creating order invoice for order 1413622
[2025-06-17 13:28:34.788 +10:00 INF] Successfully created MYOB invoice O1413622 for order 1413622
[2025-06-17 13:28:34.801 +10:00 INF] Successfully created MYOB invoice for order 1413622
[2025-06-17 13:28:34.804 +10:00 INF] Generating PDF invoice for order 1413622 at \\dfs01\resource\invoices\2025/Jun/17\O1413622.pdf
[2025-06-17 13:28:35.248 +10:00 INF] Successfully generated PDF for order 1413622
[2025-06-17 13:28:35.291 +10:00 WRN] No email address found for order 1413622
[2025-06-17 13:28:35.295 +10:00 INF] Successfully processed order 1413622 (MYOB + PDF)
[2025-06-17 13:28:35.303 +10:00 INF] Order processing completed. Processed: 17, Success: 17, Failed: 0
[2025-06-17 13:28:35.309 +10:00 INF] Processing credit invoices...
[2025-06-17 13:28:35.311 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 13:28:35.319 +10:00 INF] Found 0 credits to invoice
[2025-06-17 13:28:35.324 +10:00 INF] Processing refund invoices...
[2025-06-17 13:28:35.326 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 13:28:35.333 +10:00 INF] Found 16 refunds to invoice
[2025-06-17 13:28:35.339 +10:00 INF] Creating refund invoice for refund 1801, Amount: ¤1.69
[2025-06-17 13:28:35.671 +10:00 INF] Deleting existing invoice S248751801
[2025-06-17 13:28:36.054 +10:00 WRN] Failed to delete existing invoice S248751801 - invoice may be locked/paid in MYOB
MYOB.AccountRight.SDK.ApiValidationException: Encountered a validation error (https://arl2.api.myob.com/accountright/b2f20bad-65c4-4dd3-a921-0a20ccfffb73/Sale/Invoice/Service/ae7fcbd9-f0a0-4771-83a9-3dcdea23f5cb)
 ---> System.Net.WebException: The remote server returned an error: (400) Bad Request.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiDeleteRequestSync(Uri uri, ICompanyFileCredentials credentials)
   at MYOB.AccountRight.SDK.Services.MutableService`1.Delete(CompanyFile cf, Guid uid, ICompanyFileCredentials credentials, ErrorLevel errorLevel)
   at LepInvoicer.Implementations.MYOBService.<>c__DisplayClass33_1.<DeleteExistingInvoice>b__1() in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 875
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Implementations.MYOBService.DeleteExistingInvoice(String invoiceNumber) in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 874
[2025-06-17 13:28:36.096 +10:00 WRN] Skipping refund 1801 - cannot delete existing invoice S248751801
[2025-06-17 13:28:36.103 +10:00 INF] Creating refund invoice for refund 1802, Amount: ¤4.59
[2025-06-17 13:28:39.175 +10:00 INF] Deleting existing invoice S139771802
[2025-06-17 13:28:39.498 +10:00 WRN] Failed to delete existing invoice S139771802 - invoice may be locked/paid in MYOB
MYOB.AccountRight.SDK.ApiValidationException: Encountered a validation error (https://arl2.api.myob.com/accountright/b2f20bad-65c4-4dd3-a921-0a20ccfffb73/Sale/Invoice/Service/61295f40-7707-4574-825e-84da93a4b016)
 ---> System.Net.WebException: The remote server returned an error: (400) Bad Request.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiDeleteRequestSync(Uri uri, ICompanyFileCredentials credentials)
   at MYOB.AccountRight.SDK.Services.MutableService`1.Delete(CompanyFile cf, Guid uid, ICompanyFileCredentials credentials, ErrorLevel errorLevel)
   at LepInvoicer.Implementations.MYOBService.<>c__DisplayClass33_1.<DeleteExistingInvoice>b__1() in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 875
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Implementations.MYOBService.DeleteExistingInvoice(String invoiceNumber) in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 874
[2025-06-17 13:28:39.523 +10:00 WRN] Skipping refund 1802 - cannot delete existing invoice S139771802
[2025-06-17 13:28:39.529 +10:00 INF] Creating refund invoice for refund 1803, Amount: ¤9.27
[2025-06-17 13:28:39.899 +10:00 INF] Deleting existing invoice S150791803
[2025-06-17 13:28:40.299 +10:00 WRN] Failed to delete existing invoice S150791803 - invoice may be locked/paid in MYOB
MYOB.AccountRight.SDK.ApiValidationException: Encountered a validation error (https://arl2.api.myob.com/accountright/b2f20bad-65c4-4dd3-a921-0a20ccfffb73/Sale/Invoice/Service/ef2d3cc0-43a4-4c63-95e9-76389d058325)
 ---> System.Net.WebException: The remote server returned an error: (400) Bad Request.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiDeleteRequestSync(Uri uri, ICompanyFileCredentials credentials)
   at MYOB.AccountRight.SDK.Services.MutableService`1.Delete(CompanyFile cf, Guid uid, ICompanyFileCredentials credentials, ErrorLevel errorLevel)
   at LepInvoicer.Implementations.MYOBService.<>c__DisplayClass33_1.<DeleteExistingInvoice>b__1() in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 875
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Implementations.MYOBService.DeleteExistingInvoice(String invoiceNumber) in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 874
[2025-06-17 13:28:40.339 +10:00 WRN] Skipping refund 1803 - cannot delete existing invoice S150791803
[2025-06-17 13:28:40.345 +10:00 INF] Creating refund invoice for refund 1804, Amount: ¤11.23
[2025-06-17 13:28:40.736 +10:00 INF] Deleting existing invoice S150791804
[2025-06-17 13:28:41.177 +10:00 WRN] Failed to delete existing invoice S150791804 - invoice may be locked/paid in MYOB
MYOB.AccountRight.SDK.ApiValidationException: Encountered a validation error (https://arl2.api.myob.com/accountright/b2f20bad-65c4-4dd3-a921-0a20ccfffb73/Sale/Invoice/Service/903aeac6-182f-4aae-b4c4-d8e81708ec76)
 ---> System.Net.WebException: The remote server returned an error: (400) Bad Request.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiDeleteRequestSync(Uri uri, ICompanyFileCredentials credentials)
   at MYOB.AccountRight.SDK.Services.MutableService`1.Delete(CompanyFile cf, Guid uid, ICompanyFileCredentials credentials, ErrorLevel errorLevel)
   at LepInvoicer.Implementations.MYOBService.<>c__DisplayClass33_1.<DeleteExistingInvoice>b__1() in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 875
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Implementations.MYOBService.DeleteExistingInvoice(String invoiceNumber) in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 874
[2025-06-17 13:28:41.206 +10:00 WRN] Skipping refund 1804 - cannot delete existing invoice S150791804
[2025-06-17 13:28:41.217 +10:00 INF] Creating refund invoice for refund 1805, Amount: ¤32.62
[2025-06-17 13:28:41.619 +10:00 INF] Deleting existing invoice S252801805
[2025-06-17 13:28:42.100 +10:00 WRN] Failed to delete existing invoice S252801805 - invoice may be locked/paid in MYOB
MYOB.AccountRight.SDK.ApiValidationException: Encountered a validation error (https://arl2.api.myob.com/accountright/b2f20bad-65c4-4dd3-a921-0a20ccfffb73/Sale/Invoice/Service/6942f99f-e33c-424d-861a-2215ef9a1e09)
 ---> System.Net.WebException: The remote server returned an error: (400) Bad Request.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiDeleteRequestSync(Uri uri, ICompanyFileCredentials credentials)
   at MYOB.AccountRight.SDK.Services.MutableService`1.Delete(CompanyFile cf, Guid uid, ICompanyFileCredentials credentials, ErrorLevel errorLevel)
   at LepInvoicer.Implementations.MYOBService.<>c__DisplayClass33_1.<DeleteExistingInvoice>b__1() in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 875
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Implementations.MYOBService.DeleteExistingInvoice(String invoiceNumber) in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 874
[2025-06-17 13:28:42.184 +10:00 WRN] Skipping refund 1805 - cannot delete existing invoice S252801805
[2025-06-17 13:28:42.191 +10:00 INF] Creating refund invoice for refund 1806, Amount: ¤36.12
[2025-06-17 13:28:42.483 +10:00 INF] Deleting existing invoice S141881806
[2025-06-17 13:28:43.757 +10:00 WRN] Failed to delete existing invoice S141881806 - invoice may be locked/paid in MYOB
MYOB.AccountRight.SDK.ApiValidationException: Encountered a validation error (https://arl2.api.myob.com/accountright/b2f20bad-65c4-4dd3-a921-0a20ccfffb73/Sale/Invoice/Service/818eaa9f-5e51-4882-ab77-6b4bb0624735)
 ---> System.Net.WebException: The remote server returned an error: (400) Bad Request.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiDeleteRequestSync(Uri uri, ICompanyFileCredentials credentials)
   at MYOB.AccountRight.SDK.Services.MutableService`1.Delete(CompanyFile cf, Guid uid, ICompanyFileCredentials credentials, ErrorLevel errorLevel)
   at LepInvoicer.Implementations.MYOBService.<>c__DisplayClass33_1.<DeleteExistingInvoice>b__1() in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 875
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Implementations.MYOBService.DeleteExistingInvoice(String invoiceNumber) in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 874
[2025-06-17 13:28:43.775 +10:00 WRN] Skipping refund 1806 - cannot delete existing invoice S141881806
[2025-06-17 13:28:43.781 +10:00 INF] Creating refund invoice for refund 1807, Amount: ¤1.77
[2025-06-17 13:28:45.760 +10:00 INF] Deleting existing invoice S152161807
[2025-06-17 13:28:46.149 +10:00 WRN] Failed to delete existing invoice S152161807 - invoice may be locked/paid in MYOB
MYOB.AccountRight.SDK.ApiValidationException: Encountered a validation error (https://arl2.api.myob.com/accountright/b2f20bad-65c4-4dd3-a921-0a20ccfffb73/Sale/Invoice/Service/793efdaa-dcb5-422e-9ef2-69458a598c33)
 ---> System.Net.WebException: The remote server returned an error: (400) Bad Request.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiDeleteRequestSync(Uri uri, ICompanyFileCredentials credentials)
   at MYOB.AccountRight.SDK.Services.MutableService`1.Delete(CompanyFile cf, Guid uid, ICompanyFileCredentials credentials, ErrorLevel errorLevel)
   at LepInvoicer.Implementations.MYOBService.<>c__DisplayClass33_1.<DeleteExistingInvoice>b__1() in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 875
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Implementations.MYOBService.DeleteExistingInvoice(String invoiceNumber) in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 874
[2025-06-17 13:28:46.183 +10:00 WRN] Skipping refund 1807 - cannot delete existing invoice S152161807
[2025-06-17 13:28:46.191 +10:00 INF] Creating refund invoice for refund 1812, Amount: ¤1,312.65
[2025-06-17 13:28:46.529 +10:00 INF] Deleting existing invoice S251951812
[2025-06-17 13:28:46.853 +10:00 WRN] Failed to delete existing invoice S251951812 - invoice may be locked/paid in MYOB
MYOB.AccountRight.SDK.ApiValidationException: Encountered a validation error (https://arl2.api.myob.com/accountright/b2f20bad-65c4-4dd3-a921-0a20ccfffb73/Sale/Invoice/Service/a713338d-93e4-4f31-ab91-4947eb95c2d0)
 ---> System.Net.WebException: The remote server returned an error: (400) Bad Request.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiDeleteRequestSync(Uri uri, ICompanyFileCredentials credentials)
   at MYOB.AccountRight.SDK.Services.MutableService`1.Delete(CompanyFile cf, Guid uid, ICompanyFileCredentials credentials, ErrorLevel errorLevel)
   at LepInvoicer.Implementations.MYOBService.<>c__DisplayClass33_1.<DeleteExistingInvoice>b__1() in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 875
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Implementations.MYOBService.DeleteExistingInvoice(String invoiceNumber) in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 874
[2025-06-17 13:28:46.878 +10:00 WRN] Skipping refund 1812 - cannot delete existing invoice S251951812
[2025-06-17 13:28:46.884 +10:00 INF] Creating refund invoice for refund 1814, Amount: ¤1.37
[2025-06-17 13:28:47.344 +10:00 INF] Deleting existing invoice S137511814
[2025-06-17 13:28:48.567 +10:00 WRN] Failed to delete existing invoice S137511814 - invoice may be locked/paid in MYOB
MYOB.AccountRight.SDK.ApiValidationException: Encountered a validation error (https://arl2.api.myob.com/accountright/b2f20bad-65c4-4dd3-a921-0a20ccfffb73/Sale/Invoice/Service/ae7cf4da-1e73-4681-98b6-92685935c4b0)
 ---> System.Net.WebException: The remote server returned an error: (400) Bad Request.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiDeleteRequestSync(Uri uri, ICompanyFileCredentials credentials)
   at MYOB.AccountRight.SDK.Services.MutableService`1.Delete(CompanyFile cf, Guid uid, ICompanyFileCredentials credentials, ErrorLevel errorLevel)
   at LepInvoicer.Implementations.MYOBService.<>c__DisplayClass33_1.<DeleteExistingInvoice>b__1() in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 875
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Implementations.MYOBService.DeleteExistingInvoice(String invoiceNumber) in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 874
[2025-06-17 13:28:48.604 +10:00 WRN] Skipping refund 1814 - cannot delete existing invoice S137511814
[2025-06-17 13:28:48.615 +10:00 INF] Creating refund invoice for refund 1815, Amount: ¤3.83
[2025-06-17 13:28:48.973 +10:00 INF] Deleting existing invoice S138991815
[2025-06-17 13:28:49.486 +10:00 WRN] Failed to delete existing invoice S138991815 - invoice may be locked/paid in MYOB
MYOB.AccountRight.SDK.ApiValidationException: Encountered a validation error (https://arl2.api.myob.com/accountright/b2f20bad-65c4-4dd3-a921-0a20ccfffb73/Sale/Invoice/Service/0aaeec22-6d8e-4d71-87bd-33f814e5f06f)
 ---> System.Net.WebException: The remote server returned an error: (400) Bad Request.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiDeleteRequestSync(Uri uri, ICompanyFileCredentials credentials)
   at MYOB.AccountRight.SDK.Services.MutableService`1.Delete(CompanyFile cf, Guid uid, ICompanyFileCredentials credentials, ErrorLevel errorLevel)
   at LepInvoicer.Implementations.MYOBService.<>c__DisplayClass33_1.<DeleteExistingInvoice>b__1() in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 875
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Implementations.MYOBService.DeleteExistingInvoice(String invoiceNumber) in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 874
[2025-06-17 13:28:49.738 +10:00 WRN] Skipping refund 1815 - cannot delete existing invoice S138991815
[2025-06-17 13:28:49.744 +10:00 INF] Creating refund invoice for refund 1816, Amount: ¤6.80
[2025-06-17 13:28:50.112 +10:00 INF] Deleting existing invoice S143701816
[2025-06-17 13:28:50.583 +10:00 WRN] Failed to delete existing invoice S143701816 - invoice may be locked/paid in MYOB
MYOB.AccountRight.SDK.ApiValidationException: Encountered a validation error (https://arl2.api.myob.com/accountright/b2f20bad-65c4-4dd3-a921-0a20ccfffb73/Sale/Invoice/Service/5ac7ff46-5d70-47ca-a353-44c57a10c35c)
 ---> System.Net.WebException: The remote server returned an error: (400) Bad Request.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiDeleteRequestSync(Uri uri, ICompanyFileCredentials credentials)
   at MYOB.AccountRight.SDK.Services.MutableService`1.Delete(CompanyFile cf, Guid uid, ICompanyFileCredentials credentials, ErrorLevel errorLevel)
   at LepInvoicer.Implementations.MYOBService.<>c__DisplayClass33_1.<DeleteExistingInvoice>b__1() in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 875
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Implementations.MYOBService.DeleteExistingInvoice(String invoiceNumber) in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 874
[2025-06-17 13:28:50.600 +10:00 WRN] Skipping refund 1816 - cannot delete existing invoice S143701816
[2025-06-17 13:28:50.607 +10:00 INF] Creating refund invoice for refund 1817, Amount: ¤19.84
[2025-06-17 13:28:50.995 +10:00 INF] Deleting existing invoice S143701817
[2025-06-17 13:28:51.379 +10:00 WRN] Failed to delete existing invoice S143701817 - invoice may be locked/paid in MYOB
MYOB.AccountRight.SDK.ApiValidationException: Encountered a validation error (https://arl2.api.myob.com/accountright/b2f20bad-65c4-4dd3-a921-0a20ccfffb73/Sale/Invoice/Service/2e674898-d09f-451c-921f-b6054b956d02)
 ---> System.Net.WebException: The remote server returned an error: (400) Bad Request.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiDeleteRequestSync(Uri uri, ICompanyFileCredentials credentials)
   at MYOB.AccountRight.SDK.Services.MutableService`1.Delete(CompanyFile cf, Guid uid, ICompanyFileCredentials credentials, ErrorLevel errorLevel)
   at LepInvoicer.Implementations.MYOBService.<>c__DisplayClass33_1.<DeleteExistingInvoice>b__1() in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 875
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Implementations.MYOBService.DeleteExistingInvoice(String invoiceNumber) in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 874
[2025-06-17 13:28:51.395 +10:00 WRN] Skipping refund 1817 - cannot delete existing invoice S143701817
[2025-06-17 13:28:51.404 +10:00 INF] Creating refund invoice for refund 1818, Amount: ¤61.92
[2025-06-17 13:28:51.711 +10:00 INF] Deleting existing invoice S1531791818
[2025-06-17 13:28:52.105 +10:00 WRN] Failed to delete existing invoice S1531791818 - invoice may be locked/paid in MYOB
MYOB.AccountRight.SDK.ApiValidationException: Encountered a validation error (https://arl2.api.myob.com/accountright/b2f20bad-65c4-4dd3-a921-0a20ccfffb73/Sale/Invoice/Service/f2d3d1a7-f2e9-4aee-b0fe-cf5f0c77f7bc)
 ---> System.Net.WebException: The remote server returned an error: (400) Bad Request.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiDeleteRequestSync(Uri uri, ICompanyFileCredentials credentials)
   at MYOB.AccountRight.SDK.Services.MutableService`1.Delete(CompanyFile cf, Guid uid, ICompanyFileCredentials credentials, ErrorLevel errorLevel)
   at LepInvoicer.Implementations.MYOBService.<>c__DisplayClass33_1.<DeleteExistingInvoice>b__1() in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 875
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Implementations.MYOBService.DeleteExistingInvoice(String invoiceNumber) in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 874
[2025-06-17 13:28:52.134 +10:00 WRN] Skipping refund 1818 - cannot delete existing invoice S1531791818
[2025-06-17 13:28:52.189 +10:00 INF] Creating refund invoice for refund 1824, Amount: ¤70.04
[2025-06-17 13:28:52.940 +10:00 INF] Deleting existing invoice S189871824
[2025-06-17 13:28:53.323 +10:00 WRN] Failed to delete existing invoice S189871824 - invoice may be locked/paid in MYOB
MYOB.AccountRight.SDK.ApiValidationException: Encountered a validation error (https://arl2.api.myob.com/accountright/b2f20bad-65c4-4dd3-a921-0a20ccfffb73/Sale/Invoice/Service/3366c74b-aec7-4ee3-87d0-4ed666a81e70)
 ---> System.Net.WebException: The remote server returned an error: (400) Bad Request.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiDeleteRequestSync(Uri uri, ICompanyFileCredentials credentials)
   at MYOB.AccountRight.SDK.Services.MutableService`1.Delete(CompanyFile cf, Guid uid, ICompanyFileCredentials credentials, ErrorLevel errorLevel)
   at LepInvoicer.Implementations.MYOBService.<>c__DisplayClass33_1.<DeleteExistingInvoice>b__1() in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 875
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Implementations.MYOBService.DeleteExistingInvoice(String invoiceNumber) in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 874
[2025-06-17 13:28:53.349 +10:00 WRN] Skipping refund 1824 - cannot delete existing invoice S189871824
[2025-06-17 13:28:53.375 +10:00 INF] Creating refund invoice for refund 1836, Amount: ¤32.32
[2025-06-17 13:28:53.719 +10:00 INF] Deleting existing invoice S252801836
[2025-06-17 13:28:54.037 +10:00 WRN] Failed to delete existing invoice S252801836 - invoice may be locked/paid in MYOB
MYOB.AccountRight.SDK.ApiValidationException: Encountered a validation error (https://arl2.api.myob.com/accountright/b2f20bad-65c4-4dd3-a921-0a20ccfffb73/Sale/Invoice/Service/4b9f90a9-fe2b-4248-bce3-1aa759007fcd)
 ---> System.Net.WebException: The remote server returned an error: (400) Bad Request.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiDeleteRequestSync(Uri uri, ICompanyFileCredentials credentials)
   at MYOB.AccountRight.SDK.Services.MutableService`1.Delete(CompanyFile cf, Guid uid, ICompanyFileCredentials credentials, ErrorLevel errorLevel)
   at LepInvoicer.Implementations.MYOBService.<>c__DisplayClass33_1.<DeleteExistingInvoice>b__1() in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 875
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Implementations.MYOBService.DeleteExistingInvoice(String invoiceNumber) in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 874
[2025-06-17 13:28:54.068 +10:00 WRN] Skipping refund 1836 - cannot delete existing invoice S252801836
[2025-06-17 13:28:54.120 +10:00 INF] Creating refund invoice for refund 1837, Amount: ¤26.00
[2025-06-17 13:28:54.825 +10:00 INF] Deleting existing invoice S191871837
[2025-06-17 13:28:55.199 +10:00 WRN] Failed to delete existing invoice S191871837 - invoice may be locked/paid in MYOB
MYOB.AccountRight.SDK.ApiValidationException: Encountered a validation error (https://arl2.api.myob.com/accountright/b2f20bad-65c4-4dd3-a921-0a20ccfffb73/Sale/Invoice/Service/27c54bcf-d87b-45d8-a5e4-997d4b831e2e)
 ---> System.Net.WebException: The remote server returned an error: (400) Bad Request.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiDeleteRequestSync(Uri uri, ICompanyFileCredentials credentials)
   at MYOB.AccountRight.SDK.Services.MutableService`1.Delete(CompanyFile cf, Guid uid, ICompanyFileCredentials credentials, ErrorLevel errorLevel)
   at LepInvoicer.Implementations.MYOBService.<>c__DisplayClass33_1.<DeleteExistingInvoice>b__1() in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 875
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Implementations.MYOBService.DeleteExistingInvoice(String invoiceNumber) in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 874
[2025-06-17 13:28:55.224 +10:00 WRN] Skipping refund 1837 - cannot delete existing invoice S191871837
[2025-06-17 13:28:55.265 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 13:28:55.435 +10:00 INF] LEP Invoicer completed successfully in 65221ms. Orders: 17, Credits: 0, Refunds: 16
[2025-06-17 13:28:55.443 +10:00 INF] Database connection disposed
[2025-06-17 13:28:55.445 +10:00 INF] LEP Invoicer completed with result: 0
