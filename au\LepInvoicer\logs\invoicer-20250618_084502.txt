[2025-06-18 08:45:02.946 +10:00 INF] Starting LEP Invoicer application
[2025-06-18 08:45:02.981 +10:00 INF] Initializing FastReport...
[2025-06-18 08:45:03.085 +10:00 INF] FastReport initialized successfully
[2025-06-18 08:45:03.558 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-18 08:45:04.811 +10:00 INF] Starting LEP Invoicer at "2025-06-18T08:45:04.8111051+10:00"
[2025-06-18 08:45:04.815 +10:00 INF] Initializing database service...
[2025-06-18 08:45:04.818 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-18 08:45:04.917 +10:00 INF] Database connection established successfully
[2025-06-18 08:45:04.918 +10:00 INF] Database service initialized successfully
[2025-06-18 08:45:04.920 +10:00 INF] Checking for pending work...
[2025-06-18 08:45:04.923 +10:00 INF] Getting 500 orders to invoice
[2025-06-18 08:45:05.817 +10:00 INF] Found 0 orders to invoice (filtered 16 candidates)
[2025-06-18 08:45:05.820 +10:00 INF] Getting 100 credits to invoice
[2025-06-18 08:45:05.856 +10:00 INF] Found 0 credits to invoice
[2025-06-18 08:45:05.858 +10:00 INF] Getting 100 refunds to invoice
[2025-06-18 08:45:05.865 +10:00 INF] Found 0 refunds to invoice
[2025-06-18 08:45:05.869 +10:00 INF] No pending work found
[2025-06-18 08:45:05.872 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-18 08:45:05.873 +10:00 INF] Cleaning up invoicer logs...
[2025-06-18 08:45:05.946 +10:00 INF] LEP Invoicer completed successfully in 1134ms. No work to process.
[2025-06-18 08:45:05.953 +10:00 INF] Database connection disposed
[2025-06-18 08:45:05.955 +10:00 INF] LEP Invoicer completed with result: 0
