[2025-06-17 16:44:53.561 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 16:44:53.593 +10:00 INF] Initializing FastReport...
[2025-06-17 16:44:53.669 +10:00 INF] FastReport initialized successfully
[2025-06-17 16:44:54.086 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 16:44:55.410 +10:00 INF] Starting LEP Invoicer at "2025-06-17T16:44:55.4106089+10:00"
[2025-06-17 16:44:55.414 +10:00 INF] Initializing database service...
[2025-06-17 16:44:55.417 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 16:44:55.580 +10:00 INF] Database connection established successfully
[2025-06-17 16:44:55.582 +10:00 INF] Database service initialized successfully
[2025-06-17 16:44:55.595 +10:00 INF] Checking for pending work...
[2025-06-17 16:44:55.599 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 16:44:56.526 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 16:44:56.528 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 16:44:56.542 +10:00 INF] Found 0 credits to invoice
[2025-06-17 16:44:56.544 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 16:44:56.549 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 16:44:56.550 +10:00 INF] No pending work found
[2025-06-17 16:44:56.551 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 16:44:56.552 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 16:44:56.629 +10:00 INF] LEP Invoicer completed successfully in 1219ms. No work to process.
[2025-06-17 16:44:56.636 +10:00 INF] Database connection disposed
[2025-06-17 16:44:56.638 +10:00 INF] LEP Invoicer completed with result: 0
