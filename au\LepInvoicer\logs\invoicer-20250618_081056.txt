[2025-06-18 08:10:56.790 +10:00 INF] Starting LEP Invoicer application
[2025-06-18 08:10:56.820 +10:00 INF] Initializing FastReport...
[2025-06-18 08:10:56.898 +10:00 INF] FastReport initialized successfully
[2025-06-18 08:10:57.396 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-18 08:10:58.740 +10:00 INF] Starting LEP Invoicer at "2025-06-18T08:10:58.7405459+10:00"
[2025-06-18 08:10:58.744 +10:00 INF] Initializing database service...
[2025-06-18 08:10:58.746 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-18 08:10:58.845 +10:00 INF] Database connection established successfully
[2025-06-18 08:10:58.847 +10:00 INF] Database service initialized successfully
[2025-06-18 08:10:58.850 +10:00 INF] Checking for pending work...
[2025-06-18 08:10:58.853 +10:00 INF] Getting 500 orders to invoice
[2025-06-18 08:10:59.750 +10:00 INF] Found 0 orders to invoice (filtered 16 candidates)
[2025-06-18 08:10:59.769 +10:00 INF] Getting 100 credits to invoice
[2025-06-18 08:10:59.785 +10:00 INF] Found 0 credits to invoice
[2025-06-18 08:10:59.793 +10:00 INF] Getting 100 refunds to invoice
[2025-06-18 08:10:59.797 +10:00 INF] Found 0 refunds to invoice
[2025-06-18 08:10:59.798 +10:00 INF] No pending work found
[2025-06-18 08:10:59.802 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-18 08:10:59.803 +10:00 INF] Cleaning up invoicer logs...
[2025-06-18 08:10:59.883 +10:00 INF] LEP Invoicer completed successfully in 1142ms. No work to process.
[2025-06-18 08:10:59.889 +10:00 INF] Database connection disposed
[2025-06-18 08:10:59.891 +10:00 INF] LEP Invoicer completed with result: 0
