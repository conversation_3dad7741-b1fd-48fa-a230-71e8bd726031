[2025-06-18 09:20:47.691 +10:00 INF] Starting LEP Invoicer application
[2025-06-18 09:20:47.725 +10:00 INF] Initializing FastReport...
[2025-06-18 09:20:47.805 +10:00 INF] FastReport initialized successfully
[2025-06-18 09:20:48.256 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-18 09:20:49.630 +10:00 INF] Starting LEP Invoicer at "2025-06-18T09:20:49.6298404+10:00"
[2025-06-18 09:20:49.633 +10:00 INF] Initializing database service...
[2025-06-18 09:20:49.636 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-18 09:20:49.761 +10:00 INF] Database connection established successfully
[2025-06-18 09:20:49.764 +10:00 INF] Database service initialized successfully
[2025-06-18 09:20:49.769 +10:00 INF] Checking for pending work...
[2025-06-18 09:20:49.772 +10:00 INF] Getting 500 orders to invoice
[2025-06-18 09:20:50.724 +10:00 INF] Found 0 orders to invoice (filtered 16 candidates)
[2025-06-18 09:20:50.727 +10:00 INF] Getting 100 credits to invoice
[2025-06-18 09:20:50.743 +10:00 INF] Found 0 credits to invoice
[2025-06-18 09:20:50.746 +10:00 INF] Getting 100 refunds to invoice
[2025-06-18 09:20:50.749 +10:00 INF] Found 0 refunds to invoice
[2025-06-18 09:20:50.751 +10:00 INF] No pending work found
[2025-06-18 09:20:50.753 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-18 09:20:50.755 +10:00 INF] Cleaning up invoicer logs...
[2025-06-18 09:20:50.836 +10:00 INF] LEP Invoicer completed successfully in 1206ms. No work to process.
[2025-06-18 09:20:50.843 +10:00 INF] Database connection disposed
[2025-06-18 09:20:50.844 +10:00 INF] LEP Invoicer completed with result: 0
