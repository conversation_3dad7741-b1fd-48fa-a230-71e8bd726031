using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Diagnostics;
using LepInvoicer.Implementations;
using LepInvoicer.Interfaces;
using AutoMapper;
using Newtonsoft.Json;
using FastReport.Export.PdfSimple;

namespace LepInvoicer.Implementations;

/// <summary>
/// Main invoicer service implementation with improved error handling and structure
/// </summary>
public class InvoicerService : IInvoicerService
{
    private readonly ILogger<InvoicerService> _logger;
    private readonly InvoicerConfiguration _config;
    private readonly IMYOBService _myobService;
    private readonly IEmailService _emailService;
    private readonly LepInvoicer.Interfaces.IPdfService _pdfService;
    private readonly IDatabaseService _databaseService;
    private readonly IMapper _mapper;

    public InvoicerService(
        ILogger<InvoicerService> logger,
        IOptions<InvoicerConfiguration> config,
        IMYOBService myobService,
        IEmailService emailService,
        LepInvoicer.Interfaces.IPdfService pdfService,
        IDatabaseService databaseService,
        IMapper mapper)
    {
        _logger = logger;
        _config = config.Value;
        _myobService = myobService;
        _emailService = emailService;
        _pdfService = pdfService;
        _databaseService = databaseService;
        _mapper = mapper;
    }

    public async Task<int> RunInvoicer()
    {
        var stopwatch = Stopwatch.StartNew();

        try
        {
            _logger.LogInformation("Starting LEP Invoicer at {StartTime}", DateTime.Now);

            // Initialize database first to check for pending work
            await InitializeDatabaseService();

            // Check if there's any work to do before initializing MYOB
            var hasWork = await CheckForPendingWork();

            if (!hasWork)
            {
                _logger.LogInformation("No pending work found - skipping MYOB initialization");
                await _databaseService.CleanupInvoicerLogs();
                stopwatch.Stop();
                _logger.LogInformation("LEP Invoicer completed successfully in {ElapsedTime}ms. No work to process.", stopwatch.ElapsedMilliseconds);
                return 0;
            }

            // Initialize remaining services only if there's work to do
            await InitializeRemainingServices();

            // Process orders
            var orderResults = await ProcessOrderInvoices();

            // Process credits
            var creditResults = await ProcessCreditInvoices();

            // Process refunds
            var refundResults = await ProcessRefundInvoices();

            // Cleanup
            await _databaseService.CleanupInvoicerLogs();

            stopwatch.Stop();

            _logger.LogInformation("LEP Invoicer completed successfully in {ElapsedTime}ms. Orders: {OrderCount}, Credits: {CreditCount}, Refunds: {RefundCount}",
                stopwatch.ElapsedMilliseconds, orderResults.ProcessedCount, creditResults.ProcessedCount, refundResults.ProcessedCount);

            return 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "LEP Invoicer failed after {ElapsedTime}ms", stopwatch.ElapsedMilliseconds);
            return 1;
        }
        finally
        {
            _databaseService?.Dispose();
        }
    }

    private async Task InitializeDatabaseService()
    {
        _logger.LogInformation("Initializing database service...");
        await _databaseService.Initialize();
        _logger.LogInformation("Database service initialized successfully");
    }

    private async Task<bool> CheckForPendingWork()
    {
        _logger.LogInformation("Checking for pending work...");

        var pendingCounts = new List<string>();
        var totalPending = 0;

        // Check for orders to invoice
        if (_config.CreateOrderInvoice)
        {
            var orders = await _databaseService.GetOrdersToInvoice(_config.InvoiceBatchSize);
            if (orders.Count > 0)
            {
                pendingCounts.Add($"{orders.Count} orders");
                totalPending += orders.Count;
            }
        }

        // Check for credits to invoice
        if (_config.CreateRefundInvoice)
        {
            var credits = await _databaseService.GetCreditsToInvoice(_config.RefundBatchSize);
            if (credits.Count > 0)
            {
                pendingCounts.Add($"{credits.Count} credits");
                totalPending += credits.Count;
            }
        }

        // Check for refunds to invoice
        if (_config.CreateRefundInvoice)
        {
            var refunds = await _databaseService.GetRefundsToInvoice(_config.RefundBatchSize);
            if (refunds.Count > 0)
            {
                pendingCounts.Add($"{refunds.Count} refunds");
                totalPending += refunds.Count;
            }
        }

        if (totalPending > 0)
        {
            _logger.LogInformation("Found pending work: {PendingWork}", string.Join(", ", pendingCounts));
            return true;
        }
        else
        {
            _logger.LogInformation("No pending work found");
            return false;
        }
    }

    private async Task InitializeRemainingServices()
    {
        _logger.LogInformation("Initializing MYOB and other services...");

        await _myobService.Initialize();

        // Setup FastReport
        FastReport.Utils.Config.FontListFolder = _config.FontListFolder;

        _logger.LogInformation("All services initialized successfully");
    }

    private async Task<ProcessingResult> ProcessOrderInvoices()
    {
        // Early return if disabled
        if (!_config.CreateOrderInvoice)
        {
            _logger.LogInformation("Order invoice creation is disabled");
            return new ProcessingResult();
        }

        _logger.LogInformation("Processing order invoices...");

        var orders = await _databaseService.GetOrdersToInvoice(_config.InvoiceBatchSize);
        _logger.LogInformation("Found {OrderCount} orders to process", orders.Count);

        var result = new ProcessingResult();

        foreach (var orderInfo in orders)
        {
            try
            {
                var order = await _databaseService.GetOrder(orderInfo.Key);

                var success = await ProcessSingleOrder(order);
                if (success)
                {
                    result.SuccessCount++;
                    await _databaseService.MarkOrderInvoiced(orderInfo.Key); // Use orderInfo.Key instead of order.Id
                }
                else
                {
                    // Mark as failed so it's skipped in future runs
                    var errorMessage = _myobService.LastErrorMessage ?? "Order processing failed";
                    await _databaseService.MarkOrderFailed(orderInfo.Key, errorMessage);
                    result.FailureCount++;
                }

                result.ProcessedCount++;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to process order {OrderId}", orderInfo.Key);
                result.FailureCount++;
                result.ProcessedCount++;

                await _databaseService.MarkOrderFailed(orderInfo.Key, ex.Message);
            }
        }

        _logger.LogInformation("Order processing completed. Processed: {ProcessedCount}, Success: {SuccessCount}, Failed: {FailureCount}",
            result.ProcessedCount, result.SuccessCount, result.FailureCount);

        return result;
    }

    private async Task<bool> ProcessSingleOrder(IOrder order)
    {
        _logger.LogInformation("Processing order {OrderId} with {JobCount} jobs, total: {Total:C}",
            order.Id, order.Jobs.Count, order.PriceOfJobs);

        try
        {
            // Early return if validation fails
            if (!ValidateOrder(order))
                return false;

            // Try MYOB invoice creation - but continue with PDF/email even if it fails
            var myobSuccess = await _myobService.CreateOrderInvoice(order);
            var errorDetails = "";

            if (!myobSuccess)
            {
                errorDetails = _myobService.LastErrorMessage ?? "MYOB invoice creation failed - see application logs for details";
                _logger.LogWarning("Failed to create MYOB invoice for order {OrderId}: {ErrorDetails} - continuing with PDF generation", order.Id, errorDetails);
            }
            else
            {
                _logger.LogInformation("Successfully created MYOB invoice for order {OrderId}", order.Id);
            }

            // Generate PDF if enabled (regardless of MYOB success/failure)
            if (_config.CreatePdfInvoice)
            {
                await GenerateAndProcessPdf(order);
            }

            // Log result with useful details
            var details = CreateOrderDetails(order);
            if (!string.IsNullOrEmpty(errorDetails))
            {
                details += $" | MYOB Error: {errorDetails}";
            }

            await _databaseService.LogInvoicingResult(
                order.Id,
                order.Jobs.Count,
                order.PriceOfJobs ?? 0,
                order.FinishDate ?? DateTime.Now,
                myobSuccess, // True if MYOB succeeded, false if failed
                details);

            if (myobSuccess)
            {
                _logger.LogInformation("Successfully processed order {OrderId} (MYOB + PDF)", order.Id);
            }
            else
            {
                _logger.LogInformation("Partially processed order {OrderId} (PDF only - MYOB failed)", order.Id);
            }

            return true; // Always return true since we generated PDF/email
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing order {OrderId}", order.Id);

            await _databaseService.LogInvoicingResult(
                order.Id,
                order.Jobs.Count,
                order.PriceOfJobs ?? 0,
                order.FinishDate ?? DateTime.Now,
                false,
                ex.Message);

            return false;
        }
    }

    private bool ValidateOrder(IOrder order)
    {
        // Early return for zero price
        if (order.PriceOfJobs == null || order.PriceOfJobs == 0)
        {
            _logger.LogWarning("Order {OrderId} has zero price, marking as invoiced", order.Id);
            return false;
        }

        // Early return for missing finish date
        if (order.FinishDate == null)
        {
            _logger.LogWarning("Order {OrderId} has no finish date", order.Id);
            return false;
        }

        return true;
    }

    /// <summary>
    /// Create useful details string for order logging
    /// </summary>
    private string CreateOrderDetails(IOrder order)
    {
        var details = new List<string>();

        // Add customer information
        if (order.Customer != null)
        {
            details.Add($"Customer: {order.Customer.Name} ({order.Customer.Username})");
        }

        // Add invoice number
        details.Add($"Invoice: O{order.Id}");

        // Add purchase order if available
        if (!string.IsNullOrEmpty(order.PurchaseOrder))
        {
            details.Add($"PO: {order.PurchaseOrder}");
        }

        // Add job count
        details.Add($"Jobs: {order.Jobs?.Count ?? 0}");

        // Add finish date
        if (order.FinishDate.HasValue)
        {
            details.Add($"Finished: {order.FinishDate.Value:yyyy-MM-dd}");
        }

        return string.Join(" | ", details);
    }

    private async Task GenerateAndProcessPdf(IOrder order)
    {
        try
        {
            var finishDate = order.FinishDate?.Date.ToString("yyyy/MMM/dd").Replace(".", "") ?? DateTime.Now.ToString("yyyy/MMM/dd");
            var pdfDirectory = Path.Combine(_config.PdfFolder, finishDate);
            Directory.CreateDirectory(pdfDirectory);

            var pdfPath = Path.Combine(pdfDirectory, $"O{order.Id}.pdf");

            await _pdfService.GenerateOrderInvoicePdf(order, pdfPath);

            // Copy to order folder
            await CopyPdfToOrderFolder(order, pdfPath);

            // Send email if enabled
            if (_config.EmailPdfInvoice)
            {
                await SendInvoiceEmail(order, pdfPath);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to generate/process PDF for order {OrderId}", order.Id);
            // Don't fail the entire order for PDF issues
        }
    }

    private Task CopyPdfToOrderFolder(IOrder order, string pdfPath)
    {
        try
        {
            var orderFolder = Path.Combine(_config.DataDirectoryFullName, "orders",
                order.DateCreated.ToString("yyyyMMdd"), order.OrderNr, "Extrafiles");

            Directory.CreateDirectory(orderFolder);

            var destinationPath = Path.Combine(orderFolder, $"O{order.Id}.pdf");
            File.Copy(pdfPath, destinationPath, true);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to copy PDF to order folder for order {OrderId}", order.Id);
        }

        return Task.CompletedTask;
    }

    private async Task SendInvoiceEmail(IOrder order, string pdfPath)
    {
        try
        {
            var emailAddress = _config.EmailPdfAddress ?? order.Customer.AccountEmail ?? order.Customer.Email;

            // Early return if no email address
            if (string.IsNullOrEmpty(emailAddress))
            {
                _logger.LogWarning("No email address found for order {OrderId}", order.Id);
                return;
            }

            var success = await _emailService.SendInvoiceEmail(emailAddress, $"O{order.Id}", pdfPath);

            // Log result based on success
            if (success)
            {
                _logger.LogInformation("Email sent successfully for order {OrderId} to {Email}", order.Id, emailAddress);
            }
            else
            {
                _logger.LogWarning("Failed to send email for order {OrderId} to {Email}", order.Id, emailAddress);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending email for order {OrderId}", order.Id);
        }
    }

    /// <summary>
    /// Resend emails for all successfully invoiced orders after a specific date
    /// </summary>
    public async Task ResendEmailsForInvoicedOrders(DateTime fromDate)
    {
        _logger.LogInformation("Starting email resend for orders invoiced after {FromDate}", fromDate.ToString("yyyy-MM-dd"));

        try
        {
            // Get orders that were successfully invoiced after the specified date
            var invoicedOrders = await _databaseService.GetInvoicedOrdersAfterDate(fromDate);

            _logger.LogInformation("Found {OrderCount} successfully invoiced orders to resend emails for", invoicedOrders.Count);

            if (!invoicedOrders.Any())
            {
                _logger.LogInformation("No orders found to resend emails for");
                return;
            }

            int successCount = 0;
            int failureCount = 0;
            int noEmailCount = 0;

            foreach (IOrder order in invoicedOrders)
            {
                try
                {
                    // Check if PDF exists in the order folder structure (where it's actually saved)
                    var orderFolder = Path.Combine(_config.DataDirectoryFullName, "orders",
                        order.DateCreated.ToString("yyyyMMdd"), order.OrderNr, "Extrafiles");
                    var pdfPath = Path.Combine(orderFolder, $"O{order.Id}.pdf");

                    if (!File.Exists(pdfPath))
                    {
                        _logger.LogWarning("PDF not found for order {OrderId} at {PdfPath}", order.Id, pdfPath);
                        failureCount++;
                        continue;
                    }

                    // Get email address using same logic as normal processing
                    var emailAddress = _config.EmailPdfAddress ?? order.Customer.AccountEmail ?? order.Customer.Email;

                    if (string.IsNullOrEmpty(emailAddress))
                    {
                        _logger.LogWarning("No email address found for order {OrderId} - skipping", order.Id);
                        noEmailCount++;
                        continue;
                    }

                    // Send email
                    var success = await _emailService.SendInvoiceEmail(emailAddress, $"O{order.Id}", pdfPath);

                    if (success)
                    {
                        _logger.LogInformation("Email resent successfully for order {OrderId} to {Email}", order.Id, emailAddress);
                        successCount++;
                    }
                    else
                    {
                        _logger.LogWarning("Failed to resend email for order {OrderId} to {Email}", order.Id, emailAddress);
                        failureCount++;
                    }

                    // Small delay to avoid overwhelming email server
                    await Task.Delay(500);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error processing email resend for order {OrderId}", order.Id);
                    failureCount++;
                }
            }

            _logger.LogInformation("Email resend completed. Success: {SuccessCount}, Failed: {FailureCount}, No Email: {NoEmailCount}, Total: {TotalCount}",
                successCount, failureCount, noEmailCount, invoicedOrders.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to resend emails for invoiced orders");
            throw;
        }
    }

    private async Task<ProcessingResult> ProcessCreditInvoices()
    {
        // Early return if disabled
        if (!_config.CreateRefundInvoice)
            return new ProcessingResult();

        _logger.LogInformation("Processing credit invoices...");

        var credits = await _databaseService.GetCreditsToInvoice(_config.RefundBatchSize);
        var result = new ProcessingResult();

        foreach (var credit in credits)
        {
            try
            {
                var success = await _myobService.CreateCreditInvoice(credit);
                if (success)
                {
                    await _databaseService.MarkCreditInvoiced(credit.Id);

                    // Generate PDF and email for credit
                    if (_config.CreatePdfInvoice)
                    {
                        await CreateCreditPdfAndEmail(credit);
                    }

                    // Log success to Invoicer2Log (matching LINQPad script)
                    if (credit.Order != null)
                    {
                        await _databaseService.LogInvoicingResult(
                            credit.Order.Id,
                            1, // JobCount = 1 for credits
                            credit.Amount,
                            credit.DateCreated,
                            true,
                            $"Credit Type {credit.Type} - CreditId {credit.Id}");
                    }

                    result.SuccessCount++;
                }
                else
                {
                    // Mark as failed so it's skipped in future runs
                    await _databaseService.MarkCreditFailed(credit.Id);

                    // Log failure to Invoicer2Log
                    var errorMessage = _myobService.LastErrorMessage ?? "Failed to create credit invoice";
                    if (credit.Order != null)
                    {
                        await _databaseService.LogInvoicingResult(
                            credit.Order.Id,
                            1, // JobCount = 1 for credits
                            credit.Amount,
                            credit.DateCreated,
                            false, // Failed
                            $"Credit failed: {errorMessage}");
                    }

                    result.FailureCount++;
                }
                result.ProcessedCount++;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to process credit {CreditId}", credit.Id);

                // Mark as failed so it's skipped in future runs
                await _databaseService.MarkCreditFailed(credit.Id);

                // Log failure to Invoicer2Log
                if (credit.Order != null)
                {
                    await _databaseService.LogInvoicingResult(
                        credit.Order.Id,
                        1,
                        credit.Amount,
                        credit.DateCreated,
                        false,
                        $"Credit processing exception: {ex.Message}");
                }

                result.FailureCount++;
                result.ProcessedCount++;
            }
        }

        return result;
    }

    private async Task<ProcessingResult> ProcessRefundInvoices()
    {
        // Early return if disabled
        if (!_config.CreateRefundInvoice)
            return new ProcessingResult();

        _logger.LogInformation("Processing refund invoices...");

        var refunds = await _databaseService.GetRefundsToInvoice(_config.RefundBatchSize);
        var result = new ProcessingResult();

        foreach (var refund in refunds)
        {
            try
            {
                var success = await _myobService.CreateRefundInvoice(refund);
                if (success)
                {
                    await _databaseService.MarkCreditInvoiced(refund.Id);

                    // Generate PDF for refund (no email per LinqPad script)
                    if (_config.CreatePdfInvoice)
                    {
                        await CreateRefundPdf(refund);
                    }

                    // Log success to Invoicer2Log with NULL OrderId (now allowed by relaxed constraint)
                    await _databaseService.LogInvoicingResult(
                        0, // Will be converted to NULL in database for refunds
                        1, // JobCount = 1 for refunds
                        refund.Amount,
                        refund.DateCreated,
                        true,
                        $"Refund Type {refund.Type} - CreditId {refund.Id} - CustomerId {refund.Customer?.Id}");

                    result.SuccessCount++;
                }
                else
                {
                    // Mark as failed so it's skipped in future runs
                    await _databaseService.MarkCreditFailed(refund.Id);

                    // Log failure to Invoicer2Log
                    var errorMessage = _myobService.LastErrorMessage ?? "Failed to create refund invoice";
                    await _databaseService.LogInvoicingResult(
                        0, // Will be converted to NULL in database for refunds
                        1, // JobCount = 1 for refunds
                        refund.Amount,
                        refund.DateCreated,
                        false, // Failed
                        $"Refund failed: {errorMessage}");

                    result.FailureCount++;
                }
                result.ProcessedCount++;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to process refund {RefundId}", refund.Id);

                // Mark as failed so it's skipped in future runs
                await _databaseService.MarkCreditFailed(refund.Id);

                // Log failure to Invoicer2Log
                await _databaseService.LogInvoicingResult(
                    0, // Will be converted to NULL in database for refunds
                    1, // JobCount = 1 for refunds
                    refund.Amount,
                    refund.DateCreated,
                    false, // Failed
                    $"Refund processing exception: {ex.Message}");

                result.FailureCount++;
                result.ProcessedCount++;
            }
        }

        return result;
    }

    private async Task CreateCreditPdfAndEmail(OrderCredit credit)
    {
        try
        {
            var invoiceNumber = credit.Order != null
                ? $"{credit.Type}{credit.Order.Id}{credit.Id}"
                : $"{credit.Type}{credit.Customer.Id}{credit.Id}";

            var orderDto = _mapper.Map<OrderRefundDto>(credit);
            var jsonStr = JsonConvert.SerializeObject(orderDto, Formatting.Indented).Replace("'", "''");

            var report = new FastReport.Report();
            var templatePath = _config.GetRefundInvoiceTemplatePath();
            report.Load(templatePath);
            report.Dictionary.Connections[0].ConnectionString = "Json='" + jsonStr + "'";
            report.Prepare();

            var finishDate = credit.Order?.FinishDate ?? DateTime.Now;
            var dateFolder = finishDate.Date.ToString("yyyy/MMM/dd").Replace(".", "");
            var pdfDir = Path.Combine(_config.PdfFolder, dateFolder);
            Directory.CreateDirectory(pdfDir);

            var pdfPath = Path.Combine(pdfDir, $"{invoiceNumber}.pdf");
            using var fs = new FileStream(pdfPath, FileMode.Create);
            new PDFSimpleExport().Export(report, fs);

            // Copy to order folder for C/M types
            if ((credit.Type == "C" || credit.Type == "M") && credit.Order != null)
            {
                var orderFolder = Path.Combine(_config.DataDirectoryFullName, "orders",
                    credit.Order.DateCreated.ToString("yyyyMMdd"), credit.Order.OrderNr, "Extrafiles");
                Directory.CreateDirectory(orderFolder);
                File.Copy(pdfPath, Path.Combine(orderFolder, $"{invoiceNumber}.pdf"), true);
            }

            // Email if enabled
            if (_config.EmailPdfInvoice && credit.Order != null)
            {
                var emailAddress = credit.Order.Customer.AccountEmail ?? credit.Order.Customer.Email;
                if (!string.IsNullOrEmpty(_config.EmailPdfAddress))
                    emailAddress = _config.EmailPdfAddress;

                if (!string.IsNullOrEmpty(emailAddress))
                {
                    await _emailService.SendInvoiceEmail(emailAddress, invoiceNumber, pdfPath);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create PDF for credit {CreditId}", credit.Id);
        }
    }

    private Task CreateRefundPdf(OrderCredit refund)
    {
        try
        {
            var invoiceNumber = $"{refund.Type}{refund.Customer.Id}{refund.Id}";
            var orderDto = _mapper.Map<CustomerRefundDto>(refund);
            var jsonStr = JsonConvert.SerializeObject(orderDto, Formatting.Indented).Replace("'", "''");

            var report = new FastReport.Report();
            var templatePath = _config.GetRefundInvoiceTemplatePath();
            report.Load(templatePath);
            report.Dictionary.Connections[0].ConnectionString = "Json='" + jsonStr + "'";
            report.Prepare();

            var customerFolder = $"Customers\\{refund.Customer.Id}";
            var pdfDir = Path.Combine(_config.PdfFolder, customerFolder);
            Directory.CreateDirectory(pdfDir);

            var pdfPath = Path.Combine(pdfDir, $"{invoiceNumber}.pdf");
            using var fs = new FileStream(pdfPath, FileMode.Create);
            new PDFSimpleExport().Export(report, fs);

            // Note: LinqPad script has email disabled for refunds (if (false && emailPdfInvoice))
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create PDF for refund {RefundId}", refund.Id);
        }

        return Task.CompletedTask;
    }
}

/// <summary>
/// Result of processing a batch of items
/// </summary>
public class ProcessingResult
{
    public int ProcessedCount { get; set; }
    public int SuccessCount { get; set; }
    public int FailureCount { get; set; }
}
