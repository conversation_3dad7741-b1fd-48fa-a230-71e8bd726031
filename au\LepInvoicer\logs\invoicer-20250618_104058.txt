[2025-06-18 10:40:58.889 +10:00 INF] Starting LEP Invoicer application
[2025-06-18 10:40:58.918 +10:00 INF] Initializing FastReport...
[2025-06-18 10:40:59.011 +10:00 INF] FastReport initialized successfully
[2025-06-18 10:40:59.655 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-18 10:41:01.323 +10:00 INF] Starting LEP Invoicer at "2025-06-18T10:41:01.3223467+10:00"
[2025-06-18 10:41:01.327 +10:00 INF] Initializing database service...
[2025-06-18 10:41:01.330 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-18 10:41:01.500 +10:00 INF] Database connection established successfully
[2025-06-18 10:41:01.502 +10:00 INF] Database service initialized successfully
[2025-06-18 10:41:01.504 +10:00 INF] Checking for pending work...
[2025-06-18 10:41:01.507 +10:00 INF] Getting 500 orders to invoice
[2025-06-18 10:41:02.674 +10:00 INF] Found 0 orders to invoice (filtered 17 candidates)
[2025-06-18 10:41:02.678 +10:00 INF] Getting 100 credits to invoice
[2025-06-18 10:41:02.692 +10:00 INF] Found 0 credits to invoice
[2025-06-18 10:41:02.704 +10:00 INF] Getting 100 refunds to invoice
[2025-06-18 10:41:02.710 +10:00 INF] Found 0 refunds to invoice
[2025-06-18 10:41:02.711 +10:00 INF] No pending work found
[2025-06-18 10:41:02.712 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-18 10:41:02.716 +10:00 INF] Cleaning up invoicer logs...
[2025-06-18 10:41:02.812 +10:00 INF] LEP Invoicer completed successfully in 1489ms. No work to process.
[2025-06-18 10:41:02.819 +10:00 INF] Database connection disposed
[2025-06-18 10:41:02.820 +10:00 INF] LEP Invoicer completed with result: 0
