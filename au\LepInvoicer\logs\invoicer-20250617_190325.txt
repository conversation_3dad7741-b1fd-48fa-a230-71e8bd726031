[2025-06-17 19:03:25.361 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 19:03:25.391 +10:00 INF] Initializing FastReport...
[2025-06-17 19:03:25.478 +10:00 INF] FastReport initialized successfully
[2025-06-17 19:03:25.882 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 19:03:27.215 +10:00 INF] Starting LEP Invoicer at "2025-06-17T19:03:27.2153638+10:00"
[2025-06-17 19:03:27.221 +10:00 INF] Initializing database service...
[2025-06-17 19:03:27.224 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 19:03:27.347 +10:00 INF] Database connection established successfully
[2025-06-17 19:03:27.348 +10:00 INF] Database service initialized successfully
[2025-06-17 19:03:27.352 +10:00 INF] Checking for pending work...
[2025-06-17 19:03:27.355 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 19:03:28.270 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 19:03:28.276 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 19:03:28.288 +10:00 INF] Found 0 credits to invoice
[2025-06-17 19:03:28.290 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 19:03:28.300 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 19:03:28.301 +10:00 INF] No pending work found
[2025-06-17 19:03:28.303 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 19:03:28.304 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 19:03:28.400 +10:00 INF] LEP Invoicer completed successfully in 1185ms. No work to process.
[2025-06-17 19:03:28.408 +10:00 INF] Database connection disposed
[2025-06-17 19:03:28.410 +10:00 INF] LEP Invoicer completed with result: 0
