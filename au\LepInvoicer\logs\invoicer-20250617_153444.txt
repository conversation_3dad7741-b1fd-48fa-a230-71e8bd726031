[2025-06-17 15:34:44.162 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 15:34:44.189 +10:00 INF] Initializing FastReport...
[2025-06-17 15:34:44.274 +10:00 INF] FastReport initialized successfully
[2025-06-17 15:34:44.691 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 15:34:46.041 +10:00 INF] Starting LEP Invoicer at "2025-06-17T15:34:46.0412199+10:00"
[2025-06-17 15:34:46.045 +10:00 INF] Initializing database service...
[2025-06-17 15:34:46.048 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 15:34:46.196 +10:00 INF] Database connection established successfully
[2025-06-17 15:34:46.197 +10:00 INF] Database service initialized successfully
[2025-06-17 15:34:46.200 +10:00 INF] Checking for pending work...
[2025-06-17 15:34:46.203 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 15:34:47.193 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 15:34:47.206 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 15:34:47.219 +10:00 INF] Found 0 credits to invoice
[2025-06-17 15:34:47.221 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 15:34:47.227 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 15:34:47.230 +10:00 INF] No pending work found
[2025-06-17 15:34:47.232 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 15:34:47.235 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 15:34:47.335 +10:00 INF] LEP Invoicer completed successfully in 1293ms. No work to process.
[2025-06-17 15:34:47.341 +10:00 INF] Database connection disposed
[2025-06-17 15:34:47.343 +10:00 INF] LEP Invoicer completed with result: 0
