[2025-06-17 19:59:02.793 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 19:59:02.822 +10:00 INF] Initializing FastReport...
[2025-06-17 19:59:02.894 +10:00 INF] FastReport initialized successfully
[2025-06-17 19:59:03.349 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 19:59:04.618 +10:00 INF] Starting LEP Invoicer at "2025-06-17T19:59:04.6177390+10:00"
[2025-06-17 19:59:04.621 +10:00 INF] Initializing database service...
[2025-06-17 19:59:04.624 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 19:59:04.727 +10:00 INF] Database connection established successfully
[2025-06-17 19:59:04.728 +10:00 INF] Database service initialized successfully
[2025-06-17 19:59:04.731 +10:00 INF] Checking for pending work...
[2025-06-17 19:59:04.734 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 19:59:05.585 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 19:59:05.598 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 19:59:05.610 +10:00 INF] Found 0 credits to invoice
[2025-06-17 19:59:05.612 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 19:59:05.616 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 19:59:05.618 +10:00 INF] No pending work found
[2025-06-17 19:59:05.619 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 19:59:05.620 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 19:59:05.698 +10:00 INF] LEP Invoicer completed successfully in 1080ms. No work to process.
[2025-06-17 19:59:05.704 +10:00 INF] Database connection disposed
[2025-06-17 19:59:05.705 +10:00 INF] LEP Invoicer completed with result: 0
