[2025-06-17 20:30:45.629 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 20:30:45.660 +10:00 INF] Initializing FastReport...
[2025-06-17 20:30:45.733 +10:00 INF] FastReport initialized successfully
[2025-06-17 20:30:46.171 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 20:30:47.629 +10:00 INF] Starting LEP Invoicer at "2025-06-17T20:30:47.6291576+10:00"
[2025-06-17 20:30:47.633 +10:00 INF] Initializing database service...
[2025-06-17 20:30:47.636 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 20:30:47.738 +10:00 INF] Database connection established successfully
[2025-06-17 20:30:47.740 +10:00 INF] Database service initialized successfully
[2025-06-17 20:30:47.744 +10:00 INF] Checking for pending work...
[2025-06-17 20:30:47.748 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 20:30:48.669 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 20:30:48.672 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 20:30:48.689 +10:00 INF] Found 0 credits to invoice
[2025-06-17 20:30:48.692 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 20:30:48.696 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 20:30:48.700 +10:00 INF] No pending work found
[2025-06-17 20:30:48.701 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 20:30:48.705 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 20:30:48.783 +10:00 INF] LEP Invoicer completed successfully in 1153ms. No work to process.
[2025-06-17 20:30:48.789 +10:00 INF] Database connection disposed
[2025-06-17 20:30:48.792 +10:00 INF] LEP Invoicer completed with result: 0
