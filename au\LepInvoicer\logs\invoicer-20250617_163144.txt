[2025-06-17 16:31:44.451 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 16:31:44.478 +10:00 INF] Initializing FastReport...
[2025-06-17 16:31:44.560 +10:00 INF] FastReport initialized successfully
[2025-06-17 16:31:44.996 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 16:31:46.281 +10:00 INF] Starting LEP Invoicer at "2025-06-17T16:31:46.2808157+10:00"
[2025-06-17 16:31:46.312 +10:00 INF] Initializing database service...
[2025-06-17 16:31:46.318 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 16:31:46.448 +10:00 INF] Database connection established successfully
[2025-06-17 16:31:46.461 +10:00 INF] Database service initialized successfully
[2025-06-17 16:31:46.469 +10:00 INF] Checking for pending work...
[2025-06-17 16:31:46.473 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 16:31:47.482 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 16:31:47.491 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 16:31:47.509 +10:00 INF] Found 0 credits to invoice
[2025-06-17 16:31:47.521 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 16:31:47.533 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 16:31:47.536 +10:00 INF] No pending work found
[2025-06-17 16:31:47.537 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 16:31:47.539 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 16:31:47.637 +10:00 INF] LEP Invoicer completed successfully in 1356ms. No work to process.
[2025-06-17 16:31:47.643 +10:00 INF] Database connection disposed
[2025-06-17 16:31:47.645 +10:00 INF] LEP Invoicer completed with result: 0
