[2025-06-17 17:50:17.622 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 17:50:17.649 +10:00 INF] Initializing FastReport...
[2025-06-17 17:50:17.726 +10:00 INF] FastReport initialized successfully
[2025-06-17 17:50:18.137 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 17:50:19.426 +10:00 INF] Starting LEP Invoicer at "2025-06-17T17:50:19.4263353+10:00"
[2025-06-17 17:50:19.430 +10:00 INF] Initializing database service...
[2025-06-17 17:50:19.432 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 17:50:19.530 +10:00 INF] Database connection established successfully
[2025-06-17 17:50:19.532 +10:00 INF] Database service initialized successfully
[2025-06-17 17:50:19.535 +10:00 INF] Checking for pending work...
[2025-06-17 17:50:19.537 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 17:50:20.469 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 17:50:20.471 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 17:50:20.484 +10:00 INF] Found 0 credits to invoice
[2025-06-17 17:50:20.486 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 17:50:20.489 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 17:50:20.490 +10:00 INF] No pending work found
[2025-06-17 17:50:20.492 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 17:50:20.493 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 17:50:20.567 +10:00 INF] LEP Invoicer completed successfully in 1141ms. No work to process.
[2025-06-17 17:50:20.573 +10:00 INF] Database connection disposed
[2025-06-17 17:50:20.575 +10:00 INF] LEP Invoicer completed with result: 0
