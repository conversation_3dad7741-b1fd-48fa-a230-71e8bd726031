[2025-06-17 16:57:57.613 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 16:57:57.641 +10:00 INF] Initializing FastReport...
[2025-06-17 16:57:57.713 +10:00 INF] FastReport initialized successfully
[2025-06-17 16:57:58.123 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 16:57:59.469 +10:00 INF] Starting LEP Invoicer at "2025-06-17T16:57:59.4692345+10:00"
[2025-06-17 16:57:59.473 +10:00 INF] Initializing database service...
[2025-06-17 16:57:59.477 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 16:57:59.585 +10:00 INF] Database connection established successfully
[2025-06-17 16:57:59.587 +10:00 INF] Database service initialized successfully
[2025-06-17 16:57:59.590 +10:00 INF] Checking for pending work...
[2025-06-17 16:57:59.594 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 16:58:00.607 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 16:58:00.610 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 16:58:00.623 +10:00 INF] Found 0 credits to invoice
[2025-06-17 16:58:00.625 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 16:58:00.631 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 16:58:00.632 +10:00 INF] No pending work found
[2025-06-17 16:58:00.634 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 16:58:00.635 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 16:58:00.739 +10:00 INF] LEP Invoicer completed successfully in 1270ms. No work to process.
[2025-06-17 16:58:00.746 +10:00 INF] Database connection disposed
[2025-06-17 16:58:00.756 +10:00 INF] LEP Invoicer completed with result: 0
