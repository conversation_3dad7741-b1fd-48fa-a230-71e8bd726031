using NHibernate.Type;
using System;

namespace lep
{
	[Serializable]
	public class PersistentEnumType<T> : PersistentEnumType
    {
        public PersistentEnumType() : base(typeof(T))
        {
        }
    }

    public static class Extensions
    {
        public static string ToDescription(this Enum e)
        {
            var attributes =
                (DescriptionAttribute[])
                e.GetType().GetField(e.ToString()).GetCustomAttributes(typeof(DescriptionAttribute), false);
            return attributes.Length > 0 ? attributes[0].DefaultDescription : e.ToString();
        }

        public static string ToCustomerDescription(this Enum e)
        {
            var attributes =
                (DescriptionAttribute[])
                e.GetType().GetField(e.ToString()).GetCustomAttributes(typeof(DescriptionAttribute), false);
            return attributes.Length > 0 ? attributes[0].CustomerDescription : e.ToString();
        }
    }
}
