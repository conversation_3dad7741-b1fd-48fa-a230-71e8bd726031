[2025-06-17 20:08:53.730 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 20:08:53.760 +10:00 INF] Initializing FastReport...
[2025-06-17 20:08:53.850 +10:00 INF] FastReport initialized successfully
[2025-06-17 20:08:54.306 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 20:08:55.534 +10:00 INF] Starting LEP Invoicer at "2025-06-17T20:08:55.5343785+10:00"
[2025-06-17 20:08:55.543 +10:00 INF] Initializing database service...
[2025-06-17 20:08:55.548 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 20:08:55.663 +10:00 INF] Database connection established successfully
[2025-06-17 20:08:55.664 +10:00 INF] Database service initialized successfully
[2025-06-17 20:08:55.672 +10:00 INF] Checking for pending work...
[2025-06-17 20:08:55.676 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 20:08:56.619 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 20:08:56.626 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 20:08:56.644 +10:00 INF] Found 0 credits to invoice
[2025-06-17 20:08:56.646 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 20:08:56.650 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 20:08:56.653 +10:00 INF] No pending work found
[2025-06-17 20:08:56.654 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 20:08:56.656 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 20:08:56.750 +10:00 INF] LEP Invoicer completed successfully in 1216ms. No work to process.
[2025-06-17 20:08:56.757 +10:00 INF] Database connection disposed
[2025-06-17 20:08:56.759 +10:00 INF] LEP Invoicer completed with result: 0
