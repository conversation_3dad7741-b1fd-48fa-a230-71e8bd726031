[2025-06-17 17:15:23.525 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 17:15:23.554 +10:00 INF] Initializing FastReport...
[2025-06-17 17:15:23.635 +10:00 INF] FastReport initialized successfully
[2025-06-17 17:15:24.092 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 17:15:25.460 +10:00 INF] Starting LEP Invoicer at "2025-06-17T17:15:25.4597428+10:00"
[2025-06-17 17:15:25.463 +10:00 INF] Initializing database service...
[2025-06-17 17:15:25.466 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 17:15:25.563 +10:00 INF] Database connection established successfully
[2025-06-17 17:15:25.565 +10:00 INF] Database service initialized successfully
[2025-06-17 17:15:25.567 +10:00 INF] Checking for pending work...
[2025-06-17 17:15:25.570 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 17:15:26.560 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 17:15:26.565 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 17:15:26.579 +10:00 INF] Found 0 credits to invoice
[2025-06-17 17:15:26.581 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 17:15:26.586 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 17:15:26.587 +10:00 INF] No pending work found
[2025-06-17 17:15:26.588 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 17:15:26.590 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 17:15:26.668 +10:00 INF] LEP Invoicer completed successfully in 1208ms. No work to process.
[2025-06-17 17:15:26.676 +10:00 INF] Database connection disposed
[2025-06-17 17:15:26.684 +10:00 INF] LEP Invoicer completed with result: 0
