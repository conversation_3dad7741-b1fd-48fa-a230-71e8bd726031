[2025-06-10 19:40:39.942 +10:00 INF] Starting LEP Invoicer application
[2025-06-10 19:40:40.873 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-10 19:40:43.194 +10:00 INF] Starting LEP Invoicer at "2025-06-10T19:40:43.1943706+10:00"
[2025-06-10 19:40:43.201 +10:00 INF] Initializing database service...
[2025-06-10 19:40:43.205 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-10 19:40:43.511 +10:00 INF] Database connection established successfully
[2025-06-10 19:40:43.514 +10:00 INF] Database service initialized successfully
[2025-06-10 19:40:43.519 +10:00 INF] Checking for pending work...
[2025-06-10 19:40:43.525 +10:00 INF] Getting 500 orders to invoice
[2025-06-10 19:40:44.445 +10:00 INF] Found 2 orders to invoice
[2025-06-10 19:40:44.450 +10:00 INF] Getting 100 credits to invoice
[2025-06-10 19:40:44.500 +10:00 INF] Found 0 credits to invoice
[2025-06-10 19:40:44.513 +10:00 INF] Getting 100 refunds to invoice
[2025-06-10 19:40:44.533 +10:00 INF] Found 0 refunds to invoice
[2025-06-10 19:40:44.536 +10:00 INF] Found pending work: 2 orders
[2025-06-10 19:40:44.540 +10:00 INF] Initializing MYOB and other services...
[2025-06-10 19:40:44.545 +10:00 INF] Initializing MYOB service...
[2025-06-10 19:40:44.547 +10:00 INF] Initializing MYOB with OAuth authentication
[2025-06-10 19:40:44.552 +10:00 INF] Starting OAuth authentication flow
[2025-06-10 19:40:44.676 +10:00 INF] Using existing OAuth tokens
[2025-06-10 19:40:44.679 +10:00 INF] Initializing MYOB services with OAuth keystore
[2025-06-10 19:40:44.685 +10:00 INF] MYOB services initialized successfully
[2025-06-10 19:40:44.688 +10:00 INF] OAuth keystore set for API calls
[2025-06-10 19:40:44.689 +10:00 INF] Getting company files from MYOB
[2025-06-10 19:40:45.055 +10:00 INF] MYOB service initialized with OAuth credentials. Using company file: LEP Colour Printers Pty Ltd
[2025-06-10 19:40:45.076 +10:00 INF] Current cache stats: {"AccountsCount":269,"TaxCodesCount":18,"CustomersCount":393,"LastCacheLoad":"2025-06-10T19:31:07.5727667+10:00","CacheAge":"00:09:37.4936067","IsExpired":false,"CacheDirectory":"C:\\Users\\<USER>\\AppData\\Local\\LepInvoicer\\cache"}
[2025-06-10 19:40:45.128 +10:00 INF] Using cached MYOB data: {"AccountsCount":269,"TaxCodesCount":18,"CustomersCount":393,"LastCacheLoad":"2025-06-10T19:31:07.5727667+10:00","CacheAge":"00:09:37.4936067","IsExpired":false,"CacheDirectory":"C:\\Users\\<USER>\\AppData\\Local\\LepInvoicer\\cache"}
[2025-06-10 19:40:45.956 +10:00 INF] Using cached GST tax code
[2025-06-10 19:40:45.960 +10:00 INF] Using cached freight account
[2025-06-10 19:40:45.962 +10:00 INF] Using cached discounts account
[2025-06-10 19:40:45.964 +10:00 INF] MYOB cache initialization complete: {"AccountsCount":269,"TaxCodesCount":18,"CustomersCount":393,"LastCacheLoad":"2025-06-10T19:31:07.5727667+10:00","CacheAge":"00:09:38.3915232","IsExpired":false,"CacheDirectory":"C:\\Users\\<USER>\\AppData\\Local\\LepInvoicer\\cache"}
[2025-06-10 19:40:45.985 +10:00 INF] MYOB service initialized successfully with company file: LEP Colour Printers Pty Ltd
[2025-06-10 19:40:45.988 +10:00 INF] All services initialized successfully
[2025-06-10 19:40:45.993 +10:00 INF] Processing order invoices...
[2025-06-10 19:40:45.994 +10:00 INF] Getting 500 orders to invoice
[2025-06-10 19:40:46.321 +10:00 INF] Found 2 orders to invoice
[2025-06-10 19:40:46.325 +10:00 INF] Found 2 orders to process
[2025-06-10 19:40:46.328 +10:00 INF] Getting order 1417006
[2025-06-10 19:40:46.673 +10:00 INF] Processing order 1417006 with 1 jobs, total: ¤0.00
[2025-06-10 19:40:46.683 +10:00 WRN] Order 1417006 has zero price, marking as invoiced
[2025-06-10 19:40:46.687 +10:00 INF] Getting order 1416838
[2025-06-10 19:40:46.700 +10:00 INF] Processing order 1416838 with 1 jobs, total: ¤0.00
[2025-06-10 19:40:46.705 +10:00 WRN] Order 1416838 has zero price, marking as invoiced
[2025-06-10 19:40:46.708 +10:00 INF] Order processing completed. Processed: 2, Success: 0, Failed: 2
[2025-06-10 19:40:46.714 +10:00 INF] Processing credit invoices...
[2025-06-10 19:40:46.716 +10:00 INF] Getting 100 credits to invoice
[2025-06-10 19:40:46.724 +10:00 INF] Found 0 credits to invoice
[2025-06-10 19:40:46.735 +10:00 INF] Processing refund invoices...
[2025-06-10 19:40:46.738 +10:00 INF] Getting 100 refunds to invoice
[2025-06-10 19:40:46.748 +10:00 INF] Found 0 refunds to invoice
[2025-06-10 19:40:46.754 +10:00 INF] Cleaning up invoicer logs...
[2025-06-10 19:40:46.826 +10:00 INF] LEP Invoicer completed successfully in 3631ms. Orders: 2, Credits: 0, Refunds: 0
[2025-06-10 19:40:46.841 +10:00 INF] Database connection disposed
[2025-06-10 19:40:46.847 +10:00 INF] LEP Invoicer completed with result: 0
