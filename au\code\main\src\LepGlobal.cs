using lep.despatch.impl;
using System;
using System.Collections.Concurrent;
using System.IO;
using System.Linq;
using lep.configuration.impl;
using lep.job;
using lep.order;
using lep.run;
using System.Collections.Generic;
using lep.user;
using System.Diagnostics;

namespace lep
{
	public class LepGlobal
	{
		// static holder for instance, need to use lambda to construct since constructor private
		private static readonly Lazy<LepGlobal> _instance = new Lazy<LepGlobal>(() => new LepGlobal());

		// private to prevent direct instantiation.
		private LepGlobal()
		{
			RunModify = new ConcurrentDictionary<int, string>();
			JobModify = new ConcurrentDictionary<int, string>();
			ChangedJobs = new ConcurrentBag<int>();
		}

		// accessor for instance
		public static LepGlobal Instance => _instance.Value;

		public ConcurrentDictionary<int, string> RunModify { get; }
		public ConcurrentDictionary<int, string> JobModify { get; }

		public DateTime LastRunModify { get; set; } = DateTime.MinValue;
		public DateTime LastJobModify { get; set; } = DateTime.MinValue;

		//  public DateTime IObservable<DateTime>  LastRunModify2 { get; set; } = DateTime.MinValue;
		//  public DateTime LastJobModify { get; set; } = DateTime.MinValue;
		// public ConcurrentDictionary<int, bool> ChangedJobs { get; set; }
		// public ConcurrentQueue<int> ChangedRuns { get; set; }

		public ConcurrentBag<int> ChangedJobs { get; set; }

		public ConcurrentQueue<PrintItem> PrintQueue { get; set; } = new ConcurrentQueue<PrintItem>();

		public object JobBoardSyncRoot { get; set; } = new object();

		public void AddChangedJob(int id)
		{
			if (id == 0) return;
			var a = ChangedJobs.ToArray().ToList<int>();
			if (id != 0 && !a.Contains(id))
				ChangedJobs.Add(id);
		}

		private ConcurrentDictionary<string, DateTime> roleUserVsTime { get; set; } = new ConcurrentDictionary<string, DateTime>();

		public void LogAccess(string user, string role) => roleUserVsTime.AddOrUpdate(role + "-" + user, DateTime.Now, (k, ov) => DateTime.Now);

		// return active uses count in last 5 minutes
		public Tuple<int, int> GetUsersCount()
		{
			try
			{
				DateTime dt;
				var itemsToRemove = roleUserVsTime.Where(kvp => kvp.Value < DateTime.Now.AddMinutes(-5));
				foreach (var item in itemsToRemove)
				{
					roleUserVsTime.TryRemove(item.Key, out dt);
				}
				var staffCount = roleUserVsTime.Count(kvp => kvp.Key.StartsWith("Staff", StringComparison.CurrentCultureIgnoreCase));
				var customerCount = roleUserVsTime.Count(kvp => kvp.Key.StartsWith("Client", StringComparison.CurrentCultureIgnoreCase));
				return new Tuple<int, int>(customerCount, staffCount);
			}
			catch (Exception ex)
			{
				var m = ex.Message;
			}
			return new Tuple<int, int>(0, 0);
		}

		public string AppRootURL { get; set; }
		public string AbsolutePathURL { set; get; }
		public DirectoryInfo CacheDirectory { set; get; }
		public DirectoryInfo DataDirectory { set; get; }
		public DirectoryInfo CustomerLogoDirectory { get; set; }
		public DirectoryInfo OldDataDirectory { set; get; }
		public FileInfo ImportSpecFolds { set; get; }
		public FileInfo ImportSpecType { set; get; }
		public FileInfo ImportSpecRange { set; get; }
		public FileInfo ImportSpecPrint { set; get; }
		public string LepCrmWebserviceUrl { set; get; }
		public string CompDataWebservieUrl { set; get; }

		public string DataDirectoryPc { set; get; }
		public string DataDirectoryMac { set; get; }
		public string SupplyMasterConnectionString { get; set; }

		public string CourierURL_StarTrack { get; set; }
		public string CourierURL_FastWay { get; set; }
		public string CourierURL_AustPOST { get; set; }
		public string CourierURL_TNT { get; set; }

		public string CourierImage_StarTrack { get; set; }
		public string CourierImage_FastWay { get; set; }
		public string CourierImage_AustPOST { get; set; }
		public string CourierImage_TNT { get; set; }

		public string SmtpServer { get; set; }
		public string SmtpUsername { get; set; }
		public string SmtpPassword { get; set; }
		public int SmtpPort { get; set; }
		public bool SendMail { get; set; }

		public string LabelsLogoLabel { get; set; }
		public string LabelsPayMeLabel { get; set; }
		public string LabelsFillingLabel { get; set; }
		public string LabelsAddressA4Label { get; set; }
		public string LabelsSampleLabel { get; set; }
		public string LabelsPickupLabel { get; set; }

		public bool TestBox { get; set; }
		public string CourierImage_CouriersPlease { get; set; }
		public string CourierURL_CouriersPlease { get; set; }
		
		public string CourierImage_TOLLNQX { get; set; }
		public string CourierURL_TOLLNQX { get; set; }



		public string CourierImage_ARAMEX { get; set; }
		public string CourierURL_ARAMEX { get; set; }


		public const string TestPrinterXPS = "Microsoft XPS Document Writer";

		public string GhostScriptPath { get; set; }
		public string PdfTkPath { get; set; }
		public string LibTiffPath { get; set; }


		public string ArtworkDirectoryPC => $"{DataDirectoryPc}\\orders";
		public string ArtworkArchiveDirectoryPC => $"{DataDirectoryPc}\\orders";

		public string ArtworkDirectoryMac => $"{DataDirectoryMac}/orders";
		public string ArtworkArchiveDirectoryMac => $"{DataDirectoryMac}/orders";

		public string InvoicerPDFFolder { get; set; }

		public string PDF2PrinterPath { get; set; }
		public string GhostscriptDllPath { get; set; }

		public string ArtworkPath(IOrder order, IJob job, bool from_customer, string filename)
		{
			var dataDir = order.Status == OrderStatusOptions.Archived ? this.OldDataDirectory.FullName : this.DataDirectory.FullName;
			var from = from_customer ? "from-customer/" : "";
			return $"{dataDir}\\orders\\{job.Order.DateCreated:yyyyMMdd}\\{job.Order.OrderNr}\\{job.JobFolderName}\\{@from}{filename}".Replace("/", "\\");
		}


		//public string ArtworkURL (IJob job, bool from_customer, string filename)
		//{
		//	var from = from_customer ? "from-customer/" : "";
		//	return string.Format("{0}/artwork.ashx/{1:yyyyMMdd}/{2}/{3}/{4}{5}", _.AppRootURL, job.Order.DateCreated,
		//		job.Order.OrderNr, JobFolderName(job), from, filename);
		//}

		public string ArtworkURL(IRun run, bool isMac)
		{
			if (isMac)
			{
				return $"{DataDirectoryMac}/runs/{run.DateCreated:yyyyMMdd}/{run.RunNr}";
			}
			else
			{
				return $"{DataDirectoryPc}/runs/{run.DateCreated:yyyyMMdd}/{run.RunNr}";
			}
		}

		//public DirectoryInfo OldArtworkDirectory(IOrder order)
		//{
		//	return
		//		new DirectoryInfo(
		//			$"{_.OldDataDirectory.FullName}\\orders\\{order.DateCreated:yyyyMMdd}\\{order.OrderNr}");
		//}

		public DirectoryInfo ArtworkDirectory(IOrder order)
		{
			try
			{
				return new DirectoryInfo($"{DataDirectory.FullName}\\orders\\{order.DateCreated:yyyyMMdd}\\{order.OrderNr}");
			}
			catch (Exception ex)
			{
				var m = ex.Message;
				return null;
			}
		}

		public DirectoryInfo ArtworkDirectory(IRun run) => new DirectoryInfo($"{DataDirectory.FullName}\\runs\\{run.DateCreated:yyyyMMdd}\\{run.RunNr}");
		public DirectoryInfo ArtworkDirectory(IRun run, IJob job) => new DirectoryInfo($"{DataDirectory.FullName}\\runs\\{run.DateCreated:yyyyMMdd}\\{run.RunNr}\\{job.JobFolderName}");

		public DirectoryInfo ArtworkDirectory(IJob job, bool from_customer)
		{
			try
			{
				var dataDir = DataDirectory.FullName;
				var from = from_customer ? "\\from-customer" : "";
				return new DirectoryInfo($"{dataDir}\\orders\\{job.Order.DateCreated:yyyyMMdd}\\{job.Order.OrderNr}\\{job.JobFolderName}{@from}");
			}
			catch (Exception ex)
			{
				var m = ex.Message;
				return null;
			}
		}
		public IList<string> GetOrderExtraFiles(IOrder order)
		{
			var extraFiles = new List<string>();
			var dataDir = order.Status == OrderStatusOptions.Archived ?OldDataDirectory.FullName : DataDirectory.FullName;
			var orderExtraFilesFolder =	$"{dataDir}\\orders\\{order.DateCreated:yyyyMMdd}\\{order.OrderNr}\\Extrafiles".Replace("/", "\\");

			var dir = new DirectoryInfo(orderExtraFilesFolder);
			if (!dir.Exists)
				return extraFiles;

			extraFiles = dir.GetFiles("*.*", SearchOption.AllDirectories)
							.Where(fn => !fn.Name.Contains("thumb"))
							.OrderByDescending(fn => fn.LastWriteTime).Select(_ => _.Name).ToList();

			return extraFiles;

		}

		public string GetCustomerExtraFilesFolder(int customerId) => $"{InvoicerPDFFolder}\\Customers\\{customerId}";

		public IList<string> GetCustomerExtraFiles(ICustomerUser customer)
		{
			var extraFiles = new List<string>();
			string dataDir = InvoicerPDFFolder;
			var orderExtraFilesFolder = GetCustomerExtraFilesFolder(customer.Id).Replace("/", "\\");

			var dir = new DirectoryInfo(orderExtraFilesFolder);
			if (!dir.Exists)
				return extraFiles;

			extraFiles = dir.GetFiles("*.*", SearchOption.AllDirectories)
							.Where(fn => !fn.Name.Contains("thumb"))
							.OrderByDescending(fn => fn.LastWriteTime).Select(_ => _.Name).ToList();

			return extraFiles;
		}

		public DirectoryInfo GetOrderExtraFilesFolder(IOrder order)
		{
			var extraFiles = new List<string>();

			var dataDir = order.Status == OrderStatusOptions.Archived ?OldDataDirectory.FullName :DataDirectory.FullName;
			var orderExtraFilesFolder =
				$"{dataDir}\\orders\\{order.DateCreated:yyyyMMdd}\\{order.OrderNr}\\Extrafiles".Replace("/", "\\");
					
			var dir = new DirectoryInfo(orderExtraFilesFolder);
			if (!dir.Exists) dir.Create();
			return dir;



		}
		//public DirectoryInfo OldArtworkDirectory(IJob job, bool from_customer)
		//{
		//	var from = from_customer ? "\\from-customer" : ""; //"x:\\"
		//	return
		//		new DirectoryInfo(
		//			$"{_.OldDataDirectory.FullName}\\orders\\{job.Order.DateCreated:yyyyMMdd}\\{job.Order.OrderNr}\\{JobFolderName(job)}{@from}");
		//}


		private int byPrefix(string name)
		{
			if (name.IndexOf("_cover.1", StringComparison.OrdinalIgnoreCase) >= 0)
				return 1;
			if (name.IndexOf("_text.1", StringComparison.OrdinalIgnoreCase) >= 0)
				return 2;

			if (name.IndexOf("_ft", StringComparison.OrdinalIgnoreCase) >= 0)
				return 3;
			if (name.IndexOf("_bk", StringComparison.OrdinalIgnoreCase) >= 0)
				return 4;

			return 40;
		}

 		public IList<FileInfo> GetThumbs(IJob job)
		{
			var nothumbsResult = new List<FileInfo>();

			var artworkPath = LepGlobal.Instance.ArtworkPath(job.Order, job, true, "");

			var dir = new DirectoryInfo(artworkPath);
			if (!dir.Exists)
				return nothumbsResult;

			var l0 = dir.GetFiles("*.png", SearchOption.TopDirectoryOnly).ToList();

			var thumbs = new List<FileInfo>();
			thumbs.AddRange(l0);

			thumbs = thumbs.OrderBy(fn => fn.DirectoryName.Length).ThenBy(fn => byPrefix(fn.Name)).ToList();

			if (thumbs.Any())
				return thumbs.Take(2).ToList();

			return nothumbsResult;
		}


		public bool IsFileInUse(string filename)
		{
			try
			{
				using (FileStream f = File.Open(filename, FileMode.Open, FileAccess.Read, FileShare.None))
				{
					f.Close();
				}
			}
			catch (IOException ex)
			{
				return true;
			}
			return false;
		}


		public void PrintPDFToWindowsPrinter(string fileName, string printerName, int copies = 1)
		{
			//string gsExecutable = LepGlobal.Instance.GhostScriptPath;
			//string processArgs = $" -sDEVICE=mswinpr2 -dBATCH -dNOPAUSE -dNOPROMPT -dNoCancel -dPDFFitPage  -dNumCopies={copies} -sOutputFile=\"%printer%{printerName}\"  \"{fileName}\"";

			//var gsProcessInfo = new ProcessStartInfo
			//{
			//	WindowStyle = ProcessWindowStyle.Hidden,
			//	FileName = gsExecutable,
			//	Arguments = processArgs
			//};
			//using (var gsProcess = Process.Start(gsProcessInfo))
			//{
			//	gsProcess.WaitForExit();
			//}


			var pdfToPrinterPath = LepGlobal.Instance.PDF2PrinterPath;
			var arguments = $@"  ""{fileName}""  ""{printerName}""  ";

			for(int i=0; i< copies; i++)
			{
				var pi = new ProcessStartInfo()
				{
					UseShellExecute = true,
					FileName = pdfToPrinterPath,
					Arguments = arguments
				};
				using (var pp = Process.Start(pi))
				{
					pp.WaitForExit();
				}				
			}


		}


	}
}
