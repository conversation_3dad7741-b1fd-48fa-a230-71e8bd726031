[2025-06-17 18:10:58.511 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 18:10:58.537 +10:00 INF] Initializing FastReport...
[2025-06-17 18:10:58.607 +10:00 INF] FastReport initialized successfully
[2025-06-17 18:10:59.109 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 18:11:00.452 +10:00 INF] Starting LEP Invoicer at "2025-06-17T18:11:00.4517194+10:00"
[2025-06-17 18:11:00.455 +10:00 INF] Initializing database service...
[2025-06-17 18:11:00.458 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 18:11:00.556 +10:00 INF] Database connection established successfully
[2025-06-17 18:11:00.557 +10:00 INF] Database service initialized successfully
[2025-06-17 18:11:00.561 +10:00 INF] Checking for pending work...
[2025-06-17 18:11:00.565 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 18:11:01.586 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 18:11:01.591 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 18:11:01.606 +10:00 INF] Found 0 credits to invoice
[2025-06-17 18:11:01.608 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 18:11:01.622 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 18:11:01.626 +10:00 INF] No pending work found
[2025-06-17 18:11:01.628 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 18:11:01.630 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 18:11:01.749 +10:00 INF] LEP Invoicer completed successfully in 1297ms. No work to process.
[2025-06-17 18:11:01.756 +10:00 INF] Database connection disposed
[2025-06-17 18:11:01.758 +10:00 INF] LEP Invoicer completed with result: 0
