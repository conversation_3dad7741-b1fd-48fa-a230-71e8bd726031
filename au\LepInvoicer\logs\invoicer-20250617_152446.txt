[2025-06-17 15:24:46.677 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 15:24:46.713 +10:00 INF] Initializing FastReport...
[2025-06-17 15:24:46.822 +10:00 INF] FastReport initialized successfully
[2025-06-17 15:24:47.517 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 15:24:49.608 +10:00 INF] Starting LEP Invoicer at "2025-06-17T15:24:49.6076138+10:00"
[2025-06-17 15:24:49.710 +10:00 INF] Initializing database service...
[2025-06-17 15:24:49.713 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 15:24:49.850 +10:00 INF] Database connection established successfully
[2025-06-17 15:24:49.852 +10:00 INF] Database service initialized successfully
[2025-06-17 15:24:49.855 +10:00 INF] Checking for pending work...
[2025-06-17 15:24:49.858 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 15:24:50.907 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 15:24:50.911 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 15:24:50.923 +10:00 INF] Found 0 credits to invoice
[2025-06-17 15:24:50.926 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 15:24:50.929 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 15:24:50.930 +10:00 INF] No pending work found
[2025-06-17 15:24:50.932 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 15:24:50.933 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 15:24:51.052 +10:00 INF] LEP Invoicer completed successfully in 1444ms. No work to process.
[2025-06-17 15:24:51.059 +10:00 INF] Database connection disposed
[2025-06-17 15:24:51.063 +10:00 INF] LEP Invoicer completed with result: 0
