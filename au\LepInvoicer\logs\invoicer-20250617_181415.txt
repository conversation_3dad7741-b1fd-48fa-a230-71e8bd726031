[2025-06-17 18:14:15.677 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 18:14:15.714 +10:00 INF] Initializing FastReport...
[2025-06-17 18:14:15.809 +10:00 INF] FastReport initialized successfully
[2025-06-17 18:14:16.309 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 18:14:17.592 +10:00 INF] Starting LEP Invoicer at "2025-06-17T18:14:17.5918803+10:00"
[2025-06-17 18:14:17.595 +10:00 INF] Initializing database service...
[2025-06-17 18:14:17.599 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 18:14:17.698 +10:00 INF] Database connection established successfully
[2025-06-17 18:14:17.700 +10:00 INF] Database service initialized successfully
[2025-06-17 18:14:17.703 +10:00 INF] Checking for pending work...
[2025-06-17 18:14:17.706 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 18:14:18.604 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 18:14:18.607 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 18:14:18.622 +10:00 INF] Found 0 credits to invoice
[2025-06-17 18:14:18.624 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 18:14:18.628 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 18:14:18.630 +10:00 INF] No pending work found
[2025-06-17 18:14:18.631 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 18:14:18.635 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 18:14:18.713 +10:00 INF] LEP Invoicer completed successfully in 1121ms. No work to process.
[2025-06-17 18:14:18.719 +10:00 INF] Database connection disposed
[2025-06-17 18:14:18.721 +10:00 INF] LEP Invoicer completed with result: 0
