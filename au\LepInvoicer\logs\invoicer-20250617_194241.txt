[2025-06-17 19:42:41.586 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 19:42:41.615 +10:00 INF] Initializing FastReport...
[2025-06-17 19:42:41.695 +10:00 INF] FastReport initialized successfully
[2025-06-17 19:42:42.131 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 19:42:43.525 +10:00 INF] Starting LEP Invoicer at "2025-06-17T19:42:43.5251529+10:00"
[2025-06-17 19:42:43.531 +10:00 INF] Initializing database service...
[2025-06-17 19:42:43.534 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 19:42:43.635 +10:00 INF] Database connection established successfully
[2025-06-17 19:42:43.636 +10:00 INF] Database service initialized successfully
[2025-06-17 19:42:43.640 +10:00 INF] Checking for pending work...
[2025-06-17 19:42:43.643 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 19:42:44.541 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 19:42:44.544 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 19:42:44.559 +10:00 INF] Found 0 credits to invoice
[2025-06-17 19:42:44.561 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 19:42:44.565 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 19:42:44.567 +10:00 INF] No pending work found
[2025-06-17 19:42:44.568 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 19:42:44.569 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 19:42:44.672 +10:00 INF] LEP Invoicer completed successfully in 1147ms. No work to process.
[2025-06-17 19:42:44.679 +10:00 INF] Database connection disposed
[2025-06-17 19:42:44.681 +10:00 INF] LEP Invoicer completed with result: 0
