[2025-06-17 18:54:38.305 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 18:54:38.335 +10:00 INF] Initializing FastReport...
[2025-06-17 18:54:38.412 +10:00 INF] FastReport initialized successfully
[2025-06-17 18:54:38.834 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 18:54:40.143 +10:00 INF] Starting LEP Invoicer at "2025-06-17T18:54:40.1431503+10:00"
[2025-06-17 18:54:40.147 +10:00 INF] Initializing database service...
[2025-06-17 18:54:40.151 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 18:54:40.260 +10:00 INF] Database connection established successfully
[2025-06-17 18:54:40.262 +10:00 INF] Database service initialized successfully
[2025-06-17 18:54:40.265 +10:00 INF] Checking for pending work...
[2025-06-17 18:54:40.268 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 18:54:41.274 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 18:54:41.277 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 18:54:41.301 +10:00 INF] Found 0 credits to invoice
[2025-06-17 18:54:41.304 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 18:54:41.308 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 18:54:41.309 +10:00 INF] No pending work found
[2025-06-17 18:54:41.310 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 18:54:41.314 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 18:54:41.419 +10:00 INF] LEP Invoicer completed successfully in 1275ms. No work to process.
[2025-06-17 18:54:41.427 +10:00 INF] Database connection disposed
[2025-06-17 18:54:41.430 +10:00 INF] LEP Invoicer completed with result: 0
