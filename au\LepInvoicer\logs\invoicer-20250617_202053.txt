[2025-06-17 20:20:53.573 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 20:20:53.600 +10:00 INF] Initializing FastReport...
[2025-06-17 20:20:53.673 +10:00 INF] FastReport initialized successfully
[2025-06-17 20:20:54.116 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 20:20:55.403 +10:00 INF] Starting LEP Invoicer at "2025-06-17T20:20:55.4033533+10:00"
[2025-06-17 20:20:55.406 +10:00 INF] Initializing database service...
[2025-06-17 20:20:55.409 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 20:20:55.509 +10:00 INF] Database connection established successfully
[2025-06-17 20:20:55.511 +10:00 INF] Database service initialized successfully
[2025-06-17 20:20:55.514 +10:00 INF] Checking for pending work...
[2025-06-17 20:20:55.517 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 20:20:56.368 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 20:20:56.371 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 20:20:56.383 +10:00 INF] Found 0 credits to invoice
[2025-06-17 20:20:56.385 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 20:20:56.389 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 20:20:56.391 +10:00 INF] No pending work found
[2025-06-17 20:20:56.392 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 20:20:56.393 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 20:20:56.472 +10:00 INF] LEP Invoicer completed successfully in 1068ms. No work to process.
[2025-06-17 20:20:56.478 +10:00 INF] Database connection disposed
[2025-06-17 20:20:56.480 +10:00 INF] LEP Invoicer completed with result: 0
