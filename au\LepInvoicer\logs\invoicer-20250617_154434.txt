[2025-06-17 15:44:34.713 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 15:44:34.743 +10:00 INF] Initializing FastReport...
[2025-06-17 15:44:34.816 +10:00 INF] FastReport initialized successfully
[2025-06-17 15:44:35.251 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 15:44:36.617 +10:00 INF] Starting LEP Invoicer at "2025-06-17T15:44:36.6173754+10:00"
[2025-06-17 15:44:36.621 +10:00 INF] Initializing database service...
[2025-06-17 15:44:36.624 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 15:44:36.726 +10:00 INF] Database connection established successfully
[2025-06-17 15:44:36.729 +10:00 INF] Database service initialized successfully
[2025-06-17 15:44:36.732 +10:00 INF] Checking for pending work...
[2025-06-17 15:44:36.735 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 15:44:37.637 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 15:44:37.640 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 15:44:37.652 +10:00 INF] Found 0 credits to invoice
[2025-06-17 15:44:37.655 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 15:44:37.659 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 15:44:37.660 +10:00 INF] No pending work found
[2025-06-17 15:44:37.662 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 15:44:37.663 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 15:44:37.745 +10:00 INF] LEP Invoicer completed successfully in 1128ms. No work to process.
[2025-06-17 15:44:37.751 +10:00 INF] Database connection disposed
[2025-06-17 15:44:37.753 +10:00 INF] LEP Invoicer completed with result: 0
