[2025-06-17 16:38:18.623 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 16:38:18.660 +10:00 INF] Initializing FastReport...
[2025-06-17 16:38:18.749 +10:00 INF] FastReport initialized successfully
[2025-06-17 16:38:19.201 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 16:38:20.700 +10:00 INF] Starting LEP Invoicer at "2025-06-17T16:38:20.7005434+10:00"
[2025-06-17 16:38:20.704 +10:00 INF] Initializing database service...
[2025-06-17 16:38:20.707 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 16:38:20.828 +10:00 INF] Database connection established successfully
[2025-06-17 16:38:20.829 +10:00 INF] Database service initialized successfully
[2025-06-17 16:38:20.832 +10:00 INF] Checking for pending work...
[2025-06-17 16:38:20.835 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 16:38:21.814 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 16:38:21.817 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 16:38:21.829 +10:00 INF] Found 0 credits to invoice
[2025-06-17 16:38:21.831 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 16:38:21.837 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 16:38:21.839 +10:00 INF] No pending work found
[2025-06-17 16:38:21.840 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 16:38:21.843 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 16:38:21.927 +10:00 INF] LEP Invoicer completed successfully in 1227ms. No work to process.
[2025-06-17 16:38:21.934 +10:00 INF] Database connection disposed
[2025-06-17 16:38:21.936 +10:00 INF] LEP Invoicer completed with result: 0
