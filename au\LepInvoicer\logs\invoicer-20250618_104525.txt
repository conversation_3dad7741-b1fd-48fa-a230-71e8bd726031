[2025-06-18 10:45:25.950 +10:00 INF] Starting LEP Invoicer application
[2025-06-18 10:45:25.992 +10:00 INF] Initializing FastReport...
[2025-06-18 10:45:26.072 +10:00 INF] FastReport initialized successfully
[2025-06-18 10:45:26.601 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-18 10:45:28.172 +10:00 INF] Starting LEP Invoicer at "2025-06-18T10:45:28.1721473+10:00"
[2025-06-18 10:45:28.175 +10:00 INF] Initializing database service...
[2025-06-18 10:45:28.178 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-18 10:45:28.326 +10:00 INF] Database connection established successfully
[2025-06-18 10:45:28.327 +10:00 INF] Database service initialized successfully
[2025-06-18 10:45:28.329 +10:00 INF] Checking for pending work...
[2025-06-18 10:45:28.333 +10:00 INF] Getting 500 orders to invoice
[2025-06-18 10:45:29.394 +10:00 INF] Found 0 orders to invoice (filtered 17 candidates)
[2025-06-18 10:45:29.406 +10:00 INF] Getting 100 credits to invoice
[2025-06-18 10:45:29.420 +10:00 INF] Found 0 credits to invoice
[2025-06-18 10:45:29.422 +10:00 INF] Getting 100 refunds to invoice
[2025-06-18 10:45:29.425 +10:00 INF] Found 0 refunds to invoice
[2025-06-18 10:45:29.427 +10:00 INF] No pending work found
[2025-06-18 10:45:29.427 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-18 10:45:29.429 +10:00 INF] Cleaning up invoicer logs...
[2025-06-18 10:45:29.501 +10:00 INF] LEP Invoicer completed successfully in 1329ms. No work to process.
[2025-06-18 10:45:29.508 +10:00 INF] Database connection disposed
[2025-06-18 10:45:29.510 +10:00 INF] LEP Invoicer completed with result: 0
