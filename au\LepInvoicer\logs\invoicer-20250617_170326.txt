[2025-06-17 17:03:26.588 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 17:03:26.615 +10:00 INF] Initializing FastReport...
[2025-06-17 17:03:26.699 +10:00 INF] FastReport initialized successfully
[2025-06-17 17:03:27.201 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 17:03:28.463 +10:00 INF] Starting LEP Invoicer at "2025-06-17T17:03:28.4630563+10:00"
[2025-06-17 17:03:28.466 +10:00 INF] Initializing database service...
[2025-06-17 17:03:28.469 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 17:03:28.566 +10:00 INF] Database connection established successfully
[2025-06-17 17:03:28.567 +10:00 INF] Database service initialized successfully
[2025-06-17 17:03:28.570 +10:00 INF] Checking for pending work...
[2025-06-17 17:03:28.573 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 17:03:29.490 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 17:03:29.493 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 17:03:29.506 +10:00 INF] Found 0 credits to invoice
[2025-06-17 17:03:29.508 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 17:03:29.512 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 17:03:29.513 +10:00 INF] No pending work found
[2025-06-17 17:03:29.514 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 17:03:29.516 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 17:03:29.590 +10:00 INF] LEP Invoicer completed successfully in 1127ms. No work to process.
[2025-06-17 17:03:29.597 +10:00 INF] Database connection disposed
[2025-06-17 17:03:29.599 +10:00 INF] LEP Invoicer completed with result: 0
