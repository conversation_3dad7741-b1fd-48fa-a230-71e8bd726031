[2025-06-17 17:42:39.481 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 17:42:39.507 +10:00 INF] Initializing FastReport...
[2025-06-17 17:42:39.586 +10:00 INF] FastReport initialized successfully
[2025-06-17 17:42:39.979 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 17:42:41.223 +10:00 INF] Starting LEP Invoicer at "2025-06-17T17:42:41.2226656+10:00"
[2025-06-17 17:42:41.226 +10:00 INF] Initializing database service...
[2025-06-17 17:42:41.232 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 17:42:41.331 +10:00 INF] Database connection established successfully
[2025-06-17 17:42:41.333 +10:00 INF] Database service initialized successfully
[2025-06-17 17:42:41.335 +10:00 INF] Checking for pending work...
[2025-06-17 17:42:41.338 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 17:42:42.246 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 17:42:42.250 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 17:42:42.263 +10:00 INF] Found 0 credits to invoice
[2025-06-17 17:42:42.265 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 17:42:42.269 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 17:42:42.272 +10:00 INF] No pending work found
[2025-06-17 17:42:42.273 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 17:42:42.275 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 17:42:42.358 +10:00 INF] LEP Invoicer completed successfully in 1135ms. No work to process.
[2025-06-17 17:42:42.366 +10:00 INF] Database connection disposed
[2025-06-17 17:42:42.369 +10:00 INF] LEP Invoicer completed with result: 0
