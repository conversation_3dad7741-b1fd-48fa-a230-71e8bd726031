[2025-06-18 07:49:59.559 +10:00 INF] Starting LEP Invoicer application
[2025-06-18 07:49:59.587 +10:00 INF] Initializing FastReport...
[2025-06-18 07:49:59.661 +10:00 INF] FastReport initialized successfully
[2025-06-18 07:50:00.105 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-18 07:50:01.423 +10:00 INF] Starting LEP Invoicer at "2025-06-18T07:50:01.4231303+10:00"
[2025-06-18 07:50:01.428 +10:00 INF] Initializing database service...
[2025-06-18 07:50:01.441 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-18 07:50:01.545 +10:00 INF] Database connection established successfully
[2025-06-18 07:50:01.546 +10:00 INF] Database service initialized successfully
[2025-06-18 07:50:01.550 +10:00 INF] Checking for pending work...
[2025-06-18 07:50:01.553 +10:00 INF] Getting 500 orders to invoice
[2025-06-18 07:50:02.570 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-18 07:50:02.576 +10:00 INF] Getting 100 credits to invoice
[2025-06-18 07:50:02.592 +10:00 INF] Found 0 credits to invoice
[2025-06-18 07:50:02.594 +10:00 INF] Getting 100 refunds to invoice
[2025-06-18 07:50:02.599 +10:00 INF] Found 0 refunds to invoice
[2025-06-18 07:50:02.601 +10:00 INF] No pending work found
[2025-06-18 07:50:02.604 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-18 07:50:02.606 +10:00 INF] Cleaning up invoicer logs...
[2025-06-18 07:50:02.689 +10:00 INF] LEP Invoicer completed successfully in 1265ms. No work to process.
[2025-06-18 07:50:02.695 +10:00 INF] Database connection disposed
[2025-06-18 07:50:02.703 +10:00 INF] LEP Invoicer completed with result: 0
