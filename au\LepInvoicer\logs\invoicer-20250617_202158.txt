[2025-06-17 20:21:58.568 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 20:21:58.600 +10:00 INF] Initializing FastReport...
[2025-06-17 20:21:58.687 +10:00 INF] FastReport initialized successfully
[2025-06-17 20:21:59.162 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 20:22:00.456 +10:00 INF] Starting LEP Invoicer at "2025-06-17T20:22:00.4557659+10:00"
[2025-06-17 20:22:00.459 +10:00 INF] Initializing database service...
[2025-06-17 20:22:00.462 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 20:22:00.567 +10:00 INF] Database connection established successfully
[2025-06-17 20:22:00.568 +10:00 INF] Database service initialized successfully
[2025-06-17 20:22:00.571 +10:00 INF] Checking for pending work...
[2025-06-17 20:22:00.574 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 20:22:01.518 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 20:22:01.521 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 20:22:01.535 +10:00 INF] Found 0 credits to invoice
[2025-06-17 20:22:01.543 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 20:22:01.546 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 20:22:01.551 +10:00 INF] No pending work found
[2025-06-17 20:22:01.553 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 20:22:01.556 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 20:22:01.653 +10:00 INF] LEP Invoicer completed successfully in 1197ms. No work to process.
[2025-06-17 20:22:01.660 +10:00 INF] Database connection disposed
[2025-06-17 20:22:01.662 +10:00 INF] LEP Invoicer completed with result: 0
