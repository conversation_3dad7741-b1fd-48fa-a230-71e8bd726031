[2025-06-18 10:14:33.712 +10:00 INF] Starting LEP Invoicer application
[2025-06-18 10:14:33.760 +10:00 INF] Initializing FastReport...
[2025-06-18 10:14:33.856 +10:00 INF] FastReport initialized successfully
[2025-06-18 10:14:34.379 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-18 10:14:35.803 +10:00 INF] Starting LEP Invoicer at "2025-06-18T10:14:35.8032571+10:00"
[2025-06-18 10:14:35.810 +10:00 INF] Initializing database service...
[2025-06-18 10:14:35.812 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-18 10:14:35.916 +10:00 INF] Database connection established successfully
[2025-06-18 10:14:35.917 +10:00 INF] Database service initialized successfully
[2025-06-18 10:14:35.920 +10:00 INF] Checking for pending work...
[2025-06-18 10:14:35.923 +10:00 INF] Getting 500 orders to invoice
[2025-06-18 10:14:36.847 +10:00 INF] Found 0 orders to invoice (filtered 16 candidates)
[2025-06-18 10:14:36.850 +10:00 INF] Getting 100 credits to invoice
[2025-06-18 10:14:36.862 +10:00 INF] Found 0 credits to invoice
[2025-06-18 10:14:36.865 +10:00 INF] Getting 100 refunds to invoice
[2025-06-18 10:14:36.869 +10:00 INF] Found 0 refunds to invoice
[2025-06-18 10:14:36.870 +10:00 INF] No pending work found
[2025-06-18 10:14:36.871 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-18 10:14:36.873 +10:00 INF] Cleaning up invoicer logs...
[2025-06-18 10:14:36.965 +10:00 INF] LEP Invoicer completed successfully in 1161ms. No work to process.
[2025-06-18 10:14:36.971 +10:00 INF] Database connection disposed
[2025-06-18 10:14:36.973 +10:00 INF] LEP Invoicer completed with result: 0
