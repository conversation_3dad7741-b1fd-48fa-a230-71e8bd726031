[2025-06-17 17:25:11.589 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 17:25:11.650 +10:00 INF] Initializing FastReport...
[2025-06-17 17:25:11.741 +10:00 INF] FastReport initialized successfully
[2025-06-17 17:25:12.177 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 17:25:13.470 +10:00 INF] Starting LEP Invoicer at "2025-06-17T17:25:13.4704559+10:00"
[2025-06-17 17:25:13.474 +10:00 INF] Initializing database service...
[2025-06-17 17:25:13.477 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 17:25:13.580 +10:00 INF] Database connection established successfully
[2025-06-17 17:25:13.581 +10:00 INF] Database service initialized successfully
[2025-06-17 17:25:13.584 +10:00 INF] Checking for pending work...
[2025-06-17 17:25:13.587 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 17:25:14.448 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 17:25:14.450 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 17:25:14.462 +10:00 INF] Found 0 credits to invoice
[2025-06-17 17:25:14.464 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 17:25:14.467 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 17:25:14.470 +10:00 INF] No pending work found
[2025-06-17 17:25:14.471 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 17:25:14.473 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 17:25:14.548 +10:00 INF] LEP Invoicer completed successfully in 1077ms. No work to process.
[2025-06-17 17:25:14.555 +10:00 INF] Database connection disposed
[2025-06-17 17:25:14.556 +10:00 INF] LEP Invoicer completed with result: 0
