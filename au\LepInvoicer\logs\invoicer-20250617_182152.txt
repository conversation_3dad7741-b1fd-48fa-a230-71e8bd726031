[2025-06-17 18:21:52.482 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 18:21:52.520 +10:00 INF] Initializing FastReport...
[2025-06-17 18:21:52.606 +10:00 INF] FastReport initialized successfully
[2025-06-17 18:21:53.026 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 18:21:54.228 +10:00 INF] Starting LEP Invoicer at "2025-06-17T18:21:54.2277986+10:00"
[2025-06-17 18:21:54.231 +10:00 INF] Initializing database service...
[2025-06-17 18:21:54.234 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 18:21:54.329 +10:00 INF] Database connection established successfully
[2025-06-17 18:21:54.330 +10:00 INF] Database service initialized successfully
[2025-06-17 18:21:54.332 +10:00 INF] Checking for pending work...
[2025-06-17 18:21:54.335 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 18:21:55.365 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 18:21:55.369 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 18:21:55.381 +10:00 INF] Found 0 credits to invoice
[2025-06-17 18:21:55.383 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 18:21:55.388 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 18:21:55.390 +10:00 INF] No pending work found
[2025-06-17 18:21:55.391 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 18:21:55.393 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 18:21:55.475 +10:00 INF] LEP Invoicer completed successfully in 1247ms. No work to process.
[2025-06-17 18:21:55.481 +10:00 INF] Database connection disposed
[2025-06-17 18:21:55.483 +10:00 INF] LEP Invoicer completed with result: 0
