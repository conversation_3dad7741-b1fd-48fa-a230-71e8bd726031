[2025-06-10 19:22:21.938 +10:00 INF] Starting LEP Invoicer application
[2025-06-10 19:22:23.251 +10:00 INF] Loaded MYOB cache: 0 accounts, 0 tax codes, 0 customers
[2025-06-10 19:22:26.070 +10:00 INF] Starting LEP Invoicer at "2025-06-10T19:22:26.0703301+10:00"
[2025-06-10 19:22:26.076 +10:00 INF] Initializing services...
[2025-06-10 19:22:26.139 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-10 19:22:26.365 +10:00 INF] Database connection established successfully
[2025-06-10 19:22:26.377 +10:00 INF] Initializing MYOB service...
[2025-06-10 19:22:26.381 +10:00 INF] Initializing MYOB with OAuth authentication
[2025-06-10 19:22:26.386 +10:00 INF] Starting OAuth authentication flow
[2025-06-10 19:22:26.546 +10:00 INF] Using existing OAuth tokens
[2025-06-10 19:22:26.548 +10:00 INF] Initializing MYOB services with OAuth keystore
[2025-06-10 19:22:26.555 +10:00 INF] MYOB services initialized successfully
[2025-06-10 19:22:26.557 +10:00 INF] OAuth keystore set for API calls
[2025-06-10 19:22:26.558 +10:00 INF] Getting company files from MYOB
[2025-06-10 19:22:27.358 +10:00 INF] MYOB service initialized with OAuth credentials. Using company file: LEP Colour Printers Pty Ltd
[2025-06-10 19:22:27.372 +10:00 INF] Current cache stats: {"AccountsCount":0,"TaxCodesCount":0,"CustomersCount":0,"LastCacheLoad":"2025-06-10T19:22:23.2325242+10:00","CacheAge":"00:00:04.1326840","IsExpired":false,"CacheDirectory":"C:\\LepSF\\au\\LepInvoicer\\bin\\Debug\\net8.0-windows7.0\\cache"}
[2025-06-10 19:22:27.399 +10:00 INF] Using cached MYOB data: {"AccountsCount":0,"TaxCodesCount":0,"CustomersCount":0,"LastCacheLoad":"2025-06-10T19:22:23.2325242+10:00","CacheAge":"00:00:04.1326840","IsExpired":false,"CacheDirectory":"C:\\LepSF\\au\\LepInvoicer\\bin\\Debug\\net8.0-windows7.0\\cache"}
[2025-06-10 19:22:28.216 +10:00 INF] GST tax code not in cache, fetching from MYOB API
[2025-06-10 19:22:28.602 +10:00 INF] Cached GST tax code from MYOB API
[2025-06-10 19:22:28.604 +10:00 INF] Freight account not in cache, fetching from MYOB API
[2025-06-10 19:22:28.978 +10:00 INF] Cached freight account from MYOB API: Freight recovered
[2025-06-10 19:22:29.134 +10:00 INF] Discounts account not in cache, fetching from MYOB API
[2025-06-10 19:22:30.213 +10:00 INF] Cached discounts account from MYOB API: Discounts Allowed
[2025-06-10 19:22:30.367 +10:00 INF] MYOB cache initialization complete: {"AccountsCount":0,"TaxCodesCount":0,"CustomersCount":0,"LastCacheLoad":"2025-06-10T19:22:23.2325242+10:00","CacheAge":"00:00:07.1344817","IsExpired":false,"CacheDirectory":"C:\\LepSF\\au\\LepInvoicer\\bin\\Debug\\net8.0-windows7.0\\cache"}
[2025-06-10 19:22:30.380 +10:00 INF] MYOB service initialized successfully with company file: LEP Colour Printers Pty Ltd
[2025-06-10 19:22:30.385 +10:00 INF] Services initialized successfully
[2025-06-10 19:22:30.402 +10:00 INF] Processing order invoices...
[2025-06-10 19:22:30.407 +10:00 INF] Getting 500 orders to invoice
[2025-06-10 19:22:31.347 +10:00 INF] Found 2 orders to invoice
[2025-06-10 19:22:31.350 +10:00 INF] Found 2 orders to process
[2025-06-10 19:22:31.353 +10:00 INF] Getting order 1417006
[2025-06-10 19:22:31.690 +10:00 INF] Processing order 1417006 with 1 jobs, total: ¤0.00
[2025-06-10 19:22:31.696 +10:00 WRN] Order 1417006 has zero price, marking as invoiced
[2025-06-10 19:22:31.699 +10:00 INF] Getting order 1416838
[2025-06-10 19:22:31.711 +10:00 INF] Processing order 1416838 with 1 jobs, total: ¤0.00
[2025-06-10 19:22:31.713 +10:00 WRN] Order 1416838 has zero price, marking as invoiced
[2025-06-10 19:22:31.716 +10:00 INF] Order processing completed. Processed: 2, Success: 0, Failed: 2
[2025-06-10 19:22:31.721 +10:00 INF] Processing credit invoices...
[2025-06-10 19:22:31.725 +10:00 INF] Getting 100 credits to invoice
[2025-06-10 19:22:31.750 +10:00 INF] Found 0 credits to invoice
[2025-06-10 19:22:31.755 +10:00 INF] Processing refund invoices...
[2025-06-10 19:22:31.758 +10:00 INF] Getting 100 refunds to invoice
[2025-06-10 19:22:31.772 +10:00 INF] Found 0 refunds to invoice
[2025-06-10 19:22:31.774 +10:00 INF] Cleaning up invoicer logs...
[2025-06-10 19:22:31.855 +10:00 INF] LEP Invoicer completed successfully in 5785ms. Orders: 2, Credits: 0, Refunds: 0
[2025-06-10 19:22:31.867 +10:00 INF] Database connection disposed
[2025-06-10 19:22:31.869 +10:00 INF] LEP Invoicer completed with result: 0
