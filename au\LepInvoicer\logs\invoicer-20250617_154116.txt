[2025-06-17 15:41:16.708 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 15:41:16.736 +10:00 INF] Initializing FastReport...
[2025-06-17 15:41:16.822 +10:00 INF] FastReport initialized successfully
[2025-06-17 15:41:17.253 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 15:41:18.620 +10:00 INF] Starting LEP Invoicer at "2025-06-17T15:41:18.6196058+10:00"
[2025-06-17 15:41:18.623 +10:00 INF] Initializing database service...
[2025-06-17 15:41:18.626 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 15:41:18.732 +10:00 INF] Database connection established successfully
[2025-06-17 15:41:18.733 +10:00 INF] Database service initialized successfully
[2025-06-17 15:41:18.736 +10:00 INF] Checking for pending work...
[2025-06-17 15:41:18.739 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 15:41:19.657 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 15:41:19.669 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 15:41:19.688 +10:00 INF] Found 0 credits to invoice
[2025-06-17 15:41:19.690 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 15:41:19.694 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 15:41:19.695 +10:00 INF] No pending work found
[2025-06-17 15:41:19.696 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 15:41:19.698 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 15:41:19.787 +10:00 INF] LEP Invoicer completed successfully in 1167ms. No work to process.
[2025-06-17 15:41:19.793 +10:00 INF] Database connection disposed
[2025-06-17 15:41:19.795 +10:00 INF] LEP Invoicer completed with result: 0
