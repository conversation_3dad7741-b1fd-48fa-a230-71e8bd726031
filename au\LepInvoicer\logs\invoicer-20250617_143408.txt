[2025-06-17 14:34:08.509 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 14:34:08.538 +10:00 INF] Initializing FastReport...
[2025-06-17 14:34:08.620 +10:00 INF] FastReport initialized successfully
[2025-06-17 14:34:09.061 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 14:34:10.325 +10:00 INF] Starting LEP Invoicer at "2025-06-17T14:34:10.3254768+10:00"
[2025-06-17 14:34:10.329 +10:00 INF] Initializing database service...
[2025-06-17 14:34:10.332 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 14:34:10.431 +10:00 INF] Database connection established successfully
[2025-06-17 14:34:10.433 +10:00 INF] Database service initialized successfully
[2025-06-17 14:34:10.436 +10:00 INF] Checking for pending work...
[2025-06-17 14:34:10.439 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 14:34:11.404 +10:00 INF] Found 2 orders to invoice (filtered 17 candidates)
[2025-06-17 14:34:11.408 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 14:34:11.438 +10:00 INF] Found 0 credits to invoice
[2025-06-17 14:34:11.441 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 14:34:11.479 +10:00 INF] Found 16 refunds to invoice
[2025-06-17 14:34:11.485 +10:00 INF] Found pending work: 2 orders, 16 refunds
[2025-06-17 14:34:11.487 +10:00 INF] Initializing MYOB and other services...
[2025-06-17 14:34:11.490 +10:00 INF] Initializing MYOB service...
[2025-06-17 14:34:11.492 +10:00 INF] Initializing MYOB with OAuth authentication
[2025-06-17 14:34:11.500 +10:00 INF] Starting OAuth authentication flow
[2025-06-17 14:34:11.513 +10:00 INF] Using existing OAuth tokens
[2025-06-17 14:34:11.514 +10:00 INF] Initializing MYOB services with OAuth keystore
[2025-06-17 14:34:11.518 +10:00 INF] MYOB services initialized successfully
[2025-06-17 14:34:11.519 +10:00 INF] OAuth keystore set for API calls
[2025-06-17 14:34:11.521 +10:00 INF] Getting company files from MYOB
[2025-06-17 14:34:11.837 +10:00 INF] MYOB service initialized with OAuth credentials. Using company file: LEP Colour Printers Pty Ltd
[2025-06-17 14:34:12.456 +10:00 INF] MYOB service initialized successfully with company file: LEP Colour Printers Pty Ltd
[2025-06-17 14:34:12.458 +10:00 INF] All services initialized successfully
[2025-06-17 14:34:12.461 +10:00 INF] Processing order invoices...
[2025-06-17 14:34:12.462 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 14:34:12.808 +10:00 INF] Found 2 orders to invoice (filtered 17 candidates)
[2025-06-17 14:34:12.810 +10:00 INF] Found 2 orders to process
[2025-06-17 14:34:12.812 +10:00 INF] Getting order 1418038
[2025-06-17 14:34:12.834 +10:00 INF] Processing order 1418038 with 1 jobs, total: ¤64.76
[2025-06-17 14:34:12.839 +10:00 INF] Creating order invoice for order 1418038
[2025-06-17 14:34:14.388 +10:00 INF] Successfully created MYOB invoice O1418038 for order 1418038
[2025-06-17 14:34:14.390 +10:00 INF] Successfully created MYOB invoice for order 1418038
[2025-06-17 14:34:14.401 +10:00 INF] Generating PDF invoice for order 1418038 at \\dfs01\resource\invoices\2025/Jun/17\O1418038.pdf
[2025-06-17 14:34:16.297 +10:00 INF] Successfully generated PDF for order 1418038
[2025-06-17 14:34:16.349 +10:00 WRN] No email address found for order 1418038
[2025-06-17 14:34:16.356 +10:00 INF] Successfully processed order 1418038 (MYOB + PDF)
[2025-06-17 14:34:16.361 +10:00 INF] Getting order 1417987
[2025-06-17 14:34:16.366 +10:00 INF] Processing order 1417987 with 1 jobs, total: ¤65.46
[2025-06-17 14:34:16.368 +10:00 INF] Creating order invoice for order 1417987
[2025-06-17 14:34:17.462 +10:00 INF] Successfully created MYOB invoice O1417987 for order 1417987
[2025-06-17 14:34:17.464 +10:00 INF] Successfully created MYOB invoice for order 1417987
[2025-06-17 14:34:17.466 +10:00 INF] Generating PDF invoice for order 1417987 at \\dfs01\resource\invoices\2025/Jun/17\O1417987.pdf
[2025-06-17 14:34:17.824 +10:00 INF] Successfully generated PDF for order 1417987
[2025-06-17 14:34:17.847 +10:00 WRN] No email address found for order 1417987
[2025-06-17 14:34:17.852 +10:00 INF] Successfully processed order 1417987 (MYOB + PDF)
[2025-06-17 14:34:17.856 +10:00 INF] Order processing completed. Processed: 2, Success: 2, Failed: 0
[2025-06-17 14:34:17.862 +10:00 INF] Processing credit invoices...
[2025-06-17 14:34:17.865 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 14:34:17.872 +10:00 INF] Found 0 credits to invoice
[2025-06-17 14:34:17.876 +10:00 INF] Processing refund invoices...
[2025-06-17 14:34:17.879 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 14:34:17.884 +10:00 INF] Found 16 refunds to invoice
[2025-06-17 14:34:17.891 +10:00 INF] Creating refund invoice for refund 1801, Amount: ¤1.69
[2025-06-17 14:34:18.168 +10:00 INF] Deleting existing invoice S248751801
[2025-06-17 14:34:18.502 +10:00 WRN] Failed to delete existing invoice S248751801 - invoice may be locked/paid in MYOB
MYOB.AccountRight.SDK.ApiValidationException: Encountered a validation error (https://arl2.api.myob.com/accountright/b2f20bad-65c4-4dd3-a921-0a20ccfffb73/Sale/Invoice/Service/ae7fcbd9-f0a0-4771-83a9-3dcdea23f5cb)
 ---> System.Net.WebException: The remote server returned an error: (400) Bad Request.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiDeleteRequestSync(Uri uri, ICompanyFileCredentials credentials)
   at MYOB.AccountRight.SDK.Services.MutableService`1.Delete(CompanyFile cf, Guid uid, ICompanyFileCredentials credentials, ErrorLevel errorLevel)
   at LepInvoicer.Implementations.MYOBService.<>c__DisplayClass33_1.<DeleteExistingInvoice>b__1() in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 875
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Implementations.MYOBService.DeleteExistingInvoice(String invoiceNumber) in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 874
[2025-06-17 14:34:18.516 +10:00 WRN] Skipping refund 1801 - cannot delete existing invoice S248751801
[2025-06-17 14:34:18.522 +10:00 INF] Creating refund invoice for refund 1802, Amount: ¤4.59
[2025-06-17 14:34:38.726 +10:00 INF] Deleting existing invoice S139771802
[2025-06-17 14:34:39.153 +10:00 WRN] Failed to delete existing invoice S139771802 - invoice may be locked/paid in MYOB
MYOB.AccountRight.SDK.ApiValidationException: Encountered a validation error (https://arl2.api.myob.com/accountright/b2f20bad-65c4-4dd3-a921-0a20ccfffb73/Sale/Invoice/Service/61295f40-7707-4574-825e-84da93a4b016)
 ---> System.Net.WebException: The remote server returned an error: (400) Bad Request.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiDeleteRequestSync(Uri uri, ICompanyFileCredentials credentials)
   at MYOB.AccountRight.SDK.Services.MutableService`1.Delete(CompanyFile cf, Guid uid, ICompanyFileCredentials credentials, ErrorLevel errorLevel)
   at LepInvoicer.Implementations.MYOBService.<>c__DisplayClass33_1.<DeleteExistingInvoice>b__1() in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 875
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Implementations.MYOBService.DeleteExistingInvoice(String invoiceNumber) in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 874
[2025-06-17 14:34:39.159 +10:00 WRN] Skipping refund 1802 - cannot delete existing invoice S139771802
[2025-06-17 14:34:39.187 +10:00 INF] Creating refund invoice for refund 1803, Amount: ¤9.27
[2025-06-17 14:34:39.501 +10:00 INF] Deleting existing invoice S150791803
[2025-06-17 14:34:39.921 +10:00 WRN] Failed to delete existing invoice S150791803 - invoice may be locked/paid in MYOB
MYOB.AccountRight.SDK.ApiValidationException: Encountered a validation error (https://arl2.api.myob.com/accountright/b2f20bad-65c4-4dd3-a921-0a20ccfffb73/Sale/Invoice/Service/ef2d3cc0-43a4-4c63-95e9-76389d058325)
 ---> System.Net.WebException: The remote server returned an error: (400) Bad Request.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiDeleteRequestSync(Uri uri, ICompanyFileCredentials credentials)
   at MYOB.AccountRight.SDK.Services.MutableService`1.Delete(CompanyFile cf, Guid uid, ICompanyFileCredentials credentials, ErrorLevel errorLevel)
   at LepInvoicer.Implementations.MYOBService.<>c__DisplayClass33_1.<DeleteExistingInvoice>b__1() in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 875
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Implementations.MYOBService.DeleteExistingInvoice(String invoiceNumber) in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 874
[2025-06-17 14:34:39.930 +10:00 WRN] Skipping refund 1803 - cannot delete existing invoice S150791803
[2025-06-17 14:34:39.946 +10:00 INF] Creating refund invoice for refund 1804, Amount: ¤11.23
[2025-06-17 14:34:40.361 +10:00 INF] Deleting existing invoice S150791804
[2025-06-17 14:34:40.656 +10:00 WRN] Failed to delete existing invoice S150791804 - invoice may be locked/paid in MYOB
MYOB.AccountRight.SDK.ApiValidationException: Encountered a validation error (https://arl2.api.myob.com/accountright/b2f20bad-65c4-4dd3-a921-0a20ccfffb73/Sale/Invoice/Service/903aeac6-182f-4aae-b4c4-d8e81708ec76)
 ---> System.Net.WebException: The remote server returned an error: (400) Bad Request.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiDeleteRequestSync(Uri uri, ICompanyFileCredentials credentials)
   at MYOB.AccountRight.SDK.Services.MutableService`1.Delete(CompanyFile cf, Guid uid, ICompanyFileCredentials credentials, ErrorLevel errorLevel)
   at LepInvoicer.Implementations.MYOBService.<>c__DisplayClass33_1.<DeleteExistingInvoice>b__1() in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 875
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Implementations.MYOBService.DeleteExistingInvoice(String invoiceNumber) in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 874
[2025-06-17 14:34:40.663 +10:00 WRN] Skipping refund 1804 - cannot delete existing invoice S150791804
[2025-06-17 14:34:40.672 +10:00 INF] Creating refund invoice for refund 1805, Amount: ¤32.62
[2025-06-17 14:34:41.171 +10:00 INF] Deleting existing invoice S252801805
[2025-06-17 14:34:41.463 +10:00 WRN] Failed to delete existing invoice S252801805 - invoice may be locked/paid in MYOB
MYOB.AccountRight.SDK.ApiValidationException: Encountered a validation error (https://arl2.api.myob.com/accountright/b2f20bad-65c4-4dd3-a921-0a20ccfffb73/Sale/Invoice/Service/6942f99f-e33c-424d-861a-2215ef9a1e09)
 ---> System.Net.WebException: The remote server returned an error: (400) Bad Request.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiDeleteRequestSync(Uri uri, ICompanyFileCredentials credentials)
   at MYOB.AccountRight.SDK.Services.MutableService`1.Delete(CompanyFile cf, Guid uid, ICompanyFileCredentials credentials, ErrorLevel errorLevel)
   at LepInvoicer.Implementations.MYOBService.<>c__DisplayClass33_1.<DeleteExistingInvoice>b__1() in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 875
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Implementations.MYOBService.DeleteExistingInvoice(String invoiceNumber) in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 874
[2025-06-17 14:34:41.480 +10:00 WRN] Skipping refund 1805 - cannot delete existing invoice S252801805
[2025-06-17 14:34:41.486 +10:00 INF] Creating refund invoice for refund 1806, Amount: ¤36.12
[2025-06-17 14:34:41.896 +10:00 INF] Deleting existing invoice S141881806
[2025-06-17 14:34:42.219 +10:00 WRN] Failed to delete existing invoice S141881806 - invoice may be locked/paid in MYOB
MYOB.AccountRight.SDK.ApiValidationException: Encountered a validation error (https://arl2.api.myob.com/accountright/b2f20bad-65c4-4dd3-a921-0a20ccfffb73/Sale/Invoice/Service/818eaa9f-5e51-4882-ab77-6b4bb0624735)
 ---> System.Net.WebException: The remote server returned an error: (400) Bad Request.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiDeleteRequestSync(Uri uri, ICompanyFileCredentials credentials)
   at MYOB.AccountRight.SDK.Services.MutableService`1.Delete(CompanyFile cf, Guid uid, ICompanyFileCredentials credentials, ErrorLevel errorLevel)
   at LepInvoicer.Implementations.MYOBService.<>c__DisplayClass33_1.<DeleteExistingInvoice>b__1() in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 875
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Implementations.MYOBService.DeleteExistingInvoice(String invoiceNumber) in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 874
[2025-06-17 14:34:42.227 +10:00 WRN] Skipping refund 1806 - cannot delete existing invoice S141881806
[2025-06-17 14:34:42.239 +10:00 INF] Creating refund invoice for refund 1807, Amount: ¤1.77
[2025-06-17 14:34:42.632 +10:00 INF] Deleting existing invoice S152161807
[2025-06-17 14:34:42.907 +10:00 WRN] Failed to delete existing invoice S152161807 - invoice may be locked/paid in MYOB
MYOB.AccountRight.SDK.ApiValidationException: Encountered a validation error (https://arl2.api.myob.com/accountright/b2f20bad-65c4-4dd3-a921-0a20ccfffb73/Sale/Invoice/Service/793efdaa-dcb5-422e-9ef2-69458a598c33)
 ---> System.Net.WebException: The remote server returned an error: (400) Bad Request.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiDeleteRequestSync(Uri uri, ICompanyFileCredentials credentials)
   at MYOB.AccountRight.SDK.Services.MutableService`1.Delete(CompanyFile cf, Guid uid, ICompanyFileCredentials credentials, ErrorLevel errorLevel)
   at LepInvoicer.Implementations.MYOBService.<>c__DisplayClass33_1.<DeleteExistingInvoice>b__1() in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 875
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Implementations.MYOBService.DeleteExistingInvoice(String invoiceNumber) in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 874
[2025-06-17 14:34:42.913 +10:00 WRN] Skipping refund 1807 - cannot delete existing invoice S152161807
[2025-06-17 14:34:42.918 +10:00 INF] Creating refund invoice for refund 1812, Amount: ¤1,312.65
[2025-06-17 14:34:44.061 +10:00 INF] Deleting existing invoice S251951812
[2025-06-17 14:34:44.319 +10:00 WRN] Failed to delete existing invoice S251951812 - invoice may be locked/paid in MYOB
MYOB.AccountRight.SDK.ApiValidationException: Encountered a validation error (https://arl2.api.myob.com/accountright/b2f20bad-65c4-4dd3-a921-0a20ccfffb73/Sale/Invoice/Service/a713338d-93e4-4f31-ab91-4947eb95c2d0)
 ---> System.Net.WebException: The remote server returned an error: (400) Bad Request.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiDeleteRequestSync(Uri uri, ICompanyFileCredentials credentials)
   at MYOB.AccountRight.SDK.Services.MutableService`1.Delete(CompanyFile cf, Guid uid, ICompanyFileCredentials credentials, ErrorLevel errorLevel)
   at LepInvoicer.Implementations.MYOBService.<>c__DisplayClass33_1.<DeleteExistingInvoice>b__1() in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 875
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Implementations.MYOBService.DeleteExistingInvoice(String invoiceNumber) in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 874
[2025-06-17 14:34:44.326 +10:00 WRN] Skipping refund 1812 - cannot delete existing invoice S251951812
[2025-06-17 14:34:44.332 +10:00 INF] Creating refund invoice for refund 1814, Amount: ¤1.37
[2025-06-17 14:34:44.657 +10:00 INF] Deleting existing invoice S137511814
[2025-06-17 14:34:44.976 +10:00 WRN] Failed to delete existing invoice S137511814 - invoice may be locked/paid in MYOB
MYOB.AccountRight.SDK.ApiValidationException: Encountered a validation error (https://arl2.api.myob.com/accountright/b2f20bad-65c4-4dd3-a921-0a20ccfffb73/Sale/Invoice/Service/ae7cf4da-1e73-4681-98b6-92685935c4b0)
 ---> System.Net.WebException: The remote server returned an error: (400) Bad Request.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiDeleteRequestSync(Uri uri, ICompanyFileCredentials credentials)
   at MYOB.AccountRight.SDK.Services.MutableService`1.Delete(CompanyFile cf, Guid uid, ICompanyFileCredentials credentials, ErrorLevel errorLevel)
   at LepInvoicer.Implementations.MYOBService.<>c__DisplayClass33_1.<DeleteExistingInvoice>b__1() in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 875
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Implementations.MYOBService.DeleteExistingInvoice(String invoiceNumber) in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 874
[2025-06-17 14:34:45.013 +10:00 WRN] Skipping refund 1814 - cannot delete existing invoice S137511814
[2025-06-17 14:34:45.026 +10:00 INF] Creating refund invoice for refund 1815, Amount: ¤3.83
[2025-06-17 14:34:45.328 +10:00 INF] Deleting existing invoice S138991815
[2025-06-17 14:34:45.599 +10:00 WRN] Failed to delete existing invoice S138991815 - invoice may be locked/paid in MYOB
MYOB.AccountRight.SDK.ApiValidationException: Encountered a validation error (https://arl2.api.myob.com/accountright/b2f20bad-65c4-4dd3-a921-0a20ccfffb73/Sale/Invoice/Service/0aaeec22-6d8e-4d71-87bd-33f814e5f06f)
 ---> System.Net.WebException: The remote server returned an error: (400) Bad Request.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiDeleteRequestSync(Uri uri, ICompanyFileCredentials credentials)
   at MYOB.AccountRight.SDK.Services.MutableService`1.Delete(CompanyFile cf, Guid uid, ICompanyFileCredentials credentials, ErrorLevel errorLevel)
   at LepInvoicer.Implementations.MYOBService.<>c__DisplayClass33_1.<DeleteExistingInvoice>b__1() in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 875
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Implementations.MYOBService.DeleteExistingInvoice(String invoiceNumber) in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 874
[2025-06-17 14:34:45.606 +10:00 WRN] Skipping refund 1815 - cannot delete existing invoice S138991815
[2025-06-17 14:34:45.636 +10:00 INF] Creating refund invoice for refund 1816, Amount: ¤6.80
[2025-06-17 14:34:46.072 +10:00 INF] Deleting existing invoice S143701816
[2025-06-17 14:34:46.398 +10:00 WRN] Failed to delete existing invoice S143701816 - invoice may be locked/paid in MYOB
MYOB.AccountRight.SDK.ApiValidationException: Encountered a validation error (https://arl2.api.myob.com/accountright/b2f20bad-65c4-4dd3-a921-0a20ccfffb73/Sale/Invoice/Service/5ac7ff46-5d70-47ca-a353-44c57a10c35c)
 ---> System.Net.WebException: The remote server returned an error: (400) Bad Request.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiDeleteRequestSync(Uri uri, ICompanyFileCredentials credentials)
   at MYOB.AccountRight.SDK.Services.MutableService`1.Delete(CompanyFile cf, Guid uid, ICompanyFileCredentials credentials, ErrorLevel errorLevel)
   at LepInvoicer.Implementations.MYOBService.<>c__DisplayClass33_1.<DeleteExistingInvoice>b__1() in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 875
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Implementations.MYOBService.DeleteExistingInvoice(String invoiceNumber) in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 874
[2025-06-17 14:34:46.404 +10:00 WRN] Skipping refund 1816 - cannot delete existing invoice S143701816
[2025-06-17 14:34:46.410 +10:00 INF] Creating refund invoice for refund 1817, Amount: ¤19.84
[2025-06-17 14:34:46.719 +10:00 INF] Deleting existing invoice S143701817
[2025-06-17 14:34:47.162 +10:00 WRN] Failed to delete existing invoice S143701817 - invoice may be locked/paid in MYOB
MYOB.AccountRight.SDK.ApiValidationException: Encountered a validation error (https://arl2.api.myob.com/accountright/b2f20bad-65c4-4dd3-a921-0a20ccfffb73/Sale/Invoice/Service/2e674898-d09f-451c-921f-b6054b956d02)
 ---> System.Net.WebException: The remote server returned an error: (400) Bad Request.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiDeleteRequestSync(Uri uri, ICompanyFileCredentials credentials)
   at MYOB.AccountRight.SDK.Services.MutableService`1.Delete(CompanyFile cf, Guid uid, ICompanyFileCredentials credentials, ErrorLevel errorLevel)
   at LepInvoicer.Implementations.MYOBService.<>c__DisplayClass33_1.<DeleteExistingInvoice>b__1() in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 875
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Implementations.MYOBService.DeleteExistingInvoice(String invoiceNumber) in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 874
[2025-06-17 14:34:47.176 +10:00 WRN] Skipping refund 1817 - cannot delete existing invoice S143701817
[2025-06-17 14:34:47.208 +10:00 INF] Creating refund invoice for refund 1818, Amount: ¤61.92
[2025-06-17 14:34:47.621 +10:00 INF] Deleting existing invoice S1531791818
[2025-06-17 14:34:47.951 +10:00 WRN] Failed to delete existing invoice S1531791818 - invoice may be locked/paid in MYOB
MYOB.AccountRight.SDK.ApiValidationException: Encountered a validation error (https://arl2.api.myob.com/accountright/b2f20bad-65c4-4dd3-a921-0a20ccfffb73/Sale/Invoice/Service/f2d3d1a7-f2e9-4aee-b0fe-cf5f0c77f7bc)
 ---> System.Net.WebException: The remote server returned an error: (400) Bad Request.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiDeleteRequestSync(Uri uri, ICompanyFileCredentials credentials)
   at MYOB.AccountRight.SDK.Services.MutableService`1.Delete(CompanyFile cf, Guid uid, ICompanyFileCredentials credentials, ErrorLevel errorLevel)
   at LepInvoicer.Implementations.MYOBService.<>c__DisplayClass33_1.<DeleteExistingInvoice>b__1() in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 875
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Implementations.MYOBService.DeleteExistingInvoice(String invoiceNumber) in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 874
[2025-06-17 14:34:47.958 +10:00 WRN] Skipping refund 1818 - cannot delete existing invoice S1531791818
[2025-06-17 14:34:47.988 +10:00 INF] Creating refund invoice for refund 1824, Amount: ¤70.04
[2025-06-17 14:34:48.279 +10:00 INF] Deleting existing invoice S189871824
[2025-06-17 14:34:48.654 +10:00 WRN] Failed to delete existing invoice S189871824 - invoice may be locked/paid in MYOB
MYOB.AccountRight.SDK.ApiValidationException: Encountered a validation error (https://arl2.api.myob.com/accountright/b2f20bad-65c4-4dd3-a921-0a20ccfffb73/Sale/Invoice/Service/3366c74b-aec7-4ee3-87d0-4ed666a81e70)
 ---> System.Net.WebException: The remote server returned an error: (400) Bad Request.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiDeleteRequestSync(Uri uri, ICompanyFileCredentials credentials)
   at MYOB.AccountRight.SDK.Services.MutableService`1.Delete(CompanyFile cf, Guid uid, ICompanyFileCredentials credentials, ErrorLevel errorLevel)
   at LepInvoicer.Implementations.MYOBService.<>c__DisplayClass33_1.<DeleteExistingInvoice>b__1() in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 875
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Implementations.MYOBService.DeleteExistingInvoice(String invoiceNumber) in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 874
[2025-06-17 14:34:48.660 +10:00 WRN] Skipping refund 1824 - cannot delete existing invoice S189871824
[2025-06-17 14:34:48.667 +10:00 INF] Creating refund invoice for refund 1836, Amount: ¤32.32
[2025-06-17 14:34:49.099 +10:00 INF] Deleting existing invoice S252801836
[2025-06-17 14:34:49.658 +10:00 WRN] Failed to delete existing invoice S252801836 - invoice may be locked/paid in MYOB
MYOB.AccountRight.SDK.ApiValidationException: Encountered a validation error (https://arl2.api.myob.com/accountright/b2f20bad-65c4-4dd3-a921-0a20ccfffb73/Sale/Invoice/Service/4b9f90a9-fe2b-4248-bce3-1aa759007fcd)
 ---> System.Net.WebException: The remote server returned an error: (400) Bad Request.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiDeleteRequestSync(Uri uri, ICompanyFileCredentials credentials)
   at MYOB.AccountRight.SDK.Services.MutableService`1.Delete(CompanyFile cf, Guid uid, ICompanyFileCredentials credentials, ErrorLevel errorLevel)
   at LepInvoicer.Implementations.MYOBService.<>c__DisplayClass33_1.<DeleteExistingInvoice>b__1() in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 875
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Implementations.MYOBService.DeleteExistingInvoice(String invoiceNumber) in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 874
[2025-06-17 14:34:49.664 +10:00 WRN] Skipping refund 1836 - cannot delete existing invoice S252801836
[2025-06-17 14:34:49.693 +10:00 INF] Creating refund invoice for refund 1837, Amount: ¤26.00
[2025-06-17 14:34:50.442 +10:00 INF] Deleting existing invoice S191871837
[2025-06-17 14:34:50.748 +10:00 WRN] Failed to delete existing invoice S191871837 - invoice may be locked/paid in MYOB
MYOB.AccountRight.SDK.ApiValidationException: Encountered a validation error (https://arl2.api.myob.com/accountright/b2f20bad-65c4-4dd3-a921-0a20ccfffb73/Sale/Invoice/Service/27c54bcf-d87b-45d8-a5e4-997d4b831e2e)
 ---> System.Net.WebException: The remote server returned an error: (400) Bad Request.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiDeleteRequestSync(Uri uri, ICompanyFileCredentials credentials)
   at MYOB.AccountRight.SDK.Services.MutableService`1.Delete(CompanyFile cf, Guid uid, ICompanyFileCredentials credentials, ErrorLevel errorLevel)
   at LepInvoicer.Implementations.MYOBService.<>c__DisplayClass33_1.<DeleteExistingInvoice>b__1() in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 875
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Implementations.MYOBService.DeleteExistingInvoice(String invoiceNumber) in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 874
[2025-06-17 14:34:50.764 +10:00 WRN] Skipping refund 1837 - cannot delete existing invoice S191871837
[2025-06-17 14:34:50.771 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 14:34:50.856 +10:00 INF] LEP Invoicer completed successfully in 40530ms. Orders: 2, Credits: 0, Refunds: 16
[2025-06-17 14:34:50.867 +10:00 INF] Database connection disposed
[2025-06-17 14:34:50.871 +10:00 INF] LEP Invoicer completed with result: 0
