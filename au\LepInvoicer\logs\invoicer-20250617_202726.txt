[2025-06-17 20:27:26.665 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 20:27:26.707 +10:00 INF] Initializing FastReport...
[2025-06-17 20:27:26.833 +10:00 INF] FastReport initialized successfully
[2025-06-17 20:27:27.317 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 20:27:28.599 +10:00 INF] Starting LEP Invoicer at "2025-06-17T20:27:28.5994300+10:00"
[2025-06-17 20:27:28.603 +10:00 INF] Initializing database service...
[2025-06-17 20:27:28.606 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 20:27:28.702 +10:00 INF] Database connection established successfully
[2025-06-17 20:27:28.704 +10:00 INF] Database service initialized successfully
[2025-06-17 20:27:28.707 +10:00 INF] Checking for pending work...
[2025-06-17 20:27:28.710 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 20:27:29.554 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 20:27:29.558 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 20:27:29.577 +10:00 INF] Found 0 credits to invoice
[2025-06-17 20:27:29.580 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 20:27:29.583 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 20:27:29.586 +10:00 INF] No pending work found
[2025-06-17 20:27:29.587 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 20:27:29.589 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 20:27:29.683 +10:00 INF] LEP Invoicer completed successfully in 1083ms. No work to process.
[2025-06-17 20:27:29.689 +10:00 INF] Database connection disposed
[2025-06-17 20:27:29.691 +10:00 INF] LEP Invoicer completed with result: 0
