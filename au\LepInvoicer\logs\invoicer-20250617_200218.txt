[2025-06-17 20:02:18.729 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 20:02:18.756 +10:00 INF] Initializing FastReport...
[2025-06-17 20:02:18.825 +10:00 INF] FastReport initialized successfully
[2025-06-17 20:02:19.277 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 20:02:20.537 +10:00 INF] Starting LEP Invoicer at "2025-06-17T20:02:20.5367552+10:00"
[2025-06-17 20:02:20.540 +10:00 INF] Initializing database service...
[2025-06-17 20:02:20.544 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 20:02:20.651 +10:00 INF] Database connection established successfully
[2025-06-17 20:02:20.653 +10:00 INF] Database service initialized successfully
[2025-06-17 20:02:20.655 +10:00 INF] Checking for pending work...
[2025-06-17 20:02:20.658 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 20:02:21.538 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 20:02:21.541 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 20:02:21.557 +10:00 INF] Found 0 credits to invoice
[2025-06-17 20:02:21.559 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 20:02:21.562 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 20:02:21.563 +10:00 INF] No pending work found
[2025-06-17 20:02:21.565 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 20:02:21.568 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 20:02:21.641 +10:00 INF] LEP Invoicer completed successfully in 1104ms. No work to process.
[2025-06-17 20:02:21.647 +10:00 INF] Database connection disposed
[2025-06-17 20:02:21.648 +10:00 INF] LEP Invoicer completed with result: 0
