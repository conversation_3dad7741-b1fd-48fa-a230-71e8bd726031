[2025-06-18 09:01:39.557 +10:00 INF] Starting LEP Invoicer application
[2025-06-18 09:01:39.595 +10:00 INF] Initializing FastReport...
[2025-06-18 09:01:39.680 +10:00 INF] FastReport initialized successfully
[2025-06-18 09:01:40.107 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-18 09:01:41.345 +10:00 INF] Starting LEP Invoicer at "2025-06-18T09:01:41.3448887+10:00"
[2025-06-18 09:01:41.348 +10:00 INF] Initializing database service...
[2025-06-18 09:01:41.351 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-18 09:01:41.461 +10:00 INF] Database connection established successfully
[2025-06-18 09:01:41.463 +10:00 INF] Database service initialized successfully
[2025-06-18 09:01:41.465 +10:00 INF] Checking for pending work...
[2025-06-18 09:01:41.469 +10:00 INF] Getting 500 orders to invoice
[2025-06-18 09:01:42.364 +10:00 INF] Found 0 orders to invoice (filtered 16 candidates)
[2025-06-18 09:01:42.373 +10:00 INF] Getting 100 credits to invoice
[2025-06-18 09:01:42.389 +10:00 INF] Found 0 credits to invoice
[2025-06-18 09:01:42.391 +10:00 INF] Getting 100 refunds to invoice
[2025-06-18 09:01:42.394 +10:00 INF] Found 0 refunds to invoice
[2025-06-18 09:01:42.396 +10:00 INF] No pending work found
[2025-06-18 09:01:42.397 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-18 09:01:42.401 +10:00 INF] Cleaning up invoicer logs...
[2025-06-18 09:01:42.482 +10:00 INF] LEP Invoicer completed successfully in 1137ms. No work to process.
[2025-06-18 09:01:42.488 +10:00 INF] Database connection disposed
[2025-06-18 09:01:42.490 +10:00 INF] LEP Invoicer completed with result: 0
