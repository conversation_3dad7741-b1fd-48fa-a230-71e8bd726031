using lumen.csv;
using System;
using System.Collections.Generic;

namespace lep.user.impl
{
	public class CustomerCsvReader : CsvParser
    {
        protected List<string> customerList = new List<string>();

        public CustomerCsvReader()
        {
            HandleQuotes = true;
        }

        public List<string> CustomerList
        {
            set { customerList = value; }
            get { return customerList; }
        }

        public override void RowData(string[] values)
        {
            if (values.Length > 0)
            {
                if (values[0].Trim().Length <= 40 && values[0].Trim().Length > 0)
                {
                    customerList.Add(values[0].Trim());
                }
                else
                {
                    throw new Exception("invalid file");
                }
            }
        }
    }
}