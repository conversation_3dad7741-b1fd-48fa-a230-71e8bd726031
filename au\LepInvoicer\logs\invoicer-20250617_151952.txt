[2025-06-17 15:19:53.008 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 15:19:53.035 +10:00 INF] Initializing FastReport...
[2025-06-17 15:19:53.118 +10:00 INF] FastReport initialized successfully
[2025-06-17 15:19:53.677 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 15:19:55.196 +10:00 INF] Starting LEP Invoicer at "2025-06-17T15:19:55.1960107+10:00"
[2025-06-17 15:19:55.203 +10:00 INF] Initializing database service...
[2025-06-17 15:19:55.206 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 15:19:55.346 +10:00 INF] Database connection established successfully
[2025-06-17 15:19:55.379 +10:00 INF] Database service initialized successfully
[2025-06-17 15:19:55.382 +10:00 INF] Checking for pending work...
[2025-06-17 15:19:55.386 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 15:19:56.706 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 15:19:56.709 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 15:19:56.714 +10:00 ERR] Failed to get credits to invoice
NHibernate.QueryParameterException: could not locate named parameter [batchSize]
   at NHibernate.Engine.Query.ParameterMetadata.GetNamedParameterDescriptor(String name)
   at NHibernate.Engine.Query.ParameterMetadata.GetNamedParameterExpectedType(String name)
   at NHibernate.Impl.AbstractQueryImpl.SetParameter[T](String name, T val)
   at LepInvoicer.Implementations.DatabaseService.GetCreditsToInvoice(Int32 batchSize) in C:\LepSF\au\LepInvoicer\Implementations\DatabaseService.cs:line 110
[2025-06-17 15:19:56.763 +10:00 ERR] LEP Invoicer failed after 1567ms
NHibernate.QueryParameterException: could not locate named parameter [batchSize]
   at NHibernate.Engine.Query.ParameterMetadata.GetNamedParameterDescriptor(String name)
   at NHibernate.Engine.Query.ParameterMetadata.GetNamedParameterExpectedType(String name)
   at NHibernate.Impl.AbstractQueryImpl.SetParameter[T](String name, T val)
   at LepInvoicer.Implementations.DatabaseService.GetCreditsToInvoice(Int32 batchSize) in C:\LepSF\au\LepInvoicer\Implementations\DatabaseService.cs:line 110
   at LepInvoicer.Implementations.InvoicerService.CheckForPendingWork() in C:\LepSF\au\LepInvoicer\Implementations\InvoicerService.cs:line 127
   at LepInvoicer.Implementations.InvoicerService.RunInvoicer() in C:\LepSF\au\LepInvoicer\Implementations\InvoicerService.cs:line 55
[2025-06-17 15:19:56.773 +10:00 INF] Database connection disposed
[2025-06-17 15:19:56.776 +10:00 INF] LEP Invoicer completed with result: 1
