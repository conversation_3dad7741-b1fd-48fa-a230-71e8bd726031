[2025-06-17 14:12:16.682 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 14:12:16.712 +10:00 INF] Initializing FastReport...
[2025-06-17 14:12:16.793 +10:00 INF] FastReport initialized successfully
[2025-06-17 14:12:17.233 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 14:12:18.537 +10:00 INF] Starting LEP Invoicer at "2025-06-17T14:12:18.5368316+10:00"
[2025-06-17 14:12:18.540 +10:00 INF] Initializing database service...
[2025-06-17 14:12:18.543 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 14:12:18.646 +10:00 INF] Database connection established successfully
[2025-06-17 14:12:18.648 +10:00 INF] Database service initialized successfully
[2025-06-17 14:12:18.650 +10:00 INF] Checking for pending work...
[2025-06-17 14:12:18.653 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 14:12:19.635 +10:00 INF] Found 2 orders to invoice (filtered 17 candidates)
[2025-06-17 14:12:19.639 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 14:12:19.666 +10:00 INF] Found 0 credits to invoice
[2025-06-17 14:12:19.668 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 14:12:19.707 +10:00 INF] Found 16 refunds to invoice
[2025-06-17 14:12:19.709 +10:00 INF] Found pending work: 2 orders, 16 refunds
[2025-06-17 14:12:19.711 +10:00 INF] Initializing MYOB and other services...
[2025-06-17 14:12:19.714 +10:00 INF] Initializing MYOB service...
[2025-06-17 14:12:19.716 +10:00 INF] Initializing MYOB with OAuth authentication
[2025-06-17 14:12:19.721 +10:00 INF] Starting OAuth authentication flow
[2025-06-17 14:12:19.727 +10:00 INF] Using existing OAuth tokens
[2025-06-17 14:12:19.728 +10:00 INF] Initializing MYOB services with OAuth keystore
[2025-06-17 14:12:19.732 +10:00 INF] MYOB services initialized successfully
[2025-06-17 14:12:19.733 +10:00 INF] OAuth keystore set for API calls
[2025-06-17 14:12:19.734 +10:00 INF] Getting company files from MYOB
[2025-06-17 14:12:20.027 +10:00 INF] MYOB service initialized with OAuth credentials. Using company file: LEP Colour Printers Pty Ltd
[2025-06-17 14:12:20.751 +10:00 INF] MYOB service initialized successfully with company file: LEP Colour Printers Pty Ltd
[2025-06-17 14:12:20.757 +10:00 INF] All services initialized successfully
[2025-06-17 14:12:20.760 +10:00 INF] Processing order invoices...
[2025-06-17 14:12:20.761 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 14:12:21.104 +10:00 INF] Found 2 orders to invoice (filtered 17 candidates)
[2025-06-17 14:12:21.106 +10:00 INF] Found 2 orders to process
[2025-06-17 14:12:21.108 +10:00 INF] Getting order 1418015
[2025-06-17 14:12:21.132 +10:00 INF] Processing order 1418015 with 4 jobs, total: ¤213.09
[2025-06-17 14:12:21.138 +10:00 INF] Creating order invoice for order 1418015
[2025-06-17 14:12:22.612 +10:00 INF] Successfully created MYOB invoice O1418015 for order 1418015
[2025-06-17 14:12:22.614 +10:00 INF] Successfully created MYOB invoice for order 1418015
[2025-06-17 14:12:22.619 +10:00 INF] Generating PDF invoice for order 1418015 at \\dfs01\resource\invoices\2025/Jun/17\O1418015.pdf
[2025-06-17 14:12:24.547 +10:00 INF] Successfully generated PDF for order 1418015
[2025-06-17 14:12:24.574 +10:00 WRN] No email address found for order 1418015
[2025-06-17 14:12:24.580 +10:00 INF] Successfully processed order 1418015 (MYOB + PDF)
[2025-06-17 14:12:24.583 +10:00 INF] Getting order 1416151
[2025-06-17 14:12:24.588 +10:00 INF] Processing order 1416151 with 2 jobs, total: ¤915.46
[2025-06-17 14:12:24.590 +10:00 INF] Creating order invoice for order 1416151
[2025-06-17 14:12:25.988 +10:00 INF] Successfully created MYOB invoice O1416151 for order 1416151
[2025-06-17 14:12:25.990 +10:00 INF] Successfully created MYOB invoice for order 1416151
[2025-06-17 14:12:25.991 +10:00 INF] Generating PDF invoice for order 1416151 at \\dfs01\resource\invoices\2025/Jun/17\O1416151.pdf
[2025-06-17 14:12:26.362 +10:00 INF] Successfully generated PDF for order 1416151
[2025-06-17 14:12:26.388 +10:00 WRN] No email address found for order 1416151
[2025-06-17 14:12:26.392 +10:00 INF] Successfully processed order 1416151 (MYOB + PDF)
[2025-06-17 14:12:26.400 +10:00 INF] Order processing completed. Processed: 2, Success: 2, Failed: 0
[2025-06-17 14:12:26.409 +10:00 INF] Processing credit invoices...
[2025-06-17 14:12:26.410 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 14:12:26.416 +10:00 INF] Found 0 credits to invoice
[2025-06-17 14:12:26.422 +10:00 INF] Processing refund invoices...
[2025-06-17 14:12:26.423 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 14:12:26.428 +10:00 INF] Found 16 refunds to invoice
[2025-06-17 14:12:26.432 +10:00 INF] Creating refund invoice for refund 1801, Amount: ¤1.69
[2025-06-17 14:12:26.764 +10:00 INF] Deleting existing invoice S248751801
[2025-06-17 14:12:27.157 +10:00 WRN] Failed to delete existing invoice S248751801 - invoice may be locked/paid in MYOB
MYOB.AccountRight.SDK.ApiValidationException: Encountered a validation error (https://arl2.api.myob.com/accountright/b2f20bad-65c4-4dd3-a921-0a20ccfffb73/Sale/Invoice/Service/ae7fcbd9-f0a0-4771-83a9-3dcdea23f5cb)
 ---> System.Net.WebException: The remote server returned an error: (400) Bad Request.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiDeleteRequestSync(Uri uri, ICompanyFileCredentials credentials)
   at MYOB.AccountRight.SDK.Services.MutableService`1.Delete(CompanyFile cf, Guid uid, ICompanyFileCredentials credentials, ErrorLevel errorLevel)
   at LepInvoicer.Implementations.MYOBService.<>c__DisplayClass33_1.<DeleteExistingInvoice>b__1() in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 875
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Implementations.MYOBService.DeleteExistingInvoice(String invoiceNumber) in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 874
[2025-06-17 14:12:27.172 +10:00 WRN] Skipping refund 1801 - cannot delete existing invoice S248751801
[2025-06-17 14:12:27.177 +10:00 INF] Creating refund invoice for refund 1802, Amount: ¤4.59
[2025-06-17 14:12:27.573 +10:00 INF] Deleting existing invoice S139771802
[2025-06-17 14:12:27.915 +10:00 WRN] Failed to delete existing invoice S139771802 - invoice may be locked/paid in MYOB
MYOB.AccountRight.SDK.ApiValidationException: Encountered a validation error (https://arl2.api.myob.com/accountright/b2f20bad-65c4-4dd3-a921-0a20ccfffb73/Sale/Invoice/Service/61295f40-7707-4574-825e-84da93a4b016)
 ---> System.Net.WebException: The remote server returned an error: (400) Bad Request.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiDeleteRequestSync(Uri uri, ICompanyFileCredentials credentials)
   at MYOB.AccountRight.SDK.Services.MutableService`1.Delete(CompanyFile cf, Guid uid, ICompanyFileCredentials credentials, ErrorLevel errorLevel)
   at LepInvoicer.Implementations.MYOBService.<>c__DisplayClass33_1.<DeleteExistingInvoice>b__1() in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 875
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Implementations.MYOBService.DeleteExistingInvoice(String invoiceNumber) in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 874
[2025-06-17 14:12:27.928 +10:00 WRN] Skipping refund 1802 - cannot delete existing invoice S139771802
[2025-06-17 14:12:27.935 +10:00 INF] Creating refund invoice for refund 1803, Amount: ¤9.27
[2025-06-17 14:12:28.300 +10:00 INF] Deleting existing invoice S150791803
[2025-06-17 14:12:28.612 +10:00 WRN] Failed to delete existing invoice S150791803 - invoice may be locked/paid in MYOB
MYOB.AccountRight.SDK.ApiValidationException: Encountered a validation error (https://arl2.api.myob.com/accountright/b2f20bad-65c4-4dd3-a921-0a20ccfffb73/Sale/Invoice/Service/ef2d3cc0-43a4-4c63-95e9-76389d058325)
 ---> System.Net.WebException: The remote server returned an error: (400) Bad Request.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiDeleteRequestSync(Uri uri, ICompanyFileCredentials credentials)
   at MYOB.AccountRight.SDK.Services.MutableService`1.Delete(CompanyFile cf, Guid uid, ICompanyFileCredentials credentials, ErrorLevel errorLevel)
   at LepInvoicer.Implementations.MYOBService.<>c__DisplayClass33_1.<DeleteExistingInvoice>b__1() in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 875
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Implementations.MYOBService.DeleteExistingInvoice(String invoiceNumber) in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 874
[2025-06-17 14:12:28.618 +10:00 WRN] Skipping refund 1803 - cannot delete existing invoice S150791803
[2025-06-17 14:12:28.655 +10:00 INF] Creating refund invoice for refund 1804, Amount: ¤11.23
[2025-06-17 14:12:29.119 +10:00 INF] Deleting existing invoice S150791804
[2025-06-17 14:12:29.439 +10:00 WRN] Failed to delete existing invoice S150791804 - invoice may be locked/paid in MYOB
MYOB.AccountRight.SDK.ApiValidationException: Encountered a validation error (https://arl2.api.myob.com/accountright/b2f20bad-65c4-4dd3-a921-0a20ccfffb73/Sale/Invoice/Service/903aeac6-182f-4aae-b4c4-d8e81708ec76)
 ---> System.Net.WebException: The remote server returned an error: (400) Bad Request.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiDeleteRequestSync(Uri uri, ICompanyFileCredentials credentials)
   at MYOB.AccountRight.SDK.Services.MutableService`1.Delete(CompanyFile cf, Guid uid, ICompanyFileCredentials credentials, ErrorLevel errorLevel)
   at LepInvoicer.Implementations.MYOBService.<>c__DisplayClass33_1.<DeleteExistingInvoice>b__1() in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 875
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Implementations.MYOBService.DeleteExistingInvoice(String invoiceNumber) in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 874
[2025-06-17 14:12:29.453 +10:00 WRN] Skipping refund 1804 - cannot delete existing invoice S150791804
[2025-06-17 14:12:29.489 +10:00 INF] Creating refund invoice for refund 1805, Amount: ¤32.62
[2025-06-17 14:12:32.206 +10:00 INF] Deleting existing invoice S252801805
[2025-06-17 14:12:32.510 +10:00 WRN] Failed to delete existing invoice S252801805 - invoice may be locked/paid in MYOB
MYOB.AccountRight.SDK.ApiValidationException: Encountered a validation error (https://arl2.api.myob.com/accountright/b2f20bad-65c4-4dd3-a921-0a20ccfffb73/Sale/Invoice/Service/6942f99f-e33c-424d-861a-2215ef9a1e09)
 ---> System.Net.WebException: The remote server returned an error: (400) Bad Request.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiDeleteRequestSync(Uri uri, ICompanyFileCredentials credentials)
   at MYOB.AccountRight.SDK.Services.MutableService`1.Delete(CompanyFile cf, Guid uid, ICompanyFileCredentials credentials, ErrorLevel errorLevel)
   at LepInvoicer.Implementations.MYOBService.<>c__DisplayClass33_1.<DeleteExistingInvoice>b__1() in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 875
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Implementations.MYOBService.DeleteExistingInvoice(String invoiceNumber) in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 874
[2025-06-17 14:12:32.516 +10:00 WRN] Skipping refund 1805 - cannot delete existing invoice S252801805
[2025-06-17 14:12:32.521 +10:00 INF] Creating refund invoice for refund 1806, Amount: ¤36.12
[2025-06-17 14:12:32.791 +10:00 INF] Deleting existing invoice S141881806
[2025-06-17 14:12:33.108 +10:00 WRN] Failed to delete existing invoice S141881806 - invoice may be locked/paid in MYOB
MYOB.AccountRight.SDK.ApiValidationException: Encountered a validation error (https://arl2.api.myob.com/accountright/b2f20bad-65c4-4dd3-a921-0a20ccfffb73/Sale/Invoice/Service/818eaa9f-5e51-4882-ab77-6b4bb0624735)
 ---> System.Net.WebException: The remote server returned an error: (400) Bad Request.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiDeleteRequestSync(Uri uri, ICompanyFileCredentials credentials)
   at MYOB.AccountRight.SDK.Services.MutableService`1.Delete(CompanyFile cf, Guid uid, ICompanyFileCredentials credentials, ErrorLevel errorLevel)
   at LepInvoicer.Implementations.MYOBService.<>c__DisplayClass33_1.<DeleteExistingInvoice>b__1() in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 875
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Implementations.MYOBService.DeleteExistingInvoice(String invoiceNumber) in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 874
[2025-06-17 14:12:33.115 +10:00 WRN] Skipping refund 1806 - cannot delete existing invoice S141881806
[2025-06-17 14:12:33.124 +10:00 INF] Creating refund invoice for refund 1807, Amount: ¤1.77
[2025-06-17 14:12:33.435 +10:00 INF] Deleting existing invoice S152161807
[2025-06-17 14:12:33.809 +10:00 WRN] Failed to delete existing invoice S152161807 - invoice may be locked/paid in MYOB
MYOB.AccountRight.SDK.ApiValidationException: Encountered a validation error (https://arl2.api.myob.com/accountright/b2f20bad-65c4-4dd3-a921-0a20ccfffb73/Sale/Invoice/Service/793efdaa-dcb5-422e-9ef2-69458a598c33)
 ---> System.Net.WebException: The remote server returned an error: (400) Bad Request.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiDeleteRequestSync(Uri uri, ICompanyFileCredentials credentials)
   at MYOB.AccountRight.SDK.Services.MutableService`1.Delete(CompanyFile cf, Guid uid, ICompanyFileCredentials credentials, ErrorLevel errorLevel)
   at LepInvoicer.Implementations.MYOBService.<>c__DisplayClass33_1.<DeleteExistingInvoice>b__1() in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 875
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Implementations.MYOBService.DeleteExistingInvoice(String invoiceNumber) in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 874
[2025-06-17 14:12:33.815 +10:00 WRN] Skipping refund 1807 - cannot delete existing invoice S152161807
[2025-06-17 14:12:33.821 +10:00 INF] Creating refund invoice for refund 1812, Amount: ¤1,312.65
[2025-06-17 14:12:34.177 +10:00 INF] Deleting existing invoice S251951812
[2025-06-17 14:12:34.567 +10:00 WRN] Failed to delete existing invoice S251951812 - invoice may be locked/paid in MYOB
MYOB.AccountRight.SDK.ApiValidationException: Encountered a validation error (https://arl2.api.myob.com/accountright/b2f20bad-65c4-4dd3-a921-0a20ccfffb73/Sale/Invoice/Service/a713338d-93e4-4f31-ab91-4947eb95c2d0)
 ---> System.Net.WebException: The remote server returned an error: (400) Bad Request.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiDeleteRequestSync(Uri uri, ICompanyFileCredentials credentials)
   at MYOB.AccountRight.SDK.Services.MutableService`1.Delete(CompanyFile cf, Guid uid, ICompanyFileCredentials credentials, ErrorLevel errorLevel)
   at LepInvoicer.Implementations.MYOBService.<>c__DisplayClass33_1.<DeleteExistingInvoice>b__1() in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 875
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Implementations.MYOBService.DeleteExistingInvoice(String invoiceNumber) in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 874
[2025-06-17 14:12:34.573 +10:00 WRN] Skipping refund 1812 - cannot delete existing invoice S251951812
[2025-06-17 14:12:34.601 +10:00 INF] Creating refund invoice for refund 1814, Amount: ¤1.37
[2025-06-17 14:12:34.885 +10:00 INF] Deleting existing invoice S137511814
[2025-06-17 14:12:35.229 +10:00 WRN] Failed to delete existing invoice S137511814 - invoice may be locked/paid in MYOB
MYOB.AccountRight.SDK.ApiValidationException: Encountered a validation error (https://arl2.api.myob.com/accountright/b2f20bad-65c4-4dd3-a921-0a20ccfffb73/Sale/Invoice/Service/ae7cf4da-1e73-4681-98b6-92685935c4b0)
 ---> System.Net.WebException: The remote server returned an error: (400) Bad Request.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiDeleteRequestSync(Uri uri, ICompanyFileCredentials credentials)
   at MYOB.AccountRight.SDK.Services.MutableService`1.Delete(CompanyFile cf, Guid uid, ICompanyFileCredentials credentials, ErrorLevel errorLevel)
   at LepInvoicer.Implementations.MYOBService.<>c__DisplayClass33_1.<DeleteExistingInvoice>b__1() in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 875
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Implementations.MYOBService.DeleteExistingInvoice(String invoiceNumber) in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 874
[2025-06-17 14:12:35.234 +10:00 WRN] Skipping refund 1814 - cannot delete existing invoice S137511814
[2025-06-17 14:12:35.261 +10:00 INF] Creating refund invoice for refund 1815, Amount: ¤3.83
[2025-06-17 14:12:36.173 +10:00 INF] Deleting existing invoice S138991815
[2025-06-17 14:12:36.511 +10:00 WRN] Failed to delete existing invoice S138991815 - invoice may be locked/paid in MYOB
MYOB.AccountRight.SDK.ApiValidationException: Encountered a validation error (https://arl2.api.myob.com/accountright/b2f20bad-65c4-4dd3-a921-0a20ccfffb73/Sale/Invoice/Service/0aaeec22-6d8e-4d71-87bd-33f814e5f06f)
 ---> System.Net.WebException: The remote server returned an error: (400) Bad Request.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiDeleteRequestSync(Uri uri, ICompanyFileCredentials credentials)
   at MYOB.AccountRight.SDK.Services.MutableService`1.Delete(CompanyFile cf, Guid uid, ICompanyFileCredentials credentials, ErrorLevel errorLevel)
   at LepInvoicer.Implementations.MYOBService.<>c__DisplayClass33_1.<DeleteExistingInvoice>b__1() in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 875
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Implementations.MYOBService.DeleteExistingInvoice(String invoiceNumber) in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 874
[2025-06-17 14:12:36.518 +10:00 WRN] Skipping refund 1815 - cannot delete existing invoice S138991815
[2025-06-17 14:12:36.527 +10:00 INF] Creating refund invoice for refund 1816, Amount: ¤6.80
[2025-06-17 14:12:36.847 +10:00 INF] Deleting existing invoice S143701816
[2025-06-17 14:12:37.173 +10:00 WRN] Failed to delete existing invoice S143701816 - invoice may be locked/paid in MYOB
MYOB.AccountRight.SDK.ApiValidationException: Encountered a validation error (https://arl2.api.myob.com/accountright/b2f20bad-65c4-4dd3-a921-0a20ccfffb73/Sale/Invoice/Service/5ac7ff46-5d70-47ca-a353-44c57a10c35c)
 ---> System.Net.WebException: The remote server returned an error: (400) Bad Request.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiDeleteRequestSync(Uri uri, ICompanyFileCredentials credentials)
   at MYOB.AccountRight.SDK.Services.MutableService`1.Delete(CompanyFile cf, Guid uid, ICompanyFileCredentials credentials, ErrorLevel errorLevel)
   at LepInvoicer.Implementations.MYOBService.<>c__DisplayClass33_1.<DeleteExistingInvoice>b__1() in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 875
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Implementations.MYOBService.DeleteExistingInvoice(String invoiceNumber) in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 874
[2025-06-17 14:12:37.180 +10:00 WRN] Skipping refund 1816 - cannot delete existing invoice S143701816
[2025-06-17 14:12:37.186 +10:00 INF] Creating refund invoice for refund 1817, Amount: ¤19.84
[2025-06-17 14:12:37.528 +10:00 INF] Deleting existing invoice S143701817
[2025-06-17 14:12:37.835 +10:00 WRN] Failed to delete existing invoice S143701817 - invoice may be locked/paid in MYOB
MYOB.AccountRight.SDK.ApiValidationException: Encountered a validation error (https://arl2.api.myob.com/accountright/b2f20bad-65c4-4dd3-a921-0a20ccfffb73/Sale/Invoice/Service/2e674898-d09f-451c-921f-b6054b956d02)
 ---> System.Net.WebException: The remote server returned an error: (400) Bad Request.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiDeleteRequestSync(Uri uri, ICompanyFileCredentials credentials)
   at MYOB.AccountRight.SDK.Services.MutableService`1.Delete(CompanyFile cf, Guid uid, ICompanyFileCredentials credentials, ErrorLevel errorLevel)
   at LepInvoicer.Implementations.MYOBService.<>c__DisplayClass33_1.<DeleteExistingInvoice>b__1() in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 875
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Implementations.MYOBService.DeleteExistingInvoice(String invoiceNumber) in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 874
[2025-06-17 14:12:37.841 +10:00 WRN] Skipping refund 1817 - cannot delete existing invoice S143701817
[2025-06-17 14:12:37.846 +10:00 INF] Creating refund invoice for refund 1818, Amount: ¤61.92
[2025-06-17 14:12:38.229 +10:00 INF] Deleting existing invoice S1531791818
[2025-06-17 14:12:38.528 +10:00 WRN] Failed to delete existing invoice S1531791818 - invoice may be locked/paid in MYOB
MYOB.AccountRight.SDK.ApiValidationException: Encountered a validation error (https://arl2.api.myob.com/accountright/b2f20bad-65c4-4dd3-a921-0a20ccfffb73/Sale/Invoice/Service/f2d3d1a7-f2e9-4aee-b0fe-cf5f0c77f7bc)
 ---> System.Net.WebException: The remote server returned an error: (400) Bad Request.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiDeleteRequestSync(Uri uri, ICompanyFileCredentials credentials)
   at MYOB.AccountRight.SDK.Services.MutableService`1.Delete(CompanyFile cf, Guid uid, ICompanyFileCredentials credentials, ErrorLevel errorLevel)
   at LepInvoicer.Implementations.MYOBService.<>c__DisplayClass33_1.<DeleteExistingInvoice>b__1() in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 875
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Implementations.MYOBService.DeleteExistingInvoice(String invoiceNumber) in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 874
[2025-06-17 14:12:38.534 +10:00 WRN] Skipping refund 1818 - cannot delete existing invoice S1531791818
[2025-06-17 14:12:38.541 +10:00 INF] Creating refund invoice for refund 1824, Amount: ¤70.04
[2025-06-17 14:12:38.889 +10:00 INF] Deleting existing invoice S189871824
[2025-06-17 14:12:39.387 +10:00 WRN] Failed to delete existing invoice S189871824 - invoice may be locked/paid in MYOB
MYOB.AccountRight.SDK.ApiValidationException: Encountered a validation error (https://arl2.api.myob.com/accountright/b2f20bad-65c4-4dd3-a921-0a20ccfffb73/Sale/Invoice/Service/3366c74b-aec7-4ee3-87d0-4ed666a81e70)
 ---> System.Net.WebException: The remote server returned an error: (400) Bad Request.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiDeleteRequestSync(Uri uri, ICompanyFileCredentials credentials)
   at MYOB.AccountRight.SDK.Services.MutableService`1.Delete(CompanyFile cf, Guid uid, ICompanyFileCredentials credentials, ErrorLevel errorLevel)
   at LepInvoicer.Implementations.MYOBService.<>c__DisplayClass33_1.<DeleteExistingInvoice>b__1() in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 875
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Implementations.MYOBService.DeleteExistingInvoice(String invoiceNumber) in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 874
[2025-06-17 14:12:39.393 +10:00 WRN] Skipping refund 1824 - cannot delete existing invoice S189871824
[2025-06-17 14:12:39.398 +10:00 INF] Creating refund invoice for refund 1836, Amount: ¤32.32
[2025-06-17 14:12:39.757 +10:00 INF] Deleting existing invoice S252801836
[2025-06-17 14:12:40.036 +10:00 WRN] Failed to delete existing invoice S252801836 - invoice may be locked/paid in MYOB
MYOB.AccountRight.SDK.ApiValidationException: Encountered a validation error (https://arl2.api.myob.com/accountright/b2f20bad-65c4-4dd3-a921-0a20ccfffb73/Sale/Invoice/Service/4b9f90a9-fe2b-4248-bce3-1aa759007fcd)
 ---> System.Net.WebException: The remote server returned an error: (400) Bad Request.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiDeleteRequestSync(Uri uri, ICompanyFileCredentials credentials)
   at MYOB.AccountRight.SDK.Services.MutableService`1.Delete(CompanyFile cf, Guid uid, ICompanyFileCredentials credentials, ErrorLevel errorLevel)
   at LepInvoicer.Implementations.MYOBService.<>c__DisplayClass33_1.<DeleteExistingInvoice>b__1() in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 875
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Implementations.MYOBService.DeleteExistingInvoice(String invoiceNumber) in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 874
[2025-06-17 14:12:40.042 +10:00 WRN] Skipping refund 1836 - cannot delete existing invoice S252801836
[2025-06-17 14:12:40.068 +10:00 INF] Creating refund invoice for refund 1837, Amount: ¤26.00
[2025-06-17 14:12:40.438 +10:00 INF] Deleting existing invoice S191871837
[2025-06-17 14:12:40.817 +10:00 WRN] Failed to delete existing invoice S191871837 - invoice may be locked/paid in MYOB
MYOB.AccountRight.SDK.ApiValidationException: Encountered a validation error (https://arl2.api.myob.com/accountright/b2f20bad-65c4-4dd3-a921-0a20ccfffb73/Sale/Invoice/Service/27c54bcf-d87b-45d8-a5e4-997d4b831e2e)
 ---> System.Net.WebException: The remote server returned an error: (400) Bad Request.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiDeleteRequestSync(Uri uri, ICompanyFileCredentials credentials)
   at MYOB.AccountRight.SDK.Services.MutableService`1.Delete(CompanyFile cf, Guid uid, ICompanyFileCredentials credentials, ErrorLevel errorLevel)
   at LepInvoicer.Implementations.MYOBService.<>c__DisplayClass33_1.<DeleteExistingInvoice>b__1() in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 875
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Implementations.MYOBService.DeleteExistingInvoice(String invoiceNumber) in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 874
[2025-06-17 14:12:40.823 +10:00 WRN] Skipping refund 1837 - cannot delete existing invoice S191871837
[2025-06-17 14:12:40.848 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 14:12:40.973 +10:00 INF] LEP Invoicer completed successfully in 22433ms. Orders: 2, Credits: 0, Refunds: 16
[2025-06-17 14:12:40.980 +10:00 INF] Database connection disposed
[2025-06-17 14:12:40.982 +10:00 INF] LEP Invoicer completed with result: 0
