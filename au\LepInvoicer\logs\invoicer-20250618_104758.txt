[2025-06-18 10:47:58.914 +10:00 INF] Starting LEP Invoicer application
[2025-06-18 10:47:58.947 +10:00 INF] Initializing FastReport...
[2025-06-18 10:47:59.059 +10:00 INF] FastReport initialized successfully
[2025-06-18 10:47:59.593 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-18 10:48:01.234 +10:00 INF] Starting LEP Invoicer at "2025-06-18T10:48:01.2337576+10:00"
[2025-06-18 10:48:01.237 +10:00 INF] Initializing database service...
[2025-06-18 10:48:01.243 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-18 10:48:01.367 +10:00 INF] Database connection established successfully
[2025-06-18 10:48:01.369 +10:00 INF] Database service initialized successfully
[2025-06-18 10:48:01.374 +10:00 INF] Checking for pending work...
[2025-06-18 10:48:01.381 +10:00 INF] Getting 500 orders to invoice
[2025-06-18 10:48:02.436 +10:00 INF] Found 0 orders to invoice (marked 0 zero-priced as invoiced, filtered 0 candidates)
[2025-06-18 10:48:02.456 +10:00 INF] Getting 100 credits to invoice
[2025-06-18 10:48:02.480 +10:00 INF] Found 0 credits to invoice
[2025-06-18 10:48:02.484 +10:00 INF] Getting 100 refunds to invoice
[2025-06-18 10:48:02.499 +10:00 INF] Found 0 refunds to invoice
[2025-06-18 10:48:02.506 +10:00 INF] No pending work found
[2025-06-18 10:48:02.527 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-18 10:48:02.529 +10:00 INF] Cleaning up invoicer logs...
[2025-06-18 10:48:02.709 +10:00 INF] LEP Invoicer completed successfully in 1475ms. No work to process.
[2025-06-18 10:48:03.127 +10:00 INF] Database connection disposed
[2025-06-18 10:48:03.130 +10:00 INF] LEP Invoicer completed with result: 0
