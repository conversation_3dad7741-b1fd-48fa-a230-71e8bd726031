[2025-06-18 10:02:28.729 +10:00 INF] Starting LEP Invoicer application
[2025-06-18 10:02:28.761 +10:00 INF] Initializing FastReport...
[2025-06-18 10:02:28.842 +10:00 INF] FastReport initialized successfully
[2025-06-18 10:02:29.268 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-18 10:02:30.566 +10:00 INF] Starting LEP Invoicer at "2025-06-18T10:02:30.5659955+10:00"
[2025-06-18 10:02:30.569 +10:00 INF] Initializing database service...
[2025-06-18 10:02:30.572 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-18 10:02:30.671 +10:00 INF] Database connection established successfully
[2025-06-18 10:02:30.672 +10:00 INF] Database service initialized successfully
[2025-06-18 10:02:30.675 +10:00 INF] Checking for pending work...
[2025-06-18 10:02:30.678 +10:00 INF] Getting 500 orders to invoice
[2025-06-18 10:02:31.544 +10:00 INF] Found 0 orders to invoice (filtered 16 candidates)
[2025-06-18 10:02:31.547 +10:00 INF] Getting 100 credits to invoice
[2025-06-18 10:02:31.559 +10:00 INF] Found 0 credits to invoice
[2025-06-18 10:02:31.561 +10:00 INF] Getting 100 refunds to invoice
[2025-06-18 10:02:31.565 +10:00 INF] Found 0 refunds to invoice
[2025-06-18 10:02:31.566 +10:00 INF] No pending work found
[2025-06-18 10:02:31.567 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-18 10:02:31.569 +10:00 INF] Cleaning up invoicer logs...
[2025-06-18 10:02:31.642 +10:00 INF] LEP Invoicer completed successfully in 1076ms. No work to process.
[2025-06-18 10:02:31.648 +10:00 INF] Database connection disposed
[2025-06-18 10:02:31.650 +10:00 INF] LEP Invoicer completed with result: 0
