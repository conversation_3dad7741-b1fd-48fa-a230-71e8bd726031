//namespace lep.www
//{
//	using lumen.csv;
//	using NHibernate;
//	using System;
//	using System.Collections.Generic;
//	using System.Data;
//	using System.Data.SqlClient;

//	public abstract class BulkCopyImport : CsvParser
//    {
//        private IList<string> errors;
//        private DataTable table;
//        private bool forceExit = false;
//        private string tablename;
//        private Type hibernateType;
//        private ISession session;

//        public BulkCopyImport(string tablename, Type hibernateType, ISession session)
//        {
//            HandleQuotes = true;
//            this.tablename = tablename;
//            this.hibernateType = hibernateType;
//            this.session = session;
//        }

//        public override void StartDocument()
//        {
//            errors = new List<string>();
//            ProcessCount = 0;
//            forceExit = false;

//            table = CreateDataTable();

//            base.StartDocument();
//        }

//        protected abstract DataTable CreateDataTable();

//        public override void RowData(string[] values)
//        {
//            base.RowData(values);

//            if (values.Length > 0 && !forceExit)
//            {
//                ProcessCount++;
//                try
//                {
//                    if (ProcessCount == 1)
//                    {
//                        forceExit = !VerifyHeader(values);
//                    }
//                    else
//                    {
//                        DataRow dr = table.NewRow();
//                        if (ProcessRow(dr, values))
//                        {
//                            table.Rows.Add(dr);
//                        }
//                    }
//                }
//                catch (CsvException ex)
//                {
//                    AddError(ex.Message);
//                }
//            }
//        }

//        protected abstract bool VerifyHeader(string[] values);

//        protected abstract bool ProcessRow(DataRow dr, string[] values);

//        public override void EndDocument()
//        {
//            base.EndDocument();

//            if (errors.Count > 0 || forceExit)
//            {
//                return;
//            }

//            SqlConnection con = session.Connection as SqlConnection;
//            if (con == null)
//            {
//                errors.Add("Import only allowed for mssql database");
//                return;
//            }

//            SqlTransaction trans = con.BeginTransaction();
//            try
//            {
//                using (SqlCommand command = con.CreateCommand())
//                {
//                    command.Transaction = trans;
//                    command.CommandType = CommandType.Text;
//                    command.CommandText = String.Format("delete from {0}", tablename);
//                    command.ExecuteNonQuery();
//                }

//                using (SqlBulkCopy bulk = new SqlBulkCopy(con, SqlBulkCopyOptions.CheckConstraints, trans))
//                {
//                    bulk.BulkCopyTimeout = 900;
//                    bulk.BatchSize = 10000;
//                    bulk.DestinationTableName = tablename;
//                    bulk.WriteToServer(table);
//                    bulk.Close();
//                ////}

//                //Mike Greenan - is needed for bulkcpy and inserts when using merge replication
//                using (SqlCommand command = con.CreateCommand())
//                {
//                    command.Transaction = trans;
//                    command.CommandType = CommandType.Text;
//                    command.CommandText = String.Format("Exec sp_addtabletocontents {0}", tablename);
//                    command.ExecuteNonQuery();
//                }

//                trans.Commit();
//            }
//            catch (Exception ex)
//            {
//                trans.Rollback();
//                errors.Add("Data insert fail: " + ex.Message);
//                return;
//            }
//            SessionFactory.Evict(hibernateType);
//        }

//        protected void AddError(string error)
//        {
//            errors.Add(String.Format("Line {0}: {1}", ProcessCount, error));
//        }

//        public IList<string> Errors
//        {
//            get { return errors; }
//        }

//        public int ProcessCount { get; set; }
//        public ISessionFactory SessionFactory { get; set; }
//    }
//}
