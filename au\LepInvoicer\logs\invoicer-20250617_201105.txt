[2025-06-17 20:11:05.506 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 20:11:05.535 +10:00 INF] Initializing FastReport...
[2025-06-17 20:11:05.620 +10:00 INF] FastReport initialized successfully
[2025-06-17 20:11:06.010 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 20:11:07.333 +10:00 INF] Starting LEP Invoicer at "2025-06-17T20:11:07.3329082+10:00"
[2025-06-17 20:11:07.339 +10:00 INF] Initializing database service...
[2025-06-17 20:11:07.342 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 20:11:07.444 +10:00 INF] Database connection established successfully
[2025-06-17 20:11:07.445 +10:00 INF] Database service initialized successfully
[2025-06-17 20:11:07.448 +10:00 INF] Checking for pending work...
[2025-06-17 20:11:07.451 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 20:11:08.329 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 20:11:08.340 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 20:11:08.358 +10:00 INF] Found 0 credits to invoice
[2025-06-17 20:11:08.361 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 20:11:08.367 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 20:11:08.368 +10:00 INF] No pending work found
[2025-06-17 20:11:08.372 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 20:11:08.376 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 20:11:08.470 +10:00 INF] LEP Invoicer completed successfully in 1137ms. No work to process.
[2025-06-17 20:11:08.495 +10:00 INF] Database connection disposed
[2025-06-17 20:11:08.498 +10:00 INF] LEP Invoicer completed with result: 0
