[2025-06-17 16:48:11.409 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 16:48:11.437 +10:00 INF] Initializing FastReport...
[2025-06-17 16:48:11.511 +10:00 INF] FastReport initialized successfully
[2025-06-17 16:48:11.919 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 16:48:13.370 +10:00 INF] Starting LEP Invoicer at "2025-06-17T16:48:13.3699445+10:00"
[2025-06-17 16:48:13.373 +10:00 INF] Initializing database service...
[2025-06-17 16:48:13.376 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 16:48:13.482 +10:00 INF] Database connection established successfully
[2025-06-17 16:48:13.483 +10:00 INF] Database service initialized successfully
[2025-06-17 16:48:13.486 +10:00 INF] Checking for pending work...
[2025-06-17 16:48:13.489 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 16:48:14.424 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 16:48:14.428 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 16:48:14.440 +10:00 INF] Found 0 credits to invoice
[2025-06-17 16:48:14.444 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 16:48:14.448 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 16:48:14.449 +10:00 INF] No pending work found
[2025-06-17 16:48:14.457 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 16:48:14.459 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 16:48:14.537 +10:00 INF] LEP Invoicer completed successfully in 1167ms. No work to process.
[2025-06-17 16:48:14.547 +10:00 INF] Database connection disposed
[2025-06-17 16:48:14.549 +10:00 INF] LEP Invoicer completed with result: 0
