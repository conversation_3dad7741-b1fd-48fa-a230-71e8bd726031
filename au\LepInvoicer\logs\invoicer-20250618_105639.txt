[2025-06-18 10:56:39.613 +10:00 INF] Starting LEP Invoicer application
[2025-06-18 10:56:39.642 +10:00 INF] Initializing FastReport...
[2025-06-18 10:56:39.713 +10:00 INF] FastReport initialized successfully
[2025-06-18 10:56:40.150 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-18 10:56:41.652 +10:00 INF] Starting LEP Invoicer at "2025-06-18T10:56:41.6519370+10:00"
[2025-06-18 10:56:41.655 +10:00 INF] Initializing database service...
[2025-06-18 10:56:41.658 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-18 10:56:41.762 +10:00 INF] Database connection established successfully
[2025-06-18 10:56:41.763 +10:00 INF] Database service initialized successfully
[2025-06-18 10:56:41.767 +10:00 INF] Checking for pending work...
[2025-06-18 10:56:41.772 +10:00 INF] Getting 500 orders to invoice
[2025-06-18 10:56:42.440 +10:00 INF] Found 0 orders to invoice (marked 0 zero-priced as invoiced, filtered 0 candidates)
[2025-06-18 10:56:42.443 +10:00 INF] Getting 100 credits to invoice
[2025-06-18 10:56:42.455 +10:00 INF] Found 0 credits to invoice
[2025-06-18 10:56:42.457 +10:00 INF] Getting 100 refunds to invoice
[2025-06-18 10:56:42.460 +10:00 INF] Found 0 refunds to invoice
[2025-06-18 10:56:42.461 +10:00 INF] No pending work found
[2025-06-18 10:56:42.463 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-18 10:56:42.464 +10:00 INF] Cleaning up invoicer logs...
[2025-06-18 10:56:42.549 +10:00 INF] LEP Invoicer completed successfully in 897ms. No work to process.
[2025-06-18 10:56:42.557 +10:00 INF] Database connection disposed
[2025-06-18 10:56:42.559 +10:00 INF] LEP Invoicer completed with result: 0
