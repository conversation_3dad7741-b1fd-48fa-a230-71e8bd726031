[2025-06-17 14:13:43.878 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 14:13:43.910 +10:00 INF] Initializing FastReport...
[2025-06-17 14:13:43.995 +10:00 INF] FastReport initialized successfully
[2025-06-17 14:13:44.578 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 14:13:46.095 +10:00 INF] Starting LEP Invoicer at "2025-06-17T14:13:46.0954837+10:00"
[2025-06-17 14:13:46.099 +10:00 INF] Initializing database service...
[2025-06-17 14:13:46.102 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 14:13:46.248 +10:00 INF] Database connection established successfully
[2025-06-17 14:13:46.250 +10:00 INF] Database service initialized successfully
[2025-06-17 14:13:46.253 +10:00 INF] Checking for pending work...
[2025-06-17 14:13:46.256 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 14:13:47.705 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 14:13:47.721 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 14:13:47.765 +10:00 INF] Found 0 credits to invoice
[2025-06-17 14:13:47.768 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 14:13:47.816 +10:00 INF] Found 16 refunds to invoice
[2025-06-17 14:13:47.818 +10:00 INF] Found pending work: 16 refunds
[2025-06-17 14:13:47.820 +10:00 INF] Initializing MYOB and other services...
[2025-06-17 14:13:47.823 +10:00 INF] Initializing MYOB service...
[2025-06-17 14:13:47.825 +10:00 INF] Initializing MYOB with OAuth authentication
[2025-06-17 14:13:47.831 +10:00 INF] Starting OAuth authentication flow
[2025-06-17 14:13:47.841 +10:00 INF] Using existing OAuth tokens
[2025-06-17 14:13:47.842 +10:00 INF] Initializing MYOB services with OAuth keystore
[2025-06-17 14:13:47.847 +10:00 INF] MYOB services initialized successfully
[2025-06-17 14:13:47.854 +10:00 INF] OAuth keystore set for API calls
[2025-06-17 14:13:47.857 +10:00 INF] Getting company files from MYOB
[2025-06-17 14:14:48.044 +10:00 ERR] Failed to initialize MYOB service with OAuth
MYOB.AccountRight.SDK.ApiCommunicationException: Encountered a communication error (https://api.myob.com/accountright)
 ---> System.Net.WebException: The remote server returned an error: (504) Gateway Timeout.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiGetRequestSync[T](Uri uri, ICompanyFileCredentials credentials, String eTag)
   at MYOB.AccountRight.SDK.Services.CompanyFileService.GetRange()
   at LepInvoicer.Implementations.MYOBService.<>c__DisplayClass20_0.<InitializeWithOAuth>b__3() in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 136
   at System.Threading.Tasks.Task`1.InnerInvoke()
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Implementations.MYOBService.InitializeWithOAuth() in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 136
[2025-06-17 14:14:48.080 +10:00 ERR] Failed to initialize MYOB service
MYOB.AccountRight.SDK.ApiCommunicationException: Encountered a communication error (https://api.myob.com/accountright)
 ---> System.Net.WebException: The remote server returned an error: (504) Gateway Timeout.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiGetRequestSync[T](Uri uri, ICompanyFileCredentials credentials, String eTag)
   at MYOB.AccountRight.SDK.Services.CompanyFileService.GetRange()
   at LepInvoicer.Implementations.MYOBService.<>c__DisplayClass20_0.<InitializeWithOAuth>b__3() in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 136
   at System.Threading.Tasks.Task`1.InnerInvoke()
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Implementations.MYOBService.InitializeWithOAuth() in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 136
   at LepInvoicer.Implementations.MYOBService.Initialize() in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 85
[2025-06-17 14:14:48.098 +10:00 ERR] LEP Invoicer failed after 62001ms
MYOB.AccountRight.SDK.ApiCommunicationException: Encountered a communication error (https://api.myob.com/accountright)
 ---> System.Net.WebException: The remote server returned an error: (504) Gateway Timeout.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiGetRequestSync[T](Uri uri, ICompanyFileCredentials credentials, String eTag)
   at MYOB.AccountRight.SDK.Services.CompanyFileService.GetRange()
   at LepInvoicer.Implementations.MYOBService.<>c__DisplayClass20_0.<InitializeWithOAuth>b__3() in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 136
   at System.Threading.Tasks.Task`1.InnerInvoke()
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Implementations.MYOBService.InitializeWithOAuth() in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 136
   at LepInvoicer.Implementations.MYOBService.Initialize() in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 85
   at LepInvoicer.Implementations.InvoicerService.InitializeRemainingServices() in C:\LepSF\au\LepInvoicer\Implementations\InvoicerService.cs:line 162
   at LepInvoicer.Implementations.InvoicerService.RunInvoicer() in C:\LepSF\au\LepInvoicer\Implementations\InvoicerService.cs:line 67
[2025-06-17 14:14:48.124 +10:00 INF] Database connection disposed
[2025-06-17 14:14:48.126 +10:00 INF] LEP Invoicer completed with result: 1
