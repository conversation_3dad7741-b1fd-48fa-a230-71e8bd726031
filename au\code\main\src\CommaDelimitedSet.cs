using NHibernate;
using NHibernate.Engine;
using NHibernate.SqlTypes;
using NHibernate.UserTypes;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Linq;

namespace lep
{
    //class MyUserType<TChoice> : ICompositeUserType
    //{
    //    public object Assemble (object cached, ISessionImplementor session, object owner)
    //    {
    //        return DeepCopy(cached);
    //    }

    //    public object DeepCopy (object value)
    //    {
    //        return ((IList<TChoice>)value).ToList();
    //    }

    //    public object Disassemble (object value, ISessionImplementor session)
    //    {
    //        return DeepCopy(value);
    //    }

    //    bool ICompositeUserType.Equals (object x, object y)
    //    {
    //        var list1 = x as IList<TChoice>;
    //        var list2 = y as IList<TChoice>;

    //        return (x == null) ? y == null : list1.SequenceEqual(list2);
    //    }

    //    public int GetHashCode (object x)
    //    {
    //        // example implementation
    //        var list = x as IList<TChoice>;
    //        unchecked {
    //            return list == null ? 0 : list.Sum(choice => choice.GetHashCode());
    //        }
    //    }

    //    public object GetPropertyValue (object component, int property)
    //    {
    //        // the list has no properties to get
    //        throw new NotSupportedException();
    //    }

    //    public bool IsMutable
    //    {
    //        get { return true; }
    //    }

    //    public object NullSafeGet (IDataReader dr, string[] names, ISessionImplementor session, object owner)
    //    {
    //        var str = (string)NHibernateUtil.String.Get(dr, names[0]);
    //        IList<TChoice> ids = str.Split( new []{';', ',' } , StringSplitOptions.RemoveEmptyEntries)
    //            .Select(id => (TChoice) id).ToList();

    //        // HACK: assuming session also implements ISession
    //        return ((ISession)session).QueryOver<TChoice>()
    //            .WhereRestrictionOn(choice => choice.Id).IsInG(ids)
    //            .List();
    //    }

    //    public void NullSafeSet (IDbCommand cmd, object value, int index, bool[] settable, NHibernate.Engine.ISessionImplementor session)
    //    {
    //        var list = value as IList<TChoice>;
    //        NHibernateUtil.String.Set(cmd, string.Join(", ", list.Select(choice => choice.Id.ToString()).ToArray()), index);
    //    }

    //    public string[] PropertyNames
    //    {
    //        get { return new string[0]; }
    //    }

    //    public IType[] PropertyTypes
    //    {
    //        get { return new IType[0]; }
    //    }

    //    public object Replace (object original, object target, NHibernate.Engine.ISessionImplementor session, object owner)
    //    {
    //        return original;
    //    }

    //    public Type ReturnedClass
    //    {
    //        get { return typeof(IList<TChoice>); }
    //    }

    //    public void SetPropertyValue (object component, int property, object value)
    //    {
    //        // the list has no properties to set
    //        throw new NotSupportedException();
    //    }
    //}

    [Serializable]
    public class CommaDelimitedSet<T> : IUserType
    {
        private const string delimiter = ";";

        #region IUserType Members

        public new bool Equals(object x, object y)
        {
            if (ReferenceEquals(x, y))
            {
                return true;
            }
            var xSet = x as ISet<T>;
            var ySet = y as ISet<T>;
            if (xSet == null || ySet == null)
            {
                return false;
            }
            // compare set contents
            return xSet.Except(ySet).Count() == 0 && ySet.Except(xSet).Count() == 0;
        }

        public int GetHashCode(object x)
        {
            return x.GetHashCode();
        }

        //public object NullSafeGet (IDataReader rs, string[] names, object owner)
        //{
        //}

        //public void NullSafeSet (IDbCommand cmd, object value, int index)
        //{
        //}

        public object DeepCopy(object value)
        {
            // return new ISet so that Equals can work
            // see http://www.mail-archive.com/<EMAIL>/msg11054.html
            var set = value as ISet<T>;
            if (set == null)
            {
                return null;
            }
            return new HashSet<T>(set);
        }

        public object Replace(object original, object target, object owner)
        {
            return original;
        }

        public object Assemble(object cached, object owner)
        {
            return DeepCopy(cached);
        }

        public object Disassemble(object value)
        {
            return DeepCopy(value);
        }

        public object NullSafeGet(DbDataReader rs, string[] names, ISessionImplementor session, object owner)
        {
            var outValue = NHibernateUtil.String.NullSafeGet(rs, names[0], session) as string;
            if (string.IsNullOrEmpty(outValue))
            {
                return new HashSet<T>();
            }
            else
            {
                if (typeof(T).IsEnum)
                {
                    var splitArray = outValue.Split(new[] { delimiter }, StringSplitOptions.RemoveEmptyEntries)
                               .Select(x => (T)Enum.Parse(typeof(T), x));

                    return new HashSet<T>(splitArray);
                }
                else
                {
                    var splitArray = outValue.Split(new[] { delimiter }, StringSplitOptions.RemoveEmptyEntries)
                        .Select(x => (T)Convert.ChangeType(x, typeof(T)));
                    return new HashSet<T>(splitArray);
                }
            }
        }

        public void NullSafeSet(DbCommand cmd, object value, int index, ISessionImplementor session)
        {
            var inValue = value as ISet<T>;
            object setValue = inValue == null ? null : string.Join(delimiter, inValue);
            NHibernateUtil.String.NullSafeSet(cmd, setValue, index, session);
        }

        public SqlType[] SqlTypes
        {
            get { return new[] { new SqlType(DbType.String) }; }
        }

        public Type ReturnedType
        {
            get { return typeof(ISet<T>); }
        }

        public bool IsMutable
        {
            get { return true; }
        }

        #endregion IUserType Members
    }
}