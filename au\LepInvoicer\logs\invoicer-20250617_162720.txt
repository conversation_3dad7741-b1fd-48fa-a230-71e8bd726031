[2025-06-17 16:27:20.553 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 16:27:20.581 +10:00 INF] Initializing FastReport...
[2025-06-17 16:27:20.654 +10:00 INF] FastReport initialized successfully
[2025-06-17 16:27:21.098 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 16:27:22.280 +10:00 INF] Starting LEP Invoicer at "2025-06-17T16:27:22.2797256+10:00"
[2025-06-17 16:27:22.284 +10:00 INF] Initializing database service...
[2025-06-17 16:27:22.290 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 16:27:22.399 +10:00 INF] Database connection established successfully
[2025-06-17 16:27:22.400 +10:00 INF] Database service initialized successfully
[2025-06-17 16:27:22.403 +10:00 INF] Checking for pending work...
[2025-06-17 16:27:22.425 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 16:27:23.291 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 16:27:23.294 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 16:27:23.307 +10:00 INF] Found 0 credits to invoice
[2025-06-17 16:27:23.309 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 16:27:23.314 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 16:27:23.316 +10:00 INF] No pending work found
[2025-06-17 16:27:23.317 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 16:27:23.318 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 16:27:23.405 +10:00 INF] LEP Invoicer completed successfully in 1126ms. No work to process.
[2025-06-17 16:27:23.424 +10:00 INF] Database connection disposed
[2025-06-17 16:27:23.426 +10:00 INF] LEP Invoicer completed with result: 0
