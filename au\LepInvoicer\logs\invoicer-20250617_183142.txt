[2025-06-17 18:31:42.468 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 18:31:42.495 +10:00 INF] Initializing FastReport...
[2025-06-17 18:31:42.566 +10:00 INF] FastReport initialized successfully
[2025-06-17 18:31:42.950 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 18:31:44.242 +10:00 INF] Starting LEP Invoicer at "2025-06-17T18:31:44.2421825+10:00"
[2025-06-17 18:31:44.246 +10:00 INF] Initializing database service...
[2025-06-17 18:31:44.248 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 18:31:44.347 +10:00 INF] Database connection established successfully
[2025-06-17 18:31:44.349 +10:00 INF] Database service initialized successfully
[2025-06-17 18:31:44.352 +10:00 INF] Checking for pending work...
[2025-06-17 18:31:44.354 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 18:31:45.230 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 18:31:45.233 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 18:31:45.245 +10:00 INF] Found 0 credits to invoice
[2025-06-17 18:31:45.248 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 18:31:45.252 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 18:31:45.256 +10:00 INF] No pending work found
[2025-06-17 18:31:45.258 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 18:31:45.260 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 18:31:45.341 +10:00 INF] LEP Invoicer completed successfully in 1099ms. No work to process.
[2025-06-17 18:31:45.351 +10:00 INF] Database connection disposed
[2025-06-17 18:31:45.354 +10:00 INF] LEP Invoicer completed with result: 0
