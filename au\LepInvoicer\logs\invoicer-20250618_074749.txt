[2025-06-18 07:47:49.639 +10:00 INF] Starting LEP Invoicer application
[2025-06-18 07:47:49.665 +10:00 INF] Initializing FastReport...
[2025-06-18 07:47:49.738 +10:00 INF] FastReport initialized successfully
[2025-06-18 07:47:50.103 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-18 07:47:51.372 +10:00 INF] Starting LEP Invoicer at "2025-06-18T07:47:51.3722635+10:00"
[2025-06-18 07:47:51.375 +10:00 INF] Initializing database service...
[2025-06-18 07:47:51.378 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-18 07:47:51.479 +10:00 INF] Database connection established successfully
[2025-06-18 07:47:51.480 +10:00 INF] Database service initialized successfully
[2025-06-18 07:47:51.483 +10:00 INF] Checking for pending work...
[2025-06-18 07:47:51.486 +10:00 INF] Getting 500 orders to invoice
[2025-06-18 07:47:52.378 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-18 07:47:52.380 +10:00 INF] Getting 100 credits to invoice
[2025-06-18 07:47:52.393 +10:00 INF] Found 0 credits to invoice
[2025-06-18 07:47:52.395 +10:00 INF] Getting 100 refunds to invoice
[2025-06-18 07:47:52.403 +10:00 INF] Found 0 refunds to invoice
[2025-06-18 07:47:52.405 +10:00 INF] No pending work found
[2025-06-18 07:47:52.406 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-18 07:47:52.407 +10:00 INF] Cleaning up invoicer logs...
[2025-06-18 07:47:52.487 +10:00 INF] LEP Invoicer completed successfully in 1115ms. No work to process.
[2025-06-18 07:47:52.494 +10:00 INF] Database connection disposed
[2025-06-18 07:47:52.496 +10:00 INF] LEP Invoicer completed with result: 0
