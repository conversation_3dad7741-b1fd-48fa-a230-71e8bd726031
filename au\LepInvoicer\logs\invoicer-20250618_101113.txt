[2025-06-18 10:11:13.615 +10:00 INF] Starting LEP Invoicer application
[2025-06-18 10:11:13.646 +10:00 INF] Initializing FastReport...
[2025-06-18 10:11:13.719 +10:00 INF] FastReport initialized successfully
[2025-06-18 10:11:14.100 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-18 10:11:15.610 +10:00 INF] Starting LEP Invoicer at "2025-06-18T10:11:15.6106134+10:00"
[2025-06-18 10:11:15.614 +10:00 INF] Initializing database service...
[2025-06-18 10:11:15.617 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-18 10:11:15.715 +10:00 INF] Database connection established successfully
[2025-06-18 10:11:15.717 +10:00 INF] Database service initialized successfully
[2025-06-18 10:11:15.719 +10:00 INF] Checking for pending work...
[2025-06-18 10:11:15.724 +10:00 INF] Getting 500 orders to invoice
[2025-06-18 10:11:16.583 +10:00 INF] Found 0 orders to invoice (filtered 16 candidates)
[2025-06-18 10:11:16.585 +10:00 INF] Getting 100 credits to invoice
[2025-06-18 10:11:16.597 +10:00 INF] Found 0 credits to invoice
[2025-06-18 10:11:16.600 +10:00 INF] Getting 100 refunds to invoice
[2025-06-18 10:11:16.603 +10:00 INF] Found 0 refunds to invoice
[2025-06-18 10:11:16.605 +10:00 INF] No pending work found
[2025-06-18 10:11:16.606 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-18 10:11:16.608 +10:00 INF] Cleaning up invoicer logs...
[2025-06-18 10:11:16.683 +10:00 INF] LEP Invoicer completed successfully in 1072ms. No work to process.
[2025-06-18 10:11:16.689 +10:00 INF] Database connection disposed
[2025-06-18 10:11:16.691 +10:00 INF] LEP Invoicer completed with result: 0
