[2025-06-17 15:53:21.625 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 15:53:21.652 +10:00 INF] Initializing FastReport...
[2025-06-17 15:53:21.728 +10:00 INF] FastReport initialized successfully
[2025-06-17 15:53:22.173 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 15:53:23.478 +10:00 INF] Starting LEP Invoicer at "2025-06-17T15:53:23.4785334+10:00"
[2025-06-17 15:53:23.482 +10:00 INF] Initializing database service...
[2025-06-17 15:53:23.486 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 15:53:23.584 +10:00 INF] Database connection established successfully
[2025-06-17 15:53:23.585 +10:00 INF] Database service initialized successfully
[2025-06-17 15:53:23.588 +10:00 INF] Checking for pending work...
[2025-06-17 15:53:23.591 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 15:53:24.467 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 15:53:24.470 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 15:53:24.483 +10:00 INF] Found 0 credits to invoice
[2025-06-17 15:53:24.485 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 15:53:24.491 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 15:53:24.493 +10:00 INF] No pending work found
[2025-06-17 15:53:24.494 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 15:53:24.496 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 15:53:24.573 +10:00 INF] LEP Invoicer completed successfully in 1094ms. No work to process.
[2025-06-17 15:53:24.579 +10:00 INF] Database connection disposed
[2025-06-17 15:53:24.581 +10:00 INF] LEP Invoicer completed with result: 0
