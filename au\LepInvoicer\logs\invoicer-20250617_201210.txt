[2025-06-17 20:12:10.754 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 20:12:10.794 +10:00 INF] Initializing FastReport...
[2025-06-17 20:12:10.894 +10:00 INF] FastReport initialized successfully
[2025-06-17 20:12:11.363 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 20:12:12.946 +10:00 INF] Starting LEP Invoicer at "2025-06-17T20:12:12.9458251+10:00"
[2025-06-17 20:12:12.951 +10:00 INF] Initializing database service...
[2025-06-17 20:12:12.955 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 20:12:13.065 +10:00 INF] Database connection established successfully
[2025-06-17 20:12:13.067 +10:00 INF] Database service initialized successfully
[2025-06-17 20:12:13.070 +10:00 INF] Checking for pending work...
[2025-06-17 20:12:13.072 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 20:12:13.950 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 20:12:13.954 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 20:12:13.975 +10:00 INF] Found 0 credits to invoice
[2025-06-17 20:12:13.978 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 20:12:13.981 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 20:12:13.986 +10:00 INF] No pending work found
[2025-06-17 20:12:13.994 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 20:12:13.998 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 20:12:14.073 +10:00 INF] LEP Invoicer completed successfully in 1127ms. No work to process.
[2025-06-17 20:12:14.080 +10:00 INF] Database connection disposed
[2025-06-17 20:12:14.082 +10:00 INF] LEP Invoicer completed with result: 0
