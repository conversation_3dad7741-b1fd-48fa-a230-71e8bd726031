[2025-06-18 07:48:54.534 +10:00 INF] Starting LEP Invoicer application
[2025-06-18 07:48:54.563 +10:00 INF] Initializing FastReport...
[2025-06-18 07:48:54.633 +10:00 INF] FastReport initialized successfully
[2025-06-18 07:48:55.097 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-18 07:48:56.396 +10:00 INF] Starting LEP Invoicer at "2025-06-18T07:48:56.3962860+10:00"
[2025-06-18 07:48:56.403 +10:00 INF] Initializing database service...
[2025-06-18 07:48:56.409 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-18 07:48:56.520 +10:00 INF] Database connection established successfully
[2025-06-18 07:48:56.521 +10:00 INF] Database service initialized successfully
[2025-06-18 07:48:56.524 +10:00 INF] Checking for pending work...
[2025-06-18 07:48:56.527 +10:00 INF] Getting 500 orders to invoice
[2025-06-18 07:48:57.446 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-18 07:48:57.448 +10:00 INF] Getting 100 credits to invoice
[2025-06-18 07:48:57.460 +10:00 INF] Found 0 credits to invoice
[2025-06-18 07:48:57.463 +10:00 INF] Getting 100 refunds to invoice
[2025-06-18 07:48:57.466 +10:00 INF] Found 0 refunds to invoice
[2025-06-18 07:48:57.469 +10:00 INF] No pending work found
[2025-06-18 07:48:57.470 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-18 07:48:57.471 +10:00 INF] Cleaning up invoicer logs...
[2025-06-18 07:48:57.579 +10:00 INF] LEP Invoicer completed successfully in 1183ms. No work to process.
[2025-06-18 07:48:57.586 +10:00 INF] Database connection disposed
[2025-06-18 07:48:57.588 +10:00 INF] LEP Invoicer completed with result: 0
