[2025-06-18 10:25:07.615 +10:00 INF] Starting LEP Invoicer application
[2025-06-18 10:25:07.647 +10:00 INF] Initializing FastReport...
[2025-06-18 10:25:07.739 +10:00 INF] FastReport initialized successfully
[2025-06-18 10:25:08.237 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-18 10:25:09.624 +10:00 INF] Starting LEP Invoicer at "2025-06-18T10:25:09.6242704+10:00"
[2025-06-18 10:25:09.628 +10:00 INF] Initializing database service...
[2025-06-18 10:25:09.632 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-18 10:25:09.732 +10:00 INF] Database connection established successfully
[2025-06-18 10:25:09.733 +10:00 INF] Database service initialized successfully
[2025-06-18 10:25:09.735 +10:00 INF] Checking for pending work...
[2025-06-18 10:25:09.739 +10:00 INF] Getting 500 orders to invoice
[2025-06-18 10:25:10.646 +10:00 INF] Found 0 orders to invoice (filtered 16 candidates)
[2025-06-18 10:25:10.649 +10:00 INF] Getting 100 credits to invoice
[2025-06-18 10:25:10.667 +10:00 INF] Found 0 credits to invoice
[2025-06-18 10:25:10.669 +10:00 INF] Getting 100 refunds to invoice
[2025-06-18 10:25:10.672 +10:00 INF] Found 0 refunds to invoice
[2025-06-18 10:25:10.674 +10:00 INF] No pending work found
[2025-06-18 10:25:10.675 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-18 10:25:10.676 +10:00 INF] Cleaning up invoicer logs...
[2025-06-18 10:25:10.754 +10:00 INF] LEP Invoicer completed successfully in 1130ms. No work to process.
[2025-06-18 10:25:10.761 +10:00 INF] Database connection disposed
[2025-06-18 10:25:10.762 +10:00 INF] LEP Invoicer completed with result: 0
