[2025-06-17 16:59:03.874 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 16:59:03.902 +10:00 INF] Initializing FastReport...
[2025-06-17 16:59:03.981 +10:00 INF] FastReport initialized successfully
[2025-06-17 16:59:04.409 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 16:59:05.704 +10:00 INF] Starting LEP Invoicer at "2025-06-17T16:59:05.7038933+10:00"
[2025-06-17 16:59:05.707 +10:00 INF] Initializing database service...
[2025-06-17 16:59:05.710 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 16:59:05.810 +10:00 INF] Database connection established successfully
[2025-06-17 16:59:05.812 +10:00 INF] Database service initialized successfully
[2025-06-17 16:59:05.814 +10:00 INF] Checking for pending work...
[2025-06-17 16:59:05.817 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 16:59:06.749 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 16:59:06.752 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 16:59:06.765 +10:00 INF] Found 0 credits to invoice
[2025-06-17 16:59:06.767 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 16:59:06.771 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 16:59:06.773 +10:00 INF] No pending work found
[2025-06-17 16:59:06.774 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 16:59:06.776 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 16:59:06.848 +10:00 INF] LEP Invoicer completed successfully in 1144ms. No work to process.
[2025-06-17 16:59:06.854 +10:00 INF] Database connection disposed
[2025-06-17 16:59:06.857 +10:00 INF] LEP Invoicer completed with result: 0
