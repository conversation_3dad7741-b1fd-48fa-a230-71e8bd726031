[2025-06-17 20:09:59.514 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 20:09:59.554 +10:00 INF] Initializing FastReport...
[2025-06-17 20:09:59.641 +10:00 INF] FastReport initialized successfully
[2025-06-17 20:10:00.024 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 20:10:01.289 +10:00 INF] Starting LEP Invoicer at "2025-06-17T20:10:01.2888956+10:00"
[2025-06-17 20:10:01.293 +10:00 INF] Initializing database service...
[2025-06-17 20:10:01.296 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 20:10:01.408 +10:00 INF] Database connection established successfully
[2025-06-17 20:10:01.411 +10:00 INF] Database service initialized successfully
[2025-06-17 20:10:01.421 +10:00 INF] Checking for pending work...
[2025-06-17 20:10:01.428 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 20:10:02.495 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 20:10:02.498 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 20:10:02.524 +10:00 INF] Found 0 credits to invoice
[2025-06-17 20:10:02.531 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 20:10:02.550 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 20:10:02.553 +10:00 INF] No pending work found
[2025-06-17 20:10:02.555 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 20:10:02.556 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 20:10:02.692 +10:00 INF] LEP Invoicer completed successfully in 1403ms. No work to process.
[2025-06-17 20:10:02.705 +10:00 INF] Database connection disposed
[2025-06-17 20:10:02.706 +10:00 INF] LEP Invoicer completed with result: 0
