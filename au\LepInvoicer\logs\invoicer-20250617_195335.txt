[2025-06-17 19:53:35.652 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 19:53:35.682 +10:00 INF] Initializing FastReport...
[2025-06-17 19:53:35.755 +10:00 INF] FastReport initialized successfully
[2025-06-17 19:53:36.172 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 19:53:37.567 +10:00 INF] Starting LEP Invoicer at "2025-06-17T19:53:37.5669204+10:00"
[2025-06-17 19:53:37.570 +10:00 INF] Initializing database service...
[2025-06-17 19:53:37.573 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 19:53:37.680 +10:00 INF] Database connection established successfully
[2025-06-17 19:53:37.682 +10:00 INF] Database service initialized successfully
[2025-06-17 19:53:37.685 +10:00 INF] Checking for pending work...
[2025-06-17 19:53:37.688 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 19:53:38.549 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 19:53:38.554 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 19:53:38.568 +10:00 INF] Found 0 credits to invoice
[2025-06-17 19:53:38.571 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 19:53:38.575 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 19:53:38.576 +10:00 INF] No pending work found
[2025-06-17 19:53:38.580 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 19:53:38.587 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 19:53:38.674 +10:00 INF] LEP Invoicer completed successfully in 1107ms. No work to process.
[2025-06-17 19:53:38.681 +10:00 INF] Database connection disposed
[2025-06-17 19:53:38.683 +10:00 INF] LEP Invoicer completed with result: 0
