[2025-06-18 08:49:29.534 +10:00 INF] Starting LEP Invoicer application
[2025-06-18 08:49:29.564 +10:00 INF] Initializing FastReport...
[2025-06-18 08:49:29.642 +10:00 INF] FastReport initialized successfully
[2025-06-18 08:49:30.077 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-18 08:49:31.365 +10:00 INF] Starting LEP Invoicer at "2025-06-18T08:49:31.3656692+10:00"
[2025-06-18 08:49:31.369 +10:00 INF] Initializing database service...
[2025-06-18 08:49:31.371 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-18 08:49:31.468 +10:00 INF] Database connection established successfully
[2025-06-18 08:49:31.469 +10:00 INF] Database service initialized successfully
[2025-06-18 08:49:31.471 +10:00 INF] Checking for pending work...
[2025-06-18 08:49:31.474 +10:00 INF] Getting 500 orders to invoice
[2025-06-18 08:49:32.360 +10:00 INF] Found 0 orders to invoice (filtered 16 candidates)
[2025-06-18 08:49:32.363 +10:00 INF] Getting 100 credits to invoice
[2025-06-18 08:49:32.378 +10:00 INF] Found 0 credits to invoice
[2025-06-18 08:49:32.382 +10:00 INF] Getting 100 refunds to invoice
[2025-06-18 08:49:32.385 +10:00 INF] Found 0 refunds to invoice
[2025-06-18 08:49:32.387 +10:00 INF] No pending work found
[2025-06-18 08:49:32.388 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-18 08:49:32.391 +10:00 INF] Cleaning up invoicer logs...
[2025-06-18 08:49:32.474 +10:00 INF] LEP Invoicer completed successfully in 1108ms. No work to process.
[2025-06-18 08:49:32.481 +10:00 INF] Database connection disposed
[2025-06-18 08:49:32.482 +10:00 INF] LEP Invoicer completed with result: 0
