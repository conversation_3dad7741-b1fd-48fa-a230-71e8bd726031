[2025-06-18 08:43:57.497 +10:00 INF] Starting LEP Invoicer application
[2025-06-18 08:43:57.528 +10:00 INF] Initializing FastReport...
[2025-06-18 08:43:57.599 +10:00 INF] FastReport initialized successfully
[2025-06-18 08:43:58.020 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-18 08:43:59.289 +10:00 INF] Starting LEP Invoicer at "2025-06-18T08:43:59.2889393+10:00"
[2025-06-18 08:43:59.293 +10:00 INF] Initializing database service...
[2025-06-18 08:43:59.296 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-18 08:43:59.424 +10:00 INF] Database connection established successfully
[2025-06-18 08:43:59.436 +10:00 INF] Database service initialized successfully
[2025-06-18 08:43:59.439 +10:00 INF] Checking for pending work...
[2025-06-18 08:43:59.444 +10:00 INF] Getting 500 orders to invoice
[2025-06-18 08:44:00.395 +10:00 INF] Found 0 orders to invoice (filtered 16 candidates)
[2025-06-18 08:44:00.398 +10:00 INF] Getting 100 credits to invoice
[2025-06-18 08:44:00.411 +10:00 INF] Found 0 credits to invoice
[2025-06-18 08:44:00.413 +10:00 INF] Getting 100 refunds to invoice
[2025-06-18 08:44:00.418 +10:00 INF] Found 0 refunds to invoice
[2025-06-18 08:44:00.419 +10:00 INF] No pending work found
[2025-06-18 08:44:00.421 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-18 08:44:00.422 +10:00 INF] Cleaning up invoicer logs...
[2025-06-18 08:44:00.503 +10:00 INF] LEP Invoicer completed successfully in 1214ms. No work to process.
[2025-06-18 08:44:00.510 +10:00 INF] Database connection disposed
[2025-06-18 08:44:00.511 +10:00 INF] LEP Invoicer completed with result: 0
