[2025-06-17 20:03:25.905 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 20:03:25.941 +10:00 INF] Initializing FastReport...
[2025-06-17 20:03:26.019 +10:00 INF] FastReport initialized successfully
[2025-06-17 20:03:26.491 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 20:03:27.808 +10:00 INF] Starting LEP Invoicer at "2025-06-17T20:03:27.8083043+10:00"
[2025-06-17 20:03:27.822 +10:00 INF] Initializing database service...
[2025-06-17 20:03:27.825 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 20:03:27.934 +10:00 INF] Database connection established successfully
[2025-06-17 20:03:27.936 +10:00 INF] Database service initialized successfully
[2025-06-17 20:03:27.939 +10:00 INF] Checking for pending work...
[2025-06-17 20:03:27.942 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 20:03:28.791 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 20:03:28.794 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 20:03:28.807 +10:00 INF] Found 0 credits to invoice
[2025-06-17 20:03:28.809 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 20:03:28.813 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 20:03:28.815 +10:00 INF] No pending work found
[2025-06-17 20:03:28.816 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 20:03:28.818 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 20:03:28.905 +10:00 INF] LEP Invoicer completed successfully in 1097ms. No work to process.
[2025-06-17 20:03:28.912 +10:00 INF] Database connection disposed
[2025-06-17 20:03:28.914 +10:00 INF] LEP Invoicer completed with result: 0
