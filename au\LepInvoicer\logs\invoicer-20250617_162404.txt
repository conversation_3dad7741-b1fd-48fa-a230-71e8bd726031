[2025-06-17 16:24:04.087 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 16:24:04.121 +10:00 INF] Initializing FastReport...
[2025-06-17 16:24:04.214 +10:00 INF] FastReport initialized successfully
[2025-06-17 16:24:04.650 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 16:24:05.942 +10:00 INF] Starting LEP Invoicer at "2025-06-17T16:24:05.9416971+10:00"
[2025-06-17 16:24:05.953 +10:00 INF] Initializing database service...
[2025-06-17 16:24:05.956 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 16:24:06.058 +10:00 INF] Database connection established successfully
[2025-06-17 16:24:06.059 +10:00 INF] Database service initialized successfully
[2025-06-17 16:24:06.062 +10:00 INF] Checking for pending work...
[2025-06-17 16:24:06.065 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 16:24:06.944 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 16:24:06.946 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 16:24:06.961 +10:00 INF] Found 0 credits to invoice
[2025-06-17 16:24:06.963 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 16:24:06.968 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 16:24:06.969 +10:00 INF] No pending work found
[2025-06-17 16:24:06.971 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 16:24:06.972 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 16:24:07.047 +10:00 INF] LEP Invoicer completed successfully in 1106ms. No work to process.
[2025-06-17 16:24:07.055 +10:00 INF] Database connection disposed
[2025-06-17 16:24:07.058 +10:00 INF] LEP Invoicer completed with result: 0
