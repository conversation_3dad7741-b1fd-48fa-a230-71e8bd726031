using lep.configuration;
using lep.courier;
using lep.despatch.impl;
using lep.email;
using lep.freight;
using lep.job;
using lep.jobmonitor.impl;
using lep.order;
using lep.pricing;
using lep.promotion;
using lep.user;

namespace lep
{
    public class Lep
    {
        public IConfigurationApplication ConfigApp { get; private set; }
        public ICourierApplication CourierApp { get; private set; }
        public IEmailApplication EmailApp { get; private set; }
        public IFreightApplication FreightApp { get; private set; }
        public IJobApplication JobApp { get; private set; }
        public JobBoardDTOHelper JobBoardDTOHelper { get; private set; }
        public IOrderApplication OrderApp { get; private set; }
        public IPricingEngine PricingEngine { get; private set; }
        public IPromotionApplication PromotionApp { get; private set; }
        public IUserApplication UserApp { get; private set; }
        public LabelPrinterApplication LabelPrintApp { get; private set; }
        public PrintEngine PrintEngine { get; private set; }

        public Lep(
            IUserApplication userApplication,
            IOrderApplication orderApp,
            IJobApplication jobApp,
            IEmailApplication emailApp,
            IPricingEngine pricingEngine,
            IFreightApplication freightApplication,
            ICourierApplication courierApplication,
            IPromotionApplication promotionApplication,
            IConfigurationApplication configurationApplication,
            JobBoardDTOHelper jobBoardDTOHelper,
            PrintEngine printEngine,
            LabelPrinterApplication labelPrintApp
        )
        {
            OrderApp = orderApp;
            JobApp = jobApp;
            EmailApp = emailApp;
            UserApp = userApplication;
            PricingEngine = pricingEngine;
            FreightApp = freightApplication;
            CourierApp = courierApplication;
            PromotionApp = promotionApplication;
            ConfigApp = configurationApplication;
            JobBoardDTOHelper = jobBoardDTOHelper;
            PrintEngine = printEngine;
            LabelPrintApp = labelPrintApp;
        }
    }
}