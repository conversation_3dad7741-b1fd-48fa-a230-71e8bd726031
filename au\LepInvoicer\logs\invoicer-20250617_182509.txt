[2025-06-17 18:25:09.709 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 18:25:09.746 +10:00 INF] Initializing FastReport...
[2025-06-17 18:25:09.828 +10:00 INF] FastReport initialized successfully
[2025-06-17 18:25:10.210 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 18:25:11.487 +10:00 INF] Starting LEP Invoicer at "2025-06-17T18:25:11.4874598+10:00"
[2025-06-17 18:25:11.491 +10:00 INF] Initializing database service...
[2025-06-17 18:25:11.494 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 18:25:11.627 +10:00 INF] Database connection established successfully
[2025-06-17 18:25:11.629 +10:00 INF] Database service initialized successfully
[2025-06-17 18:25:11.632 +10:00 INF] Checking for pending work...
[2025-06-17 18:25:11.642 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 18:25:12.526 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 18:25:12.529 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 18:25:12.540 +10:00 INF] Found 0 credits to invoice
[2025-06-17 18:25:12.542 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 18:25:12.546 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 18:25:12.549 +10:00 INF] No pending work found
[2025-06-17 18:25:12.550 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 18:25:12.552 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 18:25:12.633 +10:00 INF] LEP Invoicer completed successfully in 1145ms. No work to process.
[2025-06-17 18:25:12.639 +10:00 INF] Database connection disposed
[2025-06-17 18:25:12.641 +10:00 INF] LEP Invoicer completed with result: 0
