using lep.job;
using lep.order;
using lep.user;
using System;
using System.Collections.Generic;

namespace lep.run
{
	public interface IRun
    {
        int Id { get; set; }
        string RunNr { get; }
        string Barcode { get; }
        bool IsBusinessCard { get; set; }
        IStock Stock { get; set; }
        bool Cmyk { get; set; }
        RunCelloglazeOptions Celloglaze { get; set; }
        JobPrintOptions BackPrint { get; set; }

        DateTime? StartedDate { get; set; }
        RunStatusOptions Status { get; set; }
        IStaff Operator { get; set; }
        bool Urgent { get; set; }
        DateTime? MinSubmitDate { get; }

        bool ManuallyManage { get; set; }
        IList<ILayout> LayoutFiles { get; set; }
        IList<IJob> Jobs { get; set; }
        IList<IJob> ActiveJobs { get; }
        IList<IRunSlot> Slots { get; set; }
        DateTime DateCreated { get; set; }
        DateTime DateModified { get; set; }
        bool FileRemoved { get; set; }
        bool FileMoveRequired { get; set; }
        bool IsHalfBC { get; }

		PrintType PrintType { get; set; }
        int? NumOfPressSheets { get; set; }

		string QtyList { get; set; }

        Facility Facility { get; set; }
        IList<IOrder> FreightReadyOrders { get; }
        int ScanCount { get; set; }

        void LayoutDone(ILayout layout, IStaff prepressBy);

        void SetStatus(RunStatusOptions s, IUser user);

        void SetStatus();

        bool CanAccept(IJob job, out bool forceNeeded, out bool oversize, out string message);

        void AddJob(IJob job, IStaff staff, bool addComment = true);

        void RemoveJob(IJob job, IStaff staff, bool addComment = true);

        int CalculateUsedBC();
		int? TSlots { get; set; }

		bool HasMeetRule();

        ILayout GetLayout(LayoutType layouttype, int page);

        void AddSlot(IJob job, int slot);
		bool Is250 { get; }

		DateTime? EarliestMinEDD { get; }

		void AddComment(IUser author, string commentText, bool staffOnly = false);
	}
}
