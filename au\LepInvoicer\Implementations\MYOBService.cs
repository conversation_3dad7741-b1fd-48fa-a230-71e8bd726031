using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using MYOB.AccountRight.SDK;
using MYOB.AccountRight.SDK.Contracts.Version2.Sale;
using MYOB.AccountRight.SDK.Services.Sale;
using MYOB.AccountRight.SDK.Services;
using MYOB.AccountRight.SDK.Contracts.Version2.Contact;
using MYOB.AccountRight.SDK.Contracts.Version2.GeneralLedger;
using MYOB.AccountRight.SDK.Services.Contact;
using MYOB.AccountRight.SDK.Services.GeneralLedger;
using LepInvoicer.Implementations.Myob;
using LepInvoicer.Implementations;
using LepInvoicer.Interfaces;

namespace LepInvoicer.Implementations;

/// <summary>
/// MYOB integration service implementation
/// </summary>
public class MYOBService : IMYOBService
{
    private readonly ILogger<MYOBService> _logger;
    private readonly InvoicerConfiguration _config;
    private CompanyFile _companyFile;
    private ICompanyFileCredentials _credentials;
    private MYOB.AccountRight.SDK.IOAuthKeyService _oauthKeystore;
    private ServiceInvoiceService _serviceInvoiceService;
    private ItemInvoiceService _itemInvoiceService;
    private string _lastErrorMessage;
    private CustomerService _customerService;
    private AccountService _accountService;
    private TaxCodeService _taxCodeService;
    private EmployeeService _employeeService;
    private ApiConfiguration _apiConfiguration;

    // Cached MYOB entities
    private Dictionary<string, Account> _accounts;
    private TaxCodeLink _gstTaxCodeLink;
    private AccountLink _freightAccountLink;
    private AccountLink _discountsAccountLink;

    /// <summary>
    /// Gets the last error message from MYOB operations
    /// </summary>
    public string LastErrorMessage => _lastErrorMessage;

    public MYOBService(ILogger<MYOBService> logger, IOptions<InvoicerConfiguration> config)
    {
        _logger = logger;
        _config = config.Value;

        _logger.LogDebug("MYOBService initialized");
    }

    public async Task Initialize()
    {
        _logger.LogInformation("Initializing MYOB service...");

        // Check if test mode is enabled
        if (_config.TestMode)
        {
            _logger.LogWarning("MYOB service running in TEST MODE - MYOB integration disabled");
            return;
        }

        try
        {
            // Validate configuration
            if (string.IsNullOrEmpty(_config.MYOB.DeveloperKey) ||
                string.IsNullOrEmpty(_config.MYOB.DeveloperSecret) ||
                string.IsNullOrEmpty(_config.MYOB.ConfirmationUrl) ||
                string.IsNullOrEmpty(_config.MYOB.CompanyFileName))
            {
                throw new InvalidOperationException("MYOB configuration is incomplete. Please check appsettings.json");
            }

            // Initialize MYOB API configuration
            _apiConfiguration = new ApiConfiguration(_config.MYOB.DeveloperKey, _config.MYOB.DeveloperSecret, _config.MYOB.ConfirmationUrl);

            // Set security protocol for MYOB API (required for cloud MYOB)
            System.Net.ServicePointManager.SecurityProtocol = System.Net.SecurityProtocolType.Tls12 | System.Net.SecurityProtocolType.Tls11;

            // For MYOB AccountRight cloud, we need to use OAuth authentication
            _logger.LogInformation("Initializing MYOB with OAuth authentication");
            await InitializeWithOAuth();

            // Initialize cached entities
            await InitializeCachedEntities();

            _logger.LogInformation("MYOB service initialized successfully with company file: {CompanyFile}", _companyFile.Name);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to initialize MYOB service");
            throw;
        }
    }

    private async Task InitializeWithOAuth()
    {
        try
        {
            _logger.LogInformation("Starting OAuth authentication flow");

            var oauthService = new OAuthService(_apiConfiguration);
            var keystore = new OAuthKeyService(new Microsoft.Extensions.Logging.Abstractions.NullLogger<OAuthKeyService>());
            OAuthTokens tokens;

            if (keystore.OAuthResponse != null)
            {
                tokens = keystore.OAuthResponse;
                _logger.LogInformation("Using existing OAuth tokens");
            }
            else
            {
                _logger.LogInformation("No existing OAuth tokens found, starting OAuth flow");
                var authCode = OAuthLogin.GetAuthorizationCode(_apiConfiguration);
                tokens = await Task.Run(() => oauthService.GetTokens(authCode));
                keystore.OAuthResponse = tokens;
                _logger.LogInformation("OAuth tokens obtained and saved");
            }

            // Initialize services with OAuth keystore
            _logger.LogInformation("Initializing MYOB services with OAuth keystore");
            InitializeServices(_apiConfiguration, null, keystore);
            _logger.LogInformation("MYOB services initialized successfully");

            // For OAuth authentication, we use the keystore directly as credentials
            // Store the keystore for use in API calls
            _oauthKeystore = keystore;
            _logger.LogInformation("OAuth keystore set for API calls");

            // Get company file with OAuth credentials
            _logger.LogInformation("Getting company files from MYOB");
            var companyFileService = new CompanyFileService(_apiConfiguration, null, keystore);
            var companyFiles = await Task.Run(() => companyFileService.GetRange());

            _logger.LogDebug("Found {CompanyFileCount} company files", companyFiles.Count());

            _companyFile = companyFiles.FirstOrDefault(cf => cf.Name.Contains(_config.MYOB.CompanyFileName));

            if (_companyFile == null)
            {
                _logger.LogError("MYOB company file '{CompanyFileName}' not found. Available files: {AvailableFiles}",
                    _config.MYOB.CompanyFileName,
                    string.Join(", ", companyFiles.Select(cf => cf.Name)));
                throw new InvalidOperationException($"MYOB company file '{_config.MYOB.CompanyFileName}' not found");
            }

            _logger.LogInformation("MYOB service initialized with OAuth credentials. Using company file: {CompanyFileName}", _companyFile.Name);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to initialize MYOB service with OAuth");
            throw;
        }
    }

    public async Task<bool> CreateOrderInvoice(IOrder order)
    {
        // Clear any previous error message
        _lastErrorMessage = null;

        if (_config.TestMode)
        {
            _logger.LogInformation("TEST MODE: Simulating MYOB invoice creation for order {OrderId}", order.Id);
            await Task.Delay(100); // Simulate some processing time
            return true;
        }

        if (_companyFile == null || _oauthKeystore == null || _serviceInvoiceService == null)
        {
            var nullItems = new List<string>();
            if (_companyFile == null) nullItems.Add("CompanyFile");
            if (_oauthKeystore == null) nullItems.Add("OAuthKeystore");
            if (_serviceInvoiceService == null) nullItems.Add("ServiceInvoiceService");

            throw new InvalidOperationException($"MYOB service not initialized. Null items: {string.Join(", ", nullItems)}");
        }

        _logger.LogInformation("Creating order invoice for order {OrderId}", order.Id);

        try
        {
            var orderPrice = decimal.Round(order.PriceOfJobs ?? 0, 2);
            var invoiceNumber = $"O{order.Id}";

            // Delete existing invoice if it exists - if deletion fails, skip this order
            if (!DeleteInvoiceInternal(invoiceNumber))
            {
                _lastErrorMessage = $"Cannot delete existing invoice {invoiceNumber} - invoice may be locked or paid in MYOB";
                _logger.LogWarning("Skipping order {OrderId} - cannot delete existing invoice {InvoiceNumber}", order.Id, invoiceNumber);
                return false;
            }

            // Create new service invoice
            var orderInvoice = new ServiceInvoice
            {
                Number = invoiceNumber,
                CustomerPurchaseOrderNumber = InvoicerUtilities.TruncateLongString(order.PurchaseOrder ?? "", 20),
                InvoiceDeliveryStatus = DocumentAction.PrintAndEmail,
                InvoiceType = InvoiceLayoutType.Service,
                Subtotal = orderPrice,
                TotalAmount = orderPrice,
                BalanceDueAmount = orderPrice,
                IsTaxInclusive = false,
                Date = order.FinishDate ?? DateTime.Now,
                Customer = GetCustomerLinkFromOrder(order),
                JournalMemo = $"I2- {order.Customer.Name}",
                Lines = CreateInvoiceLinesFromOrder(order)
            };

            var result = _serviceInvoiceService.InsertEx(_companyFile, orderInvoice, _oauthKeystore);

            _logger.LogInformation("Successfully created MYOB invoice {InvoiceNumber} for order {OrderId}",
                invoiceNumber, order.Id);

            return true;
        }
        catch (Exception ex)
        {
            // Store detailed error message for database logging
            _lastErrorMessage = FormatMYOBError(ex);
            _logger.LogError(ex, "Failed to create MYOB invoice for order {OrderId}", order.Id);
            return false;
        }
    }

    public async Task<bool> CreateCreditInvoice(OrderCredit orderCredit)
    {
        // Clear any previous error message
        _lastErrorMessage = null;

        if (_config.TestMode)
        {
            _logger.LogInformation("TEST MODE: Simulating MYOB credit invoice creation for credit {CreditId}", orderCredit.Id);
            return true;
        }

        if (_companyFile == null || _serviceInvoiceService == null)
            throw new InvalidOperationException("MYOB service not initialized");

        _logger.LogInformation("Creating credit invoice for credit {CreditId}, Amount: {Amount:C}",
            orderCredit.Id, orderCredit.Amount);

        try
        {
            // Generate invoice number: C{OrderId}{CreditId} or M{OrderId}{CreditId}
            var invoiceNumber = InvoicerUtilities.GenerateInvoiceNumber(
                orderCredit.Type,
                orderCredit.Order?.Id,
                orderCredit.Id);

            // Delete any existing invoice with the same number - if deletion fails, skip this credit
            if (!await DeleteExistingInvoice(invoiceNumber))
            {
                _lastErrorMessage = $"Cannot delete existing invoice {invoiceNumber} - invoice may be locked or paid in MYOB";
                _logger.LogWarning("Skipping credit {CreditId} - cannot delete existing invoice {InvoiceNumber}", orderCredit.Id, invoiceNumber);
                return false;
            }

            // Create the service invoice
            var serviceInvoice = new ServiceInvoice
            {
                Number = invoiceNumber,
                CustomerPurchaseOrderNumber = InvoicerUtilities.TruncateString(
                    orderCredit.Order?.PurchaseOrder,
                    InvoicerConstants.StringLimits.PurchaseOrderMaxLength),
                InvoiceDeliveryStatus = DocumentAction.PrintAndEmail,
                InvoiceType = InvoiceLayoutType.Service,
                Subtotal = orderCredit.Amount,
                TotalAmount = orderCredit.Amount,
                BalanceDueAmount = orderCredit.Amount,
                IsTaxInclusive = false,
                Date = orderCredit.DateCreated,
                JournalMemo = "I2"
            };

            // Set customer
            serviceInvoice.Customer = GetCustomerLinkFromOrderCredit(orderCredit);

            // Create invoice line
            var invoiceLine = new ServiceInvoiceLine
            {
                Description = orderCredit.Description,
                Total = orderCredit.Amount,
                TaxCode = _gstTaxCodeLink ?? new TaxCodeLink { UID = Guid.NewGuid() }
            };

            // Set account if specified
            if (!string.IsNullOrEmpty(orderCredit.Account) && _accounts != null && _accounts.ContainsKey(orderCredit.Account))
            {
                var account = _accounts[orderCredit.Account];
                invoiceLine.Account = new AccountLink { UID = account.UID };
            }

            serviceInvoice.Lines = new List<ServiceInvoiceLine> { invoiceLine };

            // Create the invoice in MYOB
            var createdInvoice = await Task.Run(() =>
                _serviceInvoiceService.InsertEx(_companyFile, serviceInvoice, _oauthKeystore));

            // Add delay to respect API rate limits
            await Task.Delay(InvoicerConstants.Timing.ApiCallDelayMs);

            _logger.LogInformation("Credit invoice {InvoiceNumber} created successfully for credit {CreditId}",
                invoiceNumber, orderCredit.Id);

            return true;
        }
        catch (Exception ex)
        {
            // Store detailed error message for database logging
            _lastErrorMessage = FormatMYOBError(ex);
            _logger.LogError(ex, "Failed to create credit invoice for credit {CreditId}", orderCredit.Id);
            return false;
        }
    }

    public async Task<bool> CreateRefundInvoice(OrderCredit orderCredit)
    {
        // Clear any previous error message
        _lastErrorMessage = null;

        if (_config.TestMode)
        {
            _logger.LogInformation("TEST MODE: Simulating MYOB refund invoice creation for refund {RefundId}", orderCredit.Id);
            return true;
        }

        if (_companyFile == null || _serviceInvoiceService == null)
            throw new InvalidOperationException("MYOB service not initialized");

        _logger.LogInformation("Creating refund invoice for refund {RefundId}, Amount: {Amount:C}",
            orderCredit.Id, orderCredit.Amount);

        try
        {
            // Generate invoice number: S{CustomerId}{CreditId}
            var invoiceNumber = InvoicerUtilities.GenerateInvoiceNumber(
                orderCredit.Type,
                customerId: orderCredit.Customer?.Id,
                creditId: orderCredit.Id);

            // Delete any existing invoice with the same number - if deletion fails, skip this refund
            if (!await DeleteExistingInvoice(invoiceNumber))
            {
                _lastErrorMessage = $"Cannot delete existing invoice {invoiceNumber} - invoice may be locked or paid in MYOB";
                _logger.LogWarning("Skipping refund {RefundId} - cannot delete existing invoice {InvoiceNumber}", orderCredit.Id, invoiceNumber);
                return false;
            }

            // Create the service invoice
            var serviceInvoice = new ServiceInvoice
            {
                Number = invoiceNumber,
                CustomerPurchaseOrderNumber = InvoicerUtilities.TruncateString(
                    invoiceNumber,
                    InvoicerConstants.StringLimits.PurchaseOrderMaxLength),
                InvoiceDeliveryStatus = DocumentAction.PrintAndEmail,
                InvoiceType = InvoiceLayoutType.Service,
                Subtotal = orderCredit.Amount,
                TotalAmount = orderCredit.Amount,
                BalanceDueAmount = orderCredit.Amount,
                IsTaxInclusive = false,
                Date = orderCredit.DateCreated,
                JournalMemo = $"I2 {orderCredit.Customer?.Name}"
            };

            // Set customer
            serviceInvoice.Customer = GetCustomerLinkFromCustomer(orderCredit.Customer);

            // Create invoice line
            var invoiceLine = new ServiceInvoiceLine
            {
                Description = orderCredit.Description,
                Total = orderCredit.Amount,
                TaxCode = _gstTaxCodeLink ?? new TaxCodeLink { UID = Guid.NewGuid() }
            };

            // Set account if specified
            if (!string.IsNullOrEmpty(orderCredit.Account) && _accounts != null && _accounts.ContainsKey(orderCredit.Account))
            {
                var account = _accounts[orderCredit.Account];
                invoiceLine.Account = new AccountLink { UID = account.UID };
            }

            serviceInvoice.Lines = new List<ServiceInvoiceLine> { invoiceLine };

            // Create the invoice in MYOB
            var createdInvoice = await Task.Run(() =>
                _serviceInvoiceService.InsertEx(_companyFile, serviceInvoice, _oauthKeystore));

            // Add delay to respect API rate limits
            await Task.Delay(InvoicerConstants.Timing.ApiCallDelayMs);

            _logger.LogInformation("Refund invoice {InvoiceNumber} created successfully for refund {RefundId}",
                invoiceNumber, orderCredit.Id);

            return true;
        }
        catch (Exception ex)
        {
            // Store detailed error message for database logging
            _lastErrorMessage = FormatMYOBError(ex);
            _logger.LogError(ex, "Failed to create refund invoice for refund {RefundId}", orderCredit.Id);
            return false;
        }
    }



    public Task<bool> DeleteInvoiceAsync(string invoiceNumber)
    {
        try
        {
            DeleteInvoiceInternal(invoiceNumber);
            return Task.FromResult(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to delete invoice {InvoiceNumber}", invoiceNumber);
            return Task.FromResult(false);
        }
    }

    public AccountLink GetAccountLinkFromJob(IJob job)
    {
        try
        {
            if (_accounts == null)
                throw new InvalidOperationException("MYOB accounts not cached");

            var accountId = InvoicerUtilities.GetAccountDisplayIdFromJob(job);
            _logger.LogDebug("Getting account link for job {JobId}: {AccountId}", job.Id, accountId);

            if (_accounts.TryGetValue(accountId, out var account))
            {
                return new AccountLink { UID = account.UID };
            }

            _logger.LogWarning("Account not found in cached accounts: {AccountId}", accountId);
            // Return a placeholder
            return new AccountLink { UID = Guid.NewGuid() };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting account link for job {JobId}", job.Id);
            // Return a placeholder in case of error
            return new AccountLink { UID = Guid.NewGuid() };
        }
    }

    /// <summary>
    /// Delete existing invoice if it exists
    /// </summary>
    /// <returns>True if deletion succeeded or no invoice exists, False if deletion failed</returns>
    private bool DeleteInvoiceInternal(string invoiceNumber)
    {
        try
        {
            if (_serviceInvoiceService == null || _itemInvoiceService == null || _companyFile == null || _oauthKeystore == null)
                return true; // No service available, consider as success

            var filter = string.Format(InvoicerConstants.MYOBFilters.InvoiceByNumberFilter, invoiceNumber);
            var existingInvoices = _serviceInvoiceService.GetRange(_companyFile, filter, null);

            // If no existing invoices, deletion is successful
            if (!existingInvoices.Items.Any())
                return true;

            foreach (var invoice in existingInvoices.Items)
            {
                // Use ItemInvoiceService for deletion (matching LinqPad script)
                _itemInvoiceService.Delete(_companyFile, invoice.UID, _oauthKeystore);
                _logger.LogInformation("Deleted existing invoice {InvoiceNumber}", invoiceNumber);
            }

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to delete existing invoice {InvoiceNumber} - invoice may be locked/paid in MYOB", invoiceNumber);
            return false; // Deletion failed - invoice likely non-deletable
        }
    }

    /// <summary>
    /// Get customer link from order
    /// </summary>
    private CustomerLink GetCustomerLinkFromOrder(IOrder order)
    {
        try
        {
            if (_companyFile == null || _oauthKeystore == null)
                throw new InvalidOperationException("MYOB service not initialized");

            // Check if we already have the MYOB UID for this customer
            if (!string.IsNullOrEmpty(order.Customer.MyobUid))
            {
                return new CustomerLink { UID = Guid.Parse(order.Customer.MyobUid) };
            }

            // Look up customer by username
            if (_customerService == null)
                throw new InvalidOperationException("Customer service not initialized");

            var userName = System.Net.WebUtility.UrlEncode(order.Customer.Username);
            var custFilter = string.Format(InvoicerConstants.MYOBFilters.CustomerByDisplayIdFilter, userName);

            var customers = _customerService.GetRange(_companyFile, custFilter, null);
            var myobCustomer = customers.Items.FirstOrDefault();

            if (myobCustomer == null)
            {
                _logger.LogWarning("Customer not found in MYOB: {CustomerName} ({Username})",
                    order.Customer.Name, order.Customer.Username);

                // Create the customer in MYOB
                myobCustomer = CreateMYOBCustomerFromLEPCustomer(order.Customer);
                _logger.LogInformation("Created new MYOB customer: {CustomerName} ({Username})",
                    order.Customer.Name, order.Customer.Username);
            }

            return new CustomerLink { UID = myobCustomer.UID };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting customer link for order {OrderId}", order.Id);
            // Return a placeholder in case of error
            return new CustomerLink { UID = Guid.NewGuid() };
        }
    }

    /// <summary>
    /// Create invoice lines from order jobs
    /// </summary>
    private List<ServiceInvoiceLine> CreateInvoiceLinesFromOrder(IOrder order)
    {
        var lines = new List<ServiceInvoiceLine>();

        // Add job lines
        foreach (var job in order.Jobs)
        {
            var jobPrice = decimal.TryParse(job.Price, out var price) ? decimal.Round(price, 2) : 0m;
            var line = new ServiceInvoiceLine
            {
                Description = InvoicerUtilities.CreateJobDescription(job),
                Total = jobPrice,
                UnitPrice = jobPrice, // Required when UnitCount is provided
                Account = GetAccountLinkFromJob(job),
                TaxCode = _gstTaxCodeLink ?? new TaxCodeLink { UID = Guid.NewGuid() },
                UnitCount = 1
            };

            lines.Add(line);
        }

        // Add freight line if applicable
        var freightPrice = decimal.Round(order.PackDetail?.Price ?? 0m, 2);
        if (freightPrice > 0m && _freightAccountLink != null)
        {
            var freightLine = new ServiceInvoiceLine
            {
                Account = _freightAccountLink,
                Description = order.Courier ?? "Freight",
                Total = freightPrice,
                UnitPrice = freightPrice, // Required when UnitCount is provided
                TaxCode = _gstTaxCodeLink ?? new TaxCodeLink { UID = Guid.NewGuid() },
                UnitCount = 1
            };
            lines.Add(freightLine);
        }

        // Add promotion/discount line if applicable
        if (order.Promotion != null && order.PromotionBenefit > 0 && _discountsAccountLink != null)
        {
            var discountAmount = -1 * decimal.Round(order.PromotionBenefit, 2); // Negative for discount
            var promoLine = new ServiceInvoiceLine
            {
                Account = _discountsAccountLink,
                Description = $"{order.Promotion.PromotionCode} - {order.Promotion.ShortDescription}",
                Total = discountAmount,
                UnitPrice = discountAmount, // Required when UnitCount is provided
                TaxCode = _gstTaxCodeLink ?? new TaxCodeLink { UID = Guid.NewGuid() },
                UnitCount = 1
            };
            lines.Add(promoLine);
        }

        return lines;
    }

    /// <summary>
    /// Get GST tax code link
    /// </summary>
    private TaxCodeLink GetGstTaxCodeLink()
    {
        try
        {
            if (_companyFile == null || _oauthKeystore == null)
                throw new InvalidOperationException("MYOB service not initialized");

            var taxCodeService = new TaxCodeService(_apiConfiguration);
            var taxCodes = taxCodeService.GetRange(_companyFile, InvoicerConstants.MYOBFilters.GstTaxCodeFilter, null);
            var gstTaxCode = taxCodes.Items.FirstOrDefault();

            if (gstTaxCode == null)
            {
                _logger.LogWarning("GST tax code not found in MYOB");
                // Return a placeholder
                return new TaxCodeLink { UID = Guid.NewGuid() };
            }

            return new TaxCodeLink { UID = gstTaxCode.UID };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting GST tax code");
            // Return a placeholder in case of error
            return new TaxCodeLink { UID = Guid.NewGuid() };
        }
    }

    /// <summary>
    /// Initialize all MYOB services
    /// </summary>
    private void InitializeServices(ApiConfiguration apiConfiguration, ICompanyFileCredentials credentials, MYOB.AccountRight.SDK.IOAuthKeyService keystore)
    {
        _logger.LogDebug("Creating MYOB service instances");
        _serviceInvoiceService = new ServiceInvoiceService(apiConfiguration, null, keystore);
        _itemInvoiceService = new ItemInvoiceService(apiConfiguration, null, keystore);
        _customerService = new CustomerService(apiConfiguration, null, keystore);
        _accountService = new AccountService(apiConfiguration, null, keystore);
        _taxCodeService = new TaxCodeService(apiConfiguration, null, keystore);
        _employeeService = new EmployeeService(apiConfiguration, null, keystore);

        _logger.LogDebug("MYOB service instances created successfully. ServiceInvoiceService: {ServiceInvoiceService}, CustomerService: {CustomerService}",
            _serviceInvoiceService != null, _customerService != null);
    }

    /// <summary>
    /// Initialize cached MYOB entities (accounts, tax codes, etc.)
    /// </summary>
    private Task InitializeCachedEntities()
    {
        if (_companyFile == null || _accountService == null || _taxCodeService == null)
            return Task.CompletedTask;

        return Task.Run(() =>
        {
            try
            {
                // Cache all accounts
                var accounts = _accountService.GetRange(_companyFile, "", null);
                _accounts = accounts.Items.ToDictionary(a => a.DisplayID);
                _logger.LogInformation("Cached {AccountCount} accounts from MYOB", _accounts.Count);

                // Cache GST tax code
                var gstTaxCode = _taxCodeService.GetRange(_companyFile, InvoicerConstants.MYOBFilters.GstTaxCodeFilter, null);
                var gst = gstTaxCode.Items.FirstOrDefault();
                if (gst != null)
                {
                    _gstTaxCodeLink = new TaxCodeLink { UID = gst.UID };
                    _logger.LogInformation("Cached GST tax code from MYOB");
                }

                // Cache freight account by name (matching LinqPad script)
                var freightAccounts = _accountService.GetRange(_companyFile, InvoicerConstants.MYOBFilters.FreightAccountFilter, null);
                var freightAccount = freightAccounts.Items.FirstOrDefault();
                if (freightAccount != null)
                {
                    _freightAccountLink = new AccountLink { UID = freightAccount.UID };
                    _logger.LogInformation("Cached freight account from MYOB: {AccountName}", freightAccount.Name);
                }
                else
                {
                    _logger.LogWarning("Freight account 'Freight recovered' not found in MYOB");
                }

                // Add delay between API calls
                System.Threading.Thread.Sleep(InvoicerConstants.Timing.ApiCallDelayMs);

                // Cache discounts account by name (matching LinqPad script)
                var discountAccounts = _accountService.GetRange(_companyFile, InvoicerConstants.MYOBFilters.DiscountsAccountFilter, null);
                var discountsAccount = discountAccounts.Items.FirstOrDefault();
                if (discountsAccount != null)
                {
                    _discountsAccountLink = new AccountLink { UID = discountsAccount.UID };
                    _logger.LogInformation("Cached discounts account from MYOB: {AccountName}", discountsAccount.Name);
                }
                else
                {
                    _logger.LogWarning("Discounts account 'Discounts Allowed' not found in MYOB");
                }

                // Add delay to respect API limits
                System.Threading.Thread.Sleep(InvoicerConstants.Timing.ApiCallDelayMs);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to initialize cached MYOB entities");
                throw;
            }
        });
    }

    /// <summary>
    /// Create a new MYOB customer from LEP customer data
    /// </summary>
    private Customer CreateMYOBCustomerFromLEPCustomer(ICustomerUser lepCustomer)
    {
        if (_customerService == null || _companyFile == null)
            throw new InvalidOperationException("MYOB service not initialized");

        try
        {
            var myobCustomer = new Customer
            {
                CompanyName = lepCustomer.Name,
                DisplayID = lepCustomer.Username
            };

            // Set up addresses
            var addresses = new List<Address>();

            // Billing address
            var billingAddress = new Address
            {
                Country = lepCustomer.BillingAddress.Country,
                PostCode = lepCustomer.BillingAddress.Postcode,
                State = lepCustomer.BillingAddress.State,
                City = lepCustomer.BillingAddress.City,
                Street = string.Join(", ", new[] {
                    lepCustomer.BillingAddress.Address1,
                    lepCustomer.BillingAddress.Address2,
                    lepCustomer.BillingAddress.Address3
                }.Where(s => !string.IsNullOrEmpty(s))),
                Location = 1 // Billing
            };
            addresses.Add(billingAddress);

            // Shipping address
            var shippingAddress = new Address
            {
                Country = lepCustomer.PostalAddress.Country,
                PostCode = lepCustomer.PostalAddress.Postcode,
                State = lepCustomer.PostalAddress.State,
                City = lepCustomer.PostalAddress.City,
                Street = string.Join(", ", new[] {
                    lepCustomer.PostalAddress.Address1,
                    lepCustomer.PostalAddress.Address2,
                    lepCustomer.PostalAddress.Address3
                }.Where(s => !string.IsNullOrEmpty(s))),
                Location = 2 // Shipping
            };
            addresses.Add(shippingAddress);

            myobCustomer.Addresses = addresses;

            // Set custom fields
            myobCustomer.CustomList2 = new Identifier { Label = "Customer Status", Value = lepCustomer.CustomerStatus };
            myobCustomer.CustomList3 = new Identifier { Label = "Terms", Value = lepCustomer.PaymentTerms.ToString() == "COD" ? "COD/PREPAID" : "ACCOUNT" };

            // Set selling details
            var customerSellingDetails = new CustomerSellingDetails
            {
                TaxCode = _gstTaxCodeLink,
                FreightTaxCode = _gstTaxCodeLink
            };

            // Set sales person if available
            if (!string.IsNullOrEmpty(lepCustomer.SalesConsultant) && _employeeService != null)
            {
                try
                {
                    // This would require a database query to get the sales consultant ID
                    // For now, we'll skip this part as it requires database access
                    _logger.LogDebug("Sales consultant mapping not implemented: {SalesConsultant}", lepCustomer.SalesConsultant);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to set sales person for customer {CustomerName}", lepCustomer.Name);
                }
            }

            myobCustomer.SellingDetails = customerSellingDetails;

            // Create the customer in MYOB
            var result = _customerService.InsertEx(_companyFile, myobCustomer, null);

            // Add delay to respect API limits
            System.Threading.Thread.Sleep(InvoicerConstants.Timing.ApiCallDelayMs);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create MYOB customer for {CustomerName}", lepCustomer.Name);
            throw;
        }
    }

    /// <summary>
    /// Delete existing invoice with the same number if it exists
    /// </summary>
    /// <param name="invoiceNumber">Invoice number to check and delete</param>
    /// <returns>True if deletion succeeded or no invoice exists, False if deletion failed</returns>
    private async Task<bool> DeleteExistingInvoice(string invoiceNumber)
    {
        try
        {
            var filter = string.Format(InvoicerConstants.MYOBFilters.InvoiceByNumberFilter, invoiceNumber);
            var existingInvoices = await Task.Run(() =>
                _serviceInvoiceService.GetRange(_companyFile, filter, null));

            // If no existing invoices, deletion is successful
            if (!existingInvoices.Items.Any())
                return true;

            foreach (var existingInvoice in existingInvoices.Items)
            {
                _logger.LogInformation("Deleting existing invoice {InvoiceNumber}", existingInvoice.Number);
                await Task.Run(() =>
                    _itemInvoiceService.Delete(_companyFile, existingInvoice.UID, _oauthKeystore));

                // Add delay between API calls
                await Task.Delay(InvoicerConstants.Timing.ApiCallDelayMs);
            }

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to delete existing invoice {InvoiceNumber} - invoice may be locked/paid in MYOB", invoiceNumber);
            return false; // Deletion failed - invoice likely non-deletable
        }
    }

    /// <summary>
    /// Get customer link from order credit
    /// </summary>
    private CustomerLink GetCustomerLinkFromOrderCredit(OrderCredit orderCredit)
    {
        if (orderCredit.Order?.Customer != null)
        {
            return GetCustomerLinkFromCustomer(orderCredit.Order.Customer);
        }

        throw new InvalidOperationException($"OrderCredit {orderCredit.Id} has no associated customer");
    }

    /// <summary>
    /// Get customer link from customer
    /// </summary>
    private CustomerLink GetCustomerLinkFromCustomer(ICustomerUser customer)
    {
        try
        {
            if (_companyFile == null || _oauthKeystore == null)
                throw new InvalidOperationException("MYOB service not initialized");

            // Check if we already have the MYOB UID for this customer
            if (!string.IsNullOrEmpty(customer.MyobUid))
            {
                return new CustomerLink { UID = Guid.Parse(customer.MyobUid) };
            }

            // Look up customer by username
            if (_customerService == null)
                throw new InvalidOperationException("Customer service not initialized");

            var userName = System.Net.WebUtility.UrlEncode(customer.Username);
            var custFilter = string.Format(InvoicerConstants.MYOBFilters.CustomerByDisplayIdFilter, userName);

            var customers = _customerService.GetRange(_companyFile, custFilter, null);
            var myobCustomer = customers.Items.FirstOrDefault();

            if (myobCustomer == null)
            {
                _logger.LogWarning("Customer not found in MYOB: {CustomerName} ({Username})",
                    customer.Name, customer.Username);

                // Create the customer in MYOB
                myobCustomer = CreateMYOBCustomerFromLEPCustomer(customer);
                _logger.LogInformation("Created new MYOB customer: {CustomerName} ({Username})",
                    customer.Name, customer.Username);
            }

            return new CustomerLink { UID = myobCustomer.UID };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting customer link for customer {CustomerName}", customer.Name);
            // Return a placeholder in case of error
            return new CustomerLink { UID = Guid.NewGuid() };
        }
    }

    /// <summary>
    /// Format MYOB exception into a readable error message for database logging
    /// </summary>
    private string FormatMYOBError(Exception ex)
    {
        try
        {
            // Check if it's a MYOB API validation exception
            if (ex.GetType().Name.Contains("ApiValidationException"))
            {
                var message = ex.Message;

                // Try to extract validation errors if available
                var errorsProperty = ex.GetType().GetProperty("Errors");
                if (errorsProperty != null)
                {
                    var errors = errorsProperty.GetValue(ex);
                    if (errors != null)
                    {
                        // This would need to be adapted based on the actual MYOB SDK error structure
                        message += " | Validation Errors: " + errors.ToString();
                    }
                }

                return $"MYOB API Validation Error: {message}";
            }

            // For other exceptions, return the basic message
            return $"MYOB Error: {ex.Message}";
        }
        catch
        {
            // Fallback if error formatting fails
            return $"MYOB Error: {ex.Message}";
        }
    }
}
