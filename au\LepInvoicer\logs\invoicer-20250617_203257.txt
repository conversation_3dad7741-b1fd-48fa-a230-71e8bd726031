[2025-06-17 20:32:57.625 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 20:32:57.657 +10:00 INF] Initializing FastReport...
[2025-06-17 20:32:57.738 +10:00 INF] FastReport initialized successfully
[2025-06-17 20:32:58.303 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 20:32:59.665 +10:00 INF] Starting LEP Invoicer at "2025-06-17T20:32:59.6650595+10:00"
[2025-06-17 20:32:59.671 +10:00 INF] Initializing database service...
[2025-06-17 20:32:59.674 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 20:32:59.775 +10:00 INF] Database connection established successfully
[2025-06-17 20:32:59.777 +10:00 INF] Database service initialized successfully
[2025-06-17 20:32:59.779 +10:00 INF] Checking for pending work...
[2025-06-17 20:32:59.782 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 20:33:00.746 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 20:33:00.749 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 20:33:00.762 +10:00 INF] Found 0 credits to invoice
[2025-06-17 20:33:00.764 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 20:33:00.768 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 20:33:00.771 +10:00 INF] No pending work found
[2025-06-17 20:33:00.772 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 20:33:00.773 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 20:33:00.848 +10:00 INF] LEP Invoicer completed successfully in 1183ms. No work to process.
[2025-06-17 20:33:00.854 +10:00 INF] Database connection disposed
[2025-06-17 20:33:00.856 +10:00 INF] LEP Invoicer completed with result: 0
