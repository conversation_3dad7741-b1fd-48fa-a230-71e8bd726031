[2025-06-17 19:29:34.407 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 19:29:34.435 +10:00 INF] Initializing FastReport...
[2025-06-17 19:29:34.507 +10:00 INF] FastReport initialized successfully
[2025-06-17 19:29:34.931 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 19:29:36.206 +10:00 INF] Starting LEP Invoicer at "2025-06-17T19:29:36.2066106+10:00"
[2025-06-17 19:29:36.210 +10:00 INF] Initializing database service...
[2025-06-17 19:29:36.213 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 19:29:36.310 +10:00 INF] Database connection established successfully
[2025-06-17 19:29:36.312 +10:00 INF] Database service initialized successfully
[2025-06-17 19:29:36.314 +10:00 INF] Checking for pending work...
[2025-06-17 19:29:36.317 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 19:29:37.200 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 19:29:37.208 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 19:29:37.222 +10:00 INF] Found 0 credits to invoice
[2025-06-17 19:29:37.224 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 19:29:37.237 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 19:29:37.238 +10:00 INF] No pending work found
[2025-06-17 19:29:37.240 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 19:29:37.241 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 19:29:37.319 +10:00 INF] LEP Invoicer completed successfully in 1113ms. No work to process.
[2025-06-17 19:29:37.326 +10:00 INF] Database connection disposed
[2025-06-17 19:29:37.328 +10:00 INF] LEP Invoicer completed with result: 0
