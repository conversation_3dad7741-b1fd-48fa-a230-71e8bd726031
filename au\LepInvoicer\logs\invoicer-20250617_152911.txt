[2025-06-17 15:29:11.630 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 15:29:11.662 +10:00 INF] Initializing FastReport...
[2025-06-17 15:29:11.741 +10:00 INF] FastReport initialized successfully
[2025-06-17 15:29:12.158 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 15:29:13.404 +10:00 INF] Starting LEP Invoicer at "2025-06-17T15:29:13.4039359+10:00"
[2025-06-17 15:29:13.407 +10:00 INF] Initializing database service...
[2025-06-17 15:29:13.410 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 15:29:13.513 +10:00 INF] Database connection established successfully
[2025-06-17 15:29:13.514 +10:00 INF] Database service initialized successfully
[2025-06-17 15:29:13.516 +10:00 INF] Checking for pending work...
[2025-06-17 15:29:13.519 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 15:29:14.431 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 15:29:14.435 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 15:29:14.450 +10:00 INF] Found 0 credits to invoice
[2025-06-17 15:29:14.452 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 15:29:14.456 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 15:29:14.458 +10:00 INF] No pending work found
[2025-06-17 15:29:14.459 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 15:29:14.462 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 15:29:14.540 +10:00 INF] LEP Invoicer completed successfully in 1136ms. No work to process.
[2025-06-17 15:29:14.552 +10:00 INF] Database connection disposed
[2025-06-17 15:29:14.554 +10:00 INF] LEP Invoicer completed with result: 0
