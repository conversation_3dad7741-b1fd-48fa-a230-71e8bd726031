[2025-06-17 18:33:53.737 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 18:33:53.764 +10:00 INF] Initializing FastReport...
[2025-06-17 18:33:53.860 +10:00 INF] FastReport initialized successfully
[2025-06-17 18:33:54.572 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 18:33:55.820 +10:00 INF] Starting LEP Invoicer at "2025-06-17T18:33:55.8202205+10:00"
[2025-06-17 18:33:55.823 +10:00 INF] Initializing database service...
[2025-06-17 18:33:55.826 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 18:33:55.929 +10:00 INF] Database connection established successfully
[2025-06-17 18:33:55.934 +10:00 INF] Database service initialized successfully
[2025-06-17 18:33:55.937 +10:00 INF] Checking for pending work...
[2025-06-17 18:33:55.940 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 18:33:56.909 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 18:33:56.912 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 18:33:56.925 +10:00 INF] Found 0 credits to invoice
[2025-06-17 18:33:56.928 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 18:33:56.933 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 18:33:56.934 +10:00 INF] No pending work found
[2025-06-17 18:33:56.935 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 18:33:56.959 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 18:33:57.032 +10:00 INF] LEP Invoicer completed successfully in 1211ms. No work to process.
[2025-06-17 18:33:57.038 +10:00 INF] Database connection disposed
[2025-06-17 18:33:57.040 +10:00 INF] LEP Invoicer completed with result: 0
