[2025-06-17 15:45:40.928 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 15:45:40.967 +10:00 INF] Initializing FastReport...
[2025-06-17 15:45:41.052 +10:00 INF] FastReport initialized successfully
[2025-06-17 15:45:41.585 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 15:45:43.098 +10:00 INF] Starting LEP Invoicer at "2025-06-17T15:45:43.0982327+10:00"
[2025-06-17 15:45:43.102 +10:00 INF] Initializing database service...
[2025-06-17 15:45:43.107 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 15:45:43.244 +10:00 INF] Database connection established successfully
[2025-06-17 15:45:43.245 +10:00 INF] Database service initialized successfully
[2025-06-17 15:45:43.247 +10:00 INF] Checking for pending work...
[2025-06-17 15:45:43.250 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 15:45:44.154 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 15:45:44.156 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 15:45:44.169 +10:00 INF] Found 0 credits to invoice
[2025-06-17 15:45:44.171 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 15:45:44.174 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 15:45:44.175 +10:00 INF] No pending work found
[2025-06-17 15:45:44.177 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 15:45:44.178 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 15:45:44.254 +10:00 INF] LEP Invoicer completed successfully in 1156ms. No work to process.
[2025-06-17 15:45:44.261 +10:00 INF] Database connection disposed
[2025-06-17 15:45:44.264 +10:00 INF] LEP Invoicer completed with result: 0
