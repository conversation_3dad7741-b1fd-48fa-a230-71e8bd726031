[2025-06-17 18:41:33.762 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 18:41:33.801 +10:00 INF] Initializing FastReport...
[2025-06-17 18:41:33.903 +10:00 INF] FastReport initialized successfully
[2025-06-17 18:41:34.394 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 18:41:35.743 +10:00 INF] Starting LEP Invoicer at "2025-06-17T18:41:35.7434991+10:00"
[2025-06-17 18:41:35.747 +10:00 INF] Initializing database service...
[2025-06-17 18:41:35.750 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 18:41:35.856 +10:00 INF] Database connection established successfully
[2025-06-17 18:41:35.857 +10:00 INF] Database service initialized successfully
[2025-06-17 18:41:35.860 +10:00 INF] Checking for pending work...
[2025-06-17 18:41:35.863 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 18:41:36.746 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 18:41:36.749 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 18:41:36.761 +10:00 INF] Found 0 credits to invoice
[2025-06-17 18:41:36.763 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 18:41:36.767 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 18:41:36.768 +10:00 INF] No pending work found
[2025-06-17 18:41:36.770 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 18:41:36.771 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 18:41:36.843 +10:00 INF] LEP Invoicer completed successfully in 1099ms. No work to process.
[2025-06-17 18:41:36.850 +10:00 INF] Database connection disposed
[2025-06-17 18:41:36.852 +10:00 INF] LEP Invoicer completed with result: 0
