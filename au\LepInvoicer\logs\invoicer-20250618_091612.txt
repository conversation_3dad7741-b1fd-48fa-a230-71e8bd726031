[2025-06-18 09:16:12.717 +10:00 INF] Starting LEP Invoicer application
[2025-06-18 09:16:12.752 +10:00 INF] Initializing FastReport...
[2025-06-18 09:16:12.827 +10:00 INF] FastReport initialized successfully
[2025-06-18 09:16:13.264 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-18 09:16:14.505 +10:00 INF] Starting LEP Invoicer at "2025-06-18T09:16:14.5054396+10:00"
[2025-06-18 09:16:14.509 +10:00 INF] Initializing database service...
[2025-06-18 09:16:14.512 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-18 09:16:14.611 +10:00 INF] Database connection established successfully
[2025-06-18 09:16:14.612 +10:00 INF] Database service initialized successfully
[2025-06-18 09:16:14.615 +10:00 INF] Checking for pending work...
[2025-06-18 09:16:14.618 +10:00 INF] Getting 500 orders to invoice
[2025-06-18 09:16:15.503 +10:00 INF] Found 0 orders to invoice (filtered 16 candidates)
[2025-06-18 09:16:15.506 +10:00 INF] Getting 100 credits to invoice
[2025-06-18 09:16:15.518 +10:00 INF] Found 0 credits to invoice
[2025-06-18 09:16:15.520 +10:00 INF] Getting 100 refunds to invoice
[2025-06-18 09:16:15.524 +10:00 INF] Found 0 refunds to invoice
[2025-06-18 09:16:15.525 +10:00 INF] No pending work found
[2025-06-18 09:16:15.526 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-18 09:16:15.527 +10:00 INF] Cleaning up invoicer logs...
[2025-06-18 09:16:15.603 +10:00 INF] LEP Invoicer completed successfully in 1097ms. No work to process.
[2025-06-18 09:16:15.609 +10:00 INF] Database connection disposed
[2025-06-18 09:16:15.611 +10:00 INF] LEP Invoicer completed with result: 0
