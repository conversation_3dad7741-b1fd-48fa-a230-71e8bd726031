[2025-06-17 16:12:00.902 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 16:12:00.930 +10:00 INF] Initializing FastReport...
[2025-06-17 16:12:01.003 +10:00 INF] FastReport initialized successfully
[2025-06-17 16:12:01.537 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 16:12:03.387 +10:00 INF] Starting LEP Invoicer at "2025-06-17T16:12:03.3873054+10:00"
[2025-06-17 16:12:03.392 +10:00 INF] Initializing database service...
[2025-06-17 16:12:03.397 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 16:12:03.529 +10:00 INF] Database connection established successfully
[2025-06-17 16:12:03.530 +10:00 INF] Database service initialized successfully
[2025-06-17 16:12:03.533 +10:00 INF] Checking for pending work...
[2025-06-17 16:12:03.543 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 16:12:04.569 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 16:12:04.573 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 16:12:04.586 +10:00 INF] Found 0 credits to invoice
[2025-06-17 16:12:04.588 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 16:12:04.593 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 16:12:04.595 +10:00 INF] No pending work found
[2025-06-17 16:12:04.596 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 16:12:04.598 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 16:12:04.675 +10:00 INF] LEP Invoicer completed successfully in 1288ms. No work to process.
[2025-06-17 16:12:04.681 +10:00 INF] Database connection disposed
[2025-06-17 16:12:04.683 +10:00 INF] LEP Invoicer completed with result: 0
