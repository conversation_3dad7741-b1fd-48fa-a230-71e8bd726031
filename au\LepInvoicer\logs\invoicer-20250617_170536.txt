[2025-06-17 17:05:36.603 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 17:05:36.631 +10:00 INF] Initializing FastReport...
[2025-06-17 17:05:36.708 +10:00 INF] FastReport initialized successfully
[2025-06-17 17:05:37.129 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 17:05:38.390 +10:00 INF] Starting LEP Invoicer at "2025-06-17T17:05:38.3897415+10:00"
[2025-06-17 17:05:38.393 +10:00 INF] Initializing database service...
[2025-06-17 17:05:38.396 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 17:05:38.520 +10:00 INF] Database connection established successfully
[2025-06-17 17:05:38.526 +10:00 INF] Database service initialized successfully
[2025-06-17 17:05:38.528 +10:00 INF] Checking for pending work...
[2025-06-17 17:05:38.540 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 17:05:39.393 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 17:05:39.396 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 17:05:39.409 +10:00 INF] Found 0 credits to invoice
[2025-06-17 17:05:39.411 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 17:05:39.415 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 17:05:39.416 +10:00 INF] No pending work found
[2025-06-17 17:05:39.418 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 17:05:39.419 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 17:05:39.494 +10:00 INF] LEP Invoicer completed successfully in 1104ms. No work to process.
[2025-06-17 17:05:39.500 +10:00 INF] Database connection disposed
[2025-06-17 17:05:39.502 +10:00 INF] LEP Invoicer completed with result: 0
