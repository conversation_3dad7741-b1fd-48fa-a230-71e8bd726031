[2025-06-18 07:54:29.625 +10:00 INF] Starting LEP Invoicer application
[2025-06-18 07:54:29.653 +10:00 INF] Initializing FastReport...
[2025-06-18 07:54:29.730 +10:00 INF] FastReport initialized successfully
[2025-06-18 07:54:30.157 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-18 07:54:31.428 +10:00 INF] Starting LEP Invoicer at "2025-06-18T07:54:31.4279777+10:00"
[2025-06-18 07:54:31.434 +10:00 INF] Initializing database service...
[2025-06-18 07:54:31.436 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-18 07:54:31.542 +10:00 INF] Database connection established successfully
[2025-06-18 07:54:31.545 +10:00 INF] Database service initialized successfully
[2025-06-18 07:54:31.549 +10:00 INF] Checking for pending work...
[2025-06-18 07:54:31.552 +10:00 INF] Getting 500 orders to invoice
[2025-06-18 07:54:32.476 +10:00 INF] Found 0 orders to invoice (filtered 16 candidates)
[2025-06-18 07:54:32.478 +10:00 INF] Getting 100 credits to invoice
[2025-06-18 07:54:32.496 +10:00 INF] Found 0 credits to invoice
[2025-06-18 07:54:32.498 +10:00 INF] Getting 100 refunds to invoice
[2025-06-18 07:54:32.501 +10:00 INF] Found 0 refunds to invoice
[2025-06-18 07:54:32.502 +10:00 INF] No pending work found
[2025-06-18 07:54:32.504 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-18 07:54:32.507 +10:00 INF] Cleaning up invoicer logs...
[2025-06-18 07:54:32.580 +10:00 INF] LEP Invoicer completed successfully in 1152ms. No work to process.
[2025-06-18 07:54:32.598 +10:00 INF] Database connection disposed
[2025-06-18 07:54:32.600 +10:00 INF] LEP Invoicer completed with result: 0
