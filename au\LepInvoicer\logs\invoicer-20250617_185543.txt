[2025-06-17 18:55:43.464 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 18:55:43.491 +10:00 INF] Initializing FastReport...
[2025-06-17 18:55:43.570 +10:00 INF] FastReport initialized successfully
[2025-06-17 18:55:43.999 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 18:55:45.363 +10:00 INF] Starting LEP Invoicer at "2025-06-17T18:55:45.3634992+10:00"
[2025-06-17 18:55:45.367 +10:00 INF] Initializing database service...
[2025-06-17 18:55:45.370 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 18:55:45.474 +10:00 INF] Database connection established successfully
[2025-06-17 18:55:45.476 +10:00 INF] Database service initialized successfully
[2025-06-17 18:55:45.479 +10:00 INF] Checking for pending work...
[2025-06-17 18:55:45.482 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 18:55:46.397 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 18:55:46.407 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 18:55:46.421 +10:00 INF] Found 0 credits to invoice
[2025-06-17 18:55:46.423 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 18:55:46.427 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 18:55:46.430 +10:00 INF] No pending work found
[2025-06-17 18:55:46.432 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 18:55:46.435 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 18:55:46.519 +10:00 INF] LEP Invoicer completed successfully in 1156ms. No work to process.
[2025-06-17 18:55:46.526 +10:00 INF] Database connection disposed
[2025-06-17 18:55:46.531 +10:00 INF] LEP Invoicer completed with result: 0
