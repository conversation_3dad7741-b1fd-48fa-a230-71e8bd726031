[2025-06-18 10:52:16.599 +10:00 INF] Starting LEP Invoicer application
[2025-06-18 10:52:16.627 +10:00 INF] Initializing FastReport...
[2025-06-18 10:52:16.700 +10:00 INF] FastReport initialized successfully
[2025-06-18 10:52:17.143 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-18 10:52:18.967 +10:00 INF] Starting LEP Invoicer at "2025-06-18T10:52:18.9604465+10:00"
[2025-06-18 10:52:18.974 +10:00 INF] Initializing database service...
[2025-06-18 10:52:18.977 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-18 10:52:19.106 +10:00 INF] Database connection established successfully
[2025-06-18 10:52:19.109 +10:00 INF] Database service initialized successfully
[2025-06-18 10:52:19.111 +10:00 INF] Checking for pending work...
[2025-06-18 10:52:19.115 +10:00 INF] Getting 500 orders to invoice
[2025-06-18 10:52:19.879 +10:00 INF] Found 0 orders to invoice (marked 0 zero-priced as invoiced, filtered 0 candidates)
[2025-06-18 10:52:19.894 +10:00 INF] Getting 100 credits to invoice
[2025-06-18 10:52:19.915 +10:00 INF] Found 0 credits to invoice
[2025-06-18 10:52:19.917 +10:00 INF] Getting 100 refunds to invoice
[2025-06-18 10:52:19.922 +10:00 INF] Found 0 refunds to invoice
[2025-06-18 10:52:19.923 +10:00 INF] No pending work found
[2025-06-18 10:52:19.929 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-18 10:52:19.931 +10:00 INF] Cleaning up invoicer logs...
[2025-06-18 10:52:20.021 +10:00 INF] LEP Invoicer completed successfully in 1061ms. No work to process.
[2025-06-18 10:52:20.028 +10:00 INF] Database connection disposed
[2025-06-18 10:52:20.031 +10:00 INF] LEP Invoicer completed with result: 0
