[2025-06-18 09:02:44.684 +10:00 INF] Starting LEP Invoicer application
[2025-06-18 09:02:44.711 +10:00 INF] Initializing FastReport...
[2025-06-18 09:02:44.783 +10:00 INF] FastReport initialized successfully
[2025-06-18 09:02:45.242 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-18 09:02:46.461 +10:00 INF] Starting LEP Invoicer at "2025-06-18T09:02:46.4609163+10:00"
[2025-06-18 09:02:46.464 +10:00 INF] Initializing database service...
[2025-06-18 09:02:46.468 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-18 09:02:46.574 +10:00 INF] Database connection established successfully
[2025-06-18 09:02:46.575 +10:00 INF] Database service initialized successfully
[2025-06-18 09:02:46.578 +10:00 INF] Checking for pending work...
[2025-06-18 09:02:46.581 +10:00 INF] Getting 500 orders to invoice
[2025-06-18 09:02:47.462 +10:00 INF] Found 0 orders to invoice (filtered 16 candidates)
[2025-06-18 09:02:47.466 +10:00 INF] Getting 100 credits to invoice
[2025-06-18 09:02:47.479 +10:00 INF] Found 0 credits to invoice
[2025-06-18 09:02:47.483 +10:00 INF] Getting 100 refunds to invoice
[2025-06-18 09:02:47.488 +10:00 INF] Found 0 refunds to invoice
[2025-06-18 09:02:47.491 +10:00 INF] No pending work found
[2025-06-18 09:02:47.497 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-18 09:02:47.499 +10:00 INF] Cleaning up invoicer logs...
[2025-06-18 09:02:47.606 +10:00 INF] LEP Invoicer completed successfully in 1145ms. No work to process.
[2025-06-18 09:02:47.613 +10:00 INF] Database connection disposed
[2025-06-18 09:02:47.619 +10:00 INF] LEP Invoicer completed with result: 0
