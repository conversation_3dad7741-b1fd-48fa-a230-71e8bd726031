[2025-06-18 09:08:18.811 +10:00 INF] Starting LEP Invoicer application
[2025-06-18 09:08:18.846 +10:00 INF] Initializing FastReport...
[2025-06-18 09:08:18.926 +10:00 INF] FastReport initialized successfully
[2025-06-18 09:08:19.403 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-18 09:08:20.807 +10:00 INF] Starting LEP Invoicer at "2025-06-18T09:08:20.8072565+10:00"
[2025-06-18 09:08:20.811 +10:00 INF] Initializing database service...
[2025-06-18 09:08:20.814 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-18 09:08:20.916 +10:00 INF] Database connection established successfully
[2025-06-18 09:08:20.924 +10:00 INF] Database service initialized successfully
[2025-06-18 09:08:20.928 +10:00 INF] Checking for pending work...
[2025-06-18 09:08:20.936 +10:00 INF] Getting 500 orders to invoice
[2025-06-18 09:08:21.867 +10:00 INF] Found 0 orders to invoice (filtered 16 candidates)
[2025-06-18 09:08:21.869 +10:00 INF] Getting 100 credits to invoice
[2025-06-18 09:08:21.883 +10:00 INF] Found 0 credits to invoice
[2025-06-18 09:08:21.884 +10:00 INF] Getting 100 refunds to invoice
[2025-06-18 09:08:21.888 +10:00 INF] Found 0 refunds to invoice
[2025-06-18 09:08:21.891 +10:00 INF] No pending work found
[2025-06-18 09:08:21.892 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-18 09:08:21.894 +10:00 INF] Cleaning up invoicer logs...
[2025-06-18 09:08:21.985 +10:00 INF] LEP Invoicer completed successfully in 1178ms. No work to process.
[2025-06-18 09:08:21.999 +10:00 INF] Database connection disposed
[2025-06-18 09:08:22.001 +10:00 INF] LEP Invoicer completed with result: 0
