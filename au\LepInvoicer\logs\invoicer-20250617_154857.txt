[2025-06-17 15:48:57.490 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 15:48:57.517 +10:00 INF] Initializing FastReport...
[2025-06-17 15:48:57.608 +10:00 INF] FastReport initialized successfully
[2025-06-17 15:48:58.074 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 15:48:59.371 +10:00 INF] Starting LEP Invoicer at "2025-06-17T15:48:59.3715982+10:00"
[2025-06-17 15:48:59.376 +10:00 INF] Initializing database service...
[2025-06-17 15:48:59.379 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 15:48:59.502 +10:00 INF] Database connection established successfully
[2025-06-17 15:48:59.518 +10:00 INF] Database service initialized successfully
[2025-06-17 15:48:59.540 +10:00 INF] Checking for pending work...
[2025-06-17 15:48:59.545 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 15:49:00.576 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 15:49:00.587 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 15:49:00.611 +10:00 INF] Found 0 credits to invoice
[2025-06-17 15:49:00.613 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 15:49:00.617 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 15:49:00.629 +10:00 INF] No pending work found
[2025-06-17 15:49:00.630 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 15:49:00.632 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 15:49:00.713 +10:00 INF] LEP Invoicer completed successfully in 1342ms. No work to process.
[2025-06-17 15:49:00.720 +10:00 INF] Database connection disposed
[2025-06-17 15:49:00.722 +10:00 INF] LEP Invoicer completed with result: 0
