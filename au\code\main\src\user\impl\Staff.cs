#region using

using lep.job;
using System;

#endregion using

namespace lep.user.impl
{
	[Serializable]
    public class Staff : BaseUser, IStaff
    {
        public static IStaff SYSTEM = new Staff() { Id = 1 };
        private Role role = Role.PrepressFinishing;

        public Staff()
        {
            // CreateOrderSearchCriteria();
            //  CreatRunSearchCriteria();
        }

        public void CreateOrderSearchCriteria()
        {
            if (OrderSearchCriteria == null)
            {
                OrderSearchCriteria = new OrderSearchCriteria();
                OrderSearchCriteria.Staff = this;
            }
        }

        public void CreatRunSearchCriteria()
        {
            if (RunSearchCriteria == null)
            {
                RunSearchCriteria = new RunSearchCriteria();
                RunSearchCriteria.Staff = this;
                RunSearchCriteria.IsSearchPanelOpen = true;
                RunSearchCriteria.Customer = "";
                RunSearchCriteria.OrderNr = "";
                RunSearchCriteria.JobNr = "";
                RunSearchCriteria.Facility = Facility.FG;
            }
        }

        #region IStaff Members

        public virtual Role Role
        {
            get { return role; }
            set { role = value; }
        }

        public virtual string Barcode { get; set; }
        public virtual string IPAddress { get; set; }

        public virtual OrderSearchCriteria OrderSearchCriteria { get; set; }

        public virtual RunSearchCriteria RunSearchCriteria { get; set; }

        #endregion IStaff Members
    }
}