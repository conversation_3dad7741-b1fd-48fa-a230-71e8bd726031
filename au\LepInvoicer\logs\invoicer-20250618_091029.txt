[2025-06-18 09:10:29.691 +10:00 INF] Starting LEP Invoicer application
[2025-06-18 09:10:29.731 +10:00 INF] Initializing FastReport...
[2025-06-18 09:10:29.808 +10:00 INF] FastReport initialized successfully
[2025-06-18 09:10:30.247 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-18 09:10:31.687 +10:00 INF] Starting LEP Invoicer at "2025-06-18T09:10:31.6869533+10:00"
[2025-06-18 09:10:31.692 +10:00 INF] Initializing database service...
[2025-06-18 09:10:31.695 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-18 09:10:31.882 +10:00 INF] Database connection established successfully
[2025-06-18 09:10:31.884 +10:00 INF] Database service initialized successfully
[2025-06-18 09:10:31.887 +10:00 INF] Checking for pending work...
[2025-06-18 09:10:31.891 +10:00 INF] Getting 500 orders to invoice
[2025-06-18 09:10:33.273 +10:00 INF] Found 0 orders to invoice (filtered 16 candidates)
[2025-06-18 09:10:33.276 +10:00 INF] Getting 100 credits to invoice
[2025-06-18 09:10:33.294 +10:00 INF] Found 0 credits to invoice
[2025-06-18 09:10:33.296 +10:00 INF] Getting 100 refunds to invoice
[2025-06-18 09:10:33.301 +10:00 INF] Found 0 refunds to invoice
[2025-06-18 09:10:33.314 +10:00 INF] No pending work found
[2025-06-18 09:10:33.325 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-18 09:10:33.327 +10:00 INF] Cleaning up invoicer logs...
[2025-06-18 09:10:33.458 +10:00 INF] LEP Invoicer completed successfully in 1771ms. No work to process.
[2025-06-18 09:10:33.464 +10:00 INF] Database connection disposed
[2025-06-18 09:10:33.466 +10:00 INF] LEP Invoicer completed with result: 0
