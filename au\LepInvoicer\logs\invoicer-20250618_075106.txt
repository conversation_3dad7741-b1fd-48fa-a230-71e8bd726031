[2025-06-18 07:51:06.678 +10:00 INF] Starting LEP Invoicer application
[2025-06-18 07:51:06.709 +10:00 INF] Initializing FastReport...
[2025-06-18 07:51:06.798 +10:00 INF] FastReport initialized successfully
[2025-06-18 07:51:07.451 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-18 07:51:08.769 +10:00 INF] Starting LEP Invoicer at "2025-06-18T07:51:08.7689238+10:00"
[2025-06-18 07:51:08.772 +10:00 INF] Initializing database service...
[2025-06-18 07:51:08.775 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-18 07:51:08.887 +10:00 INF] Database connection established successfully
[2025-06-18 07:51:08.889 +10:00 INF] Database service initialized successfully
[2025-06-18 07:51:08.892 +10:00 INF] Checking for pending work...
[2025-06-18 07:51:08.895 +10:00 INF] Getting 500 orders to invoice
[2025-06-18 07:51:09.788 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-18 07:51:09.791 +10:00 INF] Getting 100 credits to invoice
[2025-06-18 07:51:09.804 +10:00 INF] Found 0 credits to invoice
[2025-06-18 07:51:09.806 +10:00 INF] Getting 100 refunds to invoice
[2025-06-18 07:51:09.812 +10:00 INF] Found 0 refunds to invoice
[2025-06-18 07:51:09.817 +10:00 INF] No pending work found
[2025-06-18 07:51:09.820 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-18 07:51:09.824 +10:00 INF] Cleaning up invoicer logs...
[2025-06-18 07:51:09.910 +10:00 INF] LEP Invoicer completed successfully in 1141ms. No work to process.
[2025-06-18 07:51:09.918 +10:00 INF] Database connection disposed
[2025-06-18 07:51:09.921 +10:00 INF] LEP Invoicer completed with result: 0
