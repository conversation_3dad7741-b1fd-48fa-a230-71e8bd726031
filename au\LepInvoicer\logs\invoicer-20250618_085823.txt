[2025-06-18 08:58:23.635 +10:00 INF] Starting LEP Invoicer application
[2025-06-18 08:58:23.664 +10:00 INF] Initializing FastReport...
[2025-06-18 08:58:23.736 +10:00 INF] FastReport initialized successfully
[2025-06-18 08:58:24.139 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-18 08:58:25.440 +10:00 INF] Starting LEP Invoicer at "2025-06-18T08:58:25.4351072+10:00"
[2025-06-18 08:58:25.447 +10:00 INF] Initializing database service...
[2025-06-18 08:58:25.450 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-18 08:58:25.559 +10:00 INF] Database connection established successfully
[2025-06-18 08:58:25.561 +10:00 INF] Database service initialized successfully
[2025-06-18 08:58:25.563 +10:00 INF] Checking for pending work...
[2025-06-18 08:58:25.566 +10:00 INF] Getting 500 orders to invoice
[2025-06-18 08:58:26.446 +10:00 INF] Found 0 orders to invoice (filtered 16 candidates)
[2025-06-18 08:58:26.456 +10:00 INF] Getting 100 credits to invoice
[2025-06-18 08:58:26.475 +10:00 INF] Found 0 credits to invoice
[2025-06-18 08:58:26.478 +10:00 INF] Getting 100 refunds to invoice
[2025-06-18 08:58:26.483 +10:00 INF] Found 0 refunds to invoice
[2025-06-18 08:58:26.492 +10:00 INF] No pending work found
[2025-06-18 08:58:26.494 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-18 08:58:26.496 +10:00 INF] Cleaning up invoicer logs...
[2025-06-18 08:58:26.577 +10:00 INF] LEP Invoicer completed successfully in 1141ms. No work to process.
[2025-06-18 08:58:26.584 +10:00 INF] Database connection disposed
[2025-06-18 08:58:26.586 +10:00 INF] LEP Invoicer completed with result: 0
