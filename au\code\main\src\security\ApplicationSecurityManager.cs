namespace lep.security
{
	//public class ApplicationSecurityManager
	//{
	//	public static ISecurityApplication SecurityApp { get; set; }
	//	public static IStaff SystemUser => SecurityApp?.GetSystemUser();
	//	public static IUser CurrentUser => SecurityApp?.Identity as IUser;

	//	//public static void AssertPermission (string operation)
	//	//{
	//	//	SecurityApp?.AssertPermission(operation);
	//	//}

	//	public static void AssertIsCustomerUser (ICustomerUser customer)
	//	{
	//		SecurityApp?.AssertIsCustomerUser(customer);
	//	}

	//	public void DoPrivileged (PrivilegeDelegate func)
	//	{
	//		SecurityApp?.DoPrivileged(func);
	//	}
	//}
}