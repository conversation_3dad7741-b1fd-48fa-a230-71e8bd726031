[2025-06-17 15:58:48.744 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 15:58:48.780 +10:00 INF] Initializing FastReport...
[2025-06-17 15:58:48.862 +10:00 INF] FastReport initialized successfully
[2025-06-17 15:58:49.376 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 15:58:50.699 +10:00 INF] Starting LEP Invoicer at "2025-06-17T15:58:50.6996557+10:00"
[2025-06-17 15:58:50.703 +10:00 INF] Initializing database service...
[2025-06-17 15:58:50.706 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 15:58:50.808 +10:00 INF] Database connection established successfully
[2025-06-17 15:58:50.809 +10:00 INF] Database service initialized successfully
[2025-06-17 15:58:50.813 +10:00 INF] Checking for pending work...
[2025-06-17 15:58:50.818 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 15:58:52.084 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 15:58:52.087 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 15:58:52.101 +10:00 INF] Found 0 credits to invoice
[2025-06-17 15:58:52.104 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 15:58:52.112 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 15:58:52.114 +10:00 INF] No pending work found
[2025-06-17 15:58:52.117 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 15:58:52.118 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 15:58:52.193 +10:00 INF] LEP Invoicer completed successfully in 1493ms. No work to process.
[2025-06-17 15:58:52.200 +10:00 INF] Database connection disposed
[2025-06-17 15:58:52.221 +10:00 INF] LEP Invoicer completed with result: 0
