[2025-06-17 17:00:09.576 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 17:00:09.603 +10:00 INF] Initializing FastReport...
[2025-06-17 17:00:09.675 +10:00 INF] FastReport initialized successfully
[2025-06-17 17:00:10.188 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 17:00:11.550 +10:00 INF] Starting LEP Invoicer at "2025-06-17T17:00:11.5497575+10:00"
[2025-06-17 17:00:11.554 +10:00 INF] Initializing database service...
[2025-06-17 17:00:11.557 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 17:00:11.664 +10:00 INF] Database connection established successfully
[2025-06-17 17:00:11.666 +10:00 INF] Database service initialized successfully
[2025-06-17 17:00:11.669 +10:00 INF] Checking for pending work...
[2025-06-17 17:00:11.672 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 17:00:12.576 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 17:00:12.584 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 17:00:12.597 +10:00 INF] Found 0 credits to invoice
[2025-06-17 17:00:12.602 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 17:00:12.616 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 17:00:12.620 +10:00 INF] No pending work found
[2025-06-17 17:00:12.628 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 17:00:12.630 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 17:00:12.707 +10:00 INF] LEP Invoicer completed successfully in 1157ms. No work to process.
[2025-06-17 17:00:12.714 +10:00 INF] Database connection disposed
[2025-06-17 17:00:12.716 +10:00 INF] LEP Invoicer completed with result: 0
