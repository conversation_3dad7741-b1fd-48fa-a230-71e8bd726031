[2025-06-17 17:45:55.844 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 17:45:55.882 +10:00 INF] Initializing FastReport...
[2025-06-17 17:45:55.982 +10:00 INF] FastReport initialized successfully
[2025-06-17 17:45:56.451 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 17:45:57.708 +10:00 INF] Starting LEP Invoicer at "2025-06-17T17:45:57.7083911+10:00"
[2025-06-17 17:45:57.712 +10:00 INF] Initializing database service...
[2025-06-17 17:45:57.715 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 17:45:57.821 +10:00 INF] Database connection established successfully
[2025-06-17 17:45:57.822 +10:00 INF] Database service initialized successfully
[2025-06-17 17:45:57.825 +10:00 INF] Checking for pending work...
[2025-06-17 17:45:57.828 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 17:45:58.745 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 17:45:58.748 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 17:45:58.761 +10:00 INF] Found 0 credits to invoice
[2025-06-17 17:45:58.763 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 17:45:58.768 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 17:45:58.770 +10:00 INF] No pending work found
[2025-06-17 17:45:58.771 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 17:45:58.773 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 17:45:58.851 +10:00 INF] LEP Invoicer completed successfully in 1143ms. No work to process.
[2025-06-17 17:45:58.859 +10:00 INF] Database connection disposed
[2025-06-17 17:45:58.863 +10:00 INF] LEP Invoicer completed with result: 0
