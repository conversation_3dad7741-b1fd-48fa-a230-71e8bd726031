[2025-06-10 19:26:07.132 +10:00 INF] Starting LEP Invoicer application
[2025-06-10 19:26:07.808 +10:00 INF] Loaded MYOB cache: 0 accounts, 0 tax codes, 0 customers
[2025-06-10 19:26:10.192 +10:00 INF] Starting LEP Invoicer at "2025-06-10T19:26:10.1915470+10:00"
[2025-06-10 19:26:10.198 +10:00 INF] Initializing services...
[2025-06-10 19:26:10.203 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-10 19:26:10.377 +10:00 INF] Database connection established successfully
[2025-06-10 19:26:10.384 +10:00 INF] Initializing MYOB service...
[2025-06-10 19:26:10.388 +10:00 INF] Initializing MYOB with OAuth authentication
[2025-06-10 19:26:10.393 +10:00 INF] Starting OAuth authentication flow
[2025-06-10 19:26:10.511 +10:00 INF] Using existing OAuth tokens
[2025-06-10 19:26:10.514 +10:00 INF] Initializing MYOB services with OAuth keystore
[2025-06-10 19:26:10.519 +10:00 INF] MYOB services initialized successfully
[2025-06-10 19:26:10.522 +10:00 INF] OAuth keystore set for API calls
[2025-06-10 19:26:10.523 +10:00 INF] Getting company files from MYOB
[2025-06-10 19:26:10.860 +10:00 INF] MYOB service initialized with OAuth credentials. Using company file: LEP Colour Printers Pty Ltd
[2025-06-10 19:26:10.873 +10:00 INF] Current cache stats: {"AccountsCount":0,"TaxCodesCount":0,"CustomersCount":0,"LastCacheLoad":"2025-06-10T19:26:07.7952953+10:00","CacheAge":"00:00:03.0716751","IsExpired":true,"CacheDirectory":"C:\\LepSF\\au\\LepInvoicer\\bin\\Debug\\net8.0-windows7.0\\cache"}
[2025-06-10 19:26:10.896 +10:00 INF] MYOB cache needs refresh (expired or empty), fetching from API...
[2025-06-10 19:26:12.524 +10:00 INF] Updated accounts cache with 269 accounts
[2025-06-10 19:26:12.553 +10:00 INF] Refreshed 279 accounts from MYOB API
[2025-06-10 19:26:13.106 +10:00 INF] Updated tax codes cache with 18 tax codes
[2025-06-10 19:26:13.109 +10:00 INF] Refreshed 18 tax codes from MYOB API
[2025-06-10 19:26:14.696 +10:00 INF] Updated customers cache with 393 customers
[2025-06-10 19:26:14.699 +10:00 INF] Refreshed 400 customers from MYOB API
[2025-06-10 19:26:14.701 +10:00 INF] Using cached GST tax code
[2025-06-10 19:26:14.702 +10:00 INF] Using cached freight account
[2025-06-10 19:26:14.704 +10:00 INF] Using cached discounts account
[2025-06-10 19:26:14.706 +10:00 INF] MYOB cache initialization complete: {"AccountsCount":269,"TaxCodesCount":18,"CustomersCount":393,"LastCacheLoad":"2025-06-10T19:26:14.6961776+10:00","CacheAge":"00:00:00.0101827","IsExpired":false,"CacheDirectory":"C:\\LepSF\\au\\LepInvoicer\\bin\\Debug\\net8.0-windows7.0\\cache"}
[2025-06-10 19:26:14.717 +10:00 INF] MYOB service initialized successfully with company file: LEP Colour Printers Pty Ltd
[2025-06-10 19:26:14.719 +10:00 INF] Services initialized successfully
[2025-06-10 19:26:14.723 +10:00 INF] Processing order invoices...
[2025-06-10 19:26:14.728 +10:00 INF] Getting 500 orders to invoice
[2025-06-10 19:26:15.633 +10:00 INF] Found 2 orders to invoice
[2025-06-10 19:26:15.635 +10:00 INF] Found 2 orders to process
[2025-06-10 19:26:15.638 +10:00 INF] Getting order 1417006
[2025-06-10 19:26:15.995 +10:00 INF] Processing order 1417006 with 1 jobs, total: ¤0.00
[2025-06-10 19:26:16.001 +10:00 WRN] Order 1417006 has zero price, marking as invoiced
[2025-06-10 19:26:16.003 +10:00 INF] Getting order 1416838
[2025-06-10 19:26:16.014 +10:00 INF] Processing order 1416838 with 1 jobs, total: ¤0.00
[2025-06-10 19:26:16.017 +10:00 WRN] Order 1416838 has zero price, marking as invoiced
[2025-06-10 19:26:16.018 +10:00 INF] Order processing completed. Processed: 2, Success: 0, Failed: 2
[2025-06-10 19:26:16.024 +10:00 INF] Processing credit invoices...
[2025-06-10 19:26:16.027 +10:00 INF] Getting 100 credits to invoice
[2025-06-10 19:26:16.050 +10:00 INF] Found 0 credits to invoice
[2025-06-10 19:26:16.056 +10:00 INF] Processing refund invoices...
[2025-06-10 19:26:16.060 +10:00 INF] Getting 100 refunds to invoice
[2025-06-10 19:26:16.094 +10:00 INF] Found 0 refunds to invoice
[2025-06-10 19:26:16.097 +10:00 INF] Cleaning up invoicer logs...
[2025-06-10 19:26:16.176 +10:00 INF] LEP Invoicer completed successfully in 5984ms. Orders: 2, Credits: 0, Refunds: 0
[2025-06-10 19:26:16.193 +10:00 INF] Database connection disposed
[2025-06-10 19:26:16.197 +10:00 INF] LEP Invoicer completed with result: 0
