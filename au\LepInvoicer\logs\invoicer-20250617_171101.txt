[2025-06-17 17:11:01.847 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 17:11:01.879 +10:00 INF] Initializing FastReport...
[2025-06-17 17:11:01.975 +10:00 INF] FastReport initialized successfully
[2025-06-17 17:11:02.632 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 17:11:04.087 +10:00 INF] Starting LEP Invoicer at "2025-06-17T17:11:04.0870414+10:00"
[2025-06-17 17:11:04.091 +10:00 INF] Initializing database service...
[2025-06-17 17:11:04.093 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 17:11:04.198 +10:00 INF] Database connection established successfully
[2025-06-17 17:11:04.200 +10:00 INF] Database service initialized successfully
[2025-06-17 17:11:04.203 +10:00 INF] Checking for pending work...
[2025-06-17 17:11:04.205 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 17:11:05.122 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 17:11:05.125 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 17:11:05.137 +10:00 INF] Found 0 credits to invoice
[2025-06-17 17:11:05.139 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 17:11:05.143 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 17:11:05.145 +10:00 INF] No pending work found
[2025-06-17 17:11:05.146 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 17:11:05.148 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 17:11:05.240 +10:00 INF] LEP Invoicer completed successfully in 1152ms. No work to process.
[2025-06-17 17:11:05.256 +10:00 INF] Database connection disposed
[2025-06-17 17:11:05.257 +10:00 INF] LEP Invoicer completed with result: 0
