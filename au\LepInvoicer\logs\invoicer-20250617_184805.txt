[2025-06-17 18:48:05.691 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 18:48:05.718 +10:00 INF] Initializing FastReport...
[2025-06-17 18:48:05.791 +10:00 INF] FastReport initialized successfully
[2025-06-17 18:48:06.157 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 18:48:07.505 +10:00 INF] Starting LEP Invoicer at "2025-06-17T18:48:07.5049041+10:00"
[2025-06-17 18:48:07.508 +10:00 INF] Initializing database service...
[2025-06-17 18:48:07.511 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 18:48:07.613 +10:00 INF] Database connection established successfully
[2025-06-17 18:48:07.614 +10:00 INF] Database service initialized successfully
[2025-06-17 18:48:07.617 +10:00 INF] Checking for pending work...
[2025-06-17 18:48:07.620 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 18:48:08.525 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 18:48:08.527 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 18:48:08.542 +10:00 INF] Found 0 credits to invoice
[2025-06-17 18:48:08.545 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 18:48:08.551 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 18:48:08.553 +10:00 INF] No pending work found
[2025-06-17 18:48:08.555 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 18:48:08.559 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 18:48:08.638 +10:00 INF] LEP Invoicer completed successfully in 1133ms. No work to process.
[2025-06-17 18:48:08.645 +10:00 INF] Database connection disposed
[2025-06-17 18:48:08.647 +10:00 INF] LEP Invoicer completed with result: 0
