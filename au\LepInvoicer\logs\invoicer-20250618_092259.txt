[2025-06-18 09:22:59.609 +10:00 INF] Starting LEP Invoicer application
[2025-06-18 09:22:59.644 +10:00 INF] Initializing FastReport...
[2025-06-18 09:22:59.718 +10:00 INF] FastReport initialized successfully
[2025-06-18 09:23:00.168 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-18 09:23:01.501 +10:00 INF] Starting LEP Invoicer at "2025-06-18T09:23:01.5015629+10:00"
[2025-06-18 09:23:01.505 +10:00 INF] Initializing database service...
[2025-06-18 09:23:01.508 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-18 09:23:01.653 +10:00 INF] Database connection established successfully
[2025-06-18 09:23:01.655 +10:00 INF] Database service initialized successfully
[2025-06-18 09:23:01.658 +10:00 INF] Checking for pending work...
[2025-06-18 09:23:01.661 +10:00 INF] Getting 500 orders to invoice
[2025-06-18 09:23:02.788 +10:00 INF] Found 0 orders to invoice (filtered 16 candidates)
[2025-06-18 09:23:02.791 +10:00 INF] Getting 100 credits to invoice
[2025-06-18 09:23:02.804 +10:00 INF] Found 0 credits to invoice
[2025-06-18 09:23:02.807 +10:00 INF] Getting 100 refunds to invoice
[2025-06-18 09:23:02.812 +10:00 INF] Found 0 refunds to invoice
[2025-06-18 09:23:02.814 +10:00 INF] No pending work found
[2025-06-18 09:23:02.815 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-18 09:23:02.816 +10:00 INF] Cleaning up invoicer logs...
[2025-06-18 09:23:02.903 +10:00 INF] LEP Invoicer completed successfully in 1402ms. No work to process.
[2025-06-18 09:23:02.914 +10:00 INF] Database connection disposed
[2025-06-18 09:23:02.916 +10:00 INF] LEP Invoicer completed with result: 0
