[2025-06-18 10:10:07.728 +10:00 INF] Starting LEP Invoicer application
[2025-06-18 10:10:07.757 +10:00 INF] Initializing FastReport...
[2025-06-18 10:10:07.829 +10:00 INF] FastReport initialized successfully
[2025-06-18 10:10:08.245 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-18 10:10:09.563 +10:00 INF] Starting LEP Invoicer at "2025-06-18T10:10:09.5635101+10:00"
[2025-06-18 10:10:09.568 +10:00 INF] Initializing database service...
[2025-06-18 10:10:09.570 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-18 10:10:09.680 +10:00 INF] Database connection established successfully
[2025-06-18 10:10:09.681 +10:00 INF] Database service initialized successfully
[2025-06-18 10:10:09.684 +10:00 INF] Checking for pending work...
[2025-06-18 10:10:09.687 +10:00 INF] Getting 500 orders to invoice
[2025-06-18 10:10:10.592 +10:00 INF] Found 0 orders to invoice (filtered 16 candidates)
[2025-06-18 10:10:10.596 +10:00 INF] Getting 100 credits to invoice
[2025-06-18 10:10:10.610 +10:00 INF] Found 0 credits to invoice
[2025-06-18 10:10:10.613 +10:00 INF] Getting 100 refunds to invoice
[2025-06-18 10:10:10.619 +10:00 INF] Found 0 refunds to invoice
[2025-06-18 10:10:10.630 +10:00 INF] No pending work found
[2025-06-18 10:10:10.633 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-18 10:10:10.638 +10:00 INF] Cleaning up invoicer logs...
[2025-06-18 10:10:10.723 +10:00 INF] LEP Invoicer completed successfully in 1160ms. No work to process.
[2025-06-18 10:10:10.733 +10:00 INF] Database connection disposed
[2025-06-18 10:10:10.735 +10:00 INF] LEP Invoicer completed with result: 0
