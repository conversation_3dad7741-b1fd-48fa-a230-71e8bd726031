[2025-06-17 17:49:12.545 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 17:49:12.572 +10:00 INF] Initializing FastReport...
[2025-06-17 17:49:12.650 +10:00 INF] FastReport initialized successfully
[2025-06-17 17:49:13.040 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 17:49:14.220 +10:00 INF] Starting LEP Invoicer at "2025-06-17T17:49:14.2201201+10:00"
[2025-06-17 17:49:14.223 +10:00 INF] Initializing database service...
[2025-06-17 17:49:14.226 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 17:49:14.325 +10:00 INF] Database connection established successfully
[2025-06-17 17:49:14.327 +10:00 INF] Database service initialized successfully
[2025-06-17 17:49:14.329 +10:00 INF] Checking for pending work...
[2025-06-17 17:49:14.332 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 17:49:15.242 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 17:49:15.245 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 17:49:15.261 +10:00 INF] Found 0 credits to invoice
[2025-06-17 17:49:15.264 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 17:49:15.268 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 17:49:15.269 +10:00 INF] No pending work found
[2025-06-17 17:49:15.273 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 17:49:15.274 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 17:49:15.345 +10:00 INF] LEP Invoicer completed successfully in 1125ms. No work to process.
[2025-06-17 17:49:15.351 +10:00 INF] Database connection disposed
[2025-06-17 17:49:15.354 +10:00 INF] LEP Invoicer completed with result: 0
