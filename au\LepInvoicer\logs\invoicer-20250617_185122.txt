[2025-06-17 18:51:22.532 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 18:51:22.561 +10:00 INF] Initializing FastReport...
[2025-06-17 18:51:22.635 +10:00 INF] FastReport initialized successfully
[2025-06-17 18:51:23.069 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 18:51:24.396 +10:00 INF] Starting LEP Invoicer at "2025-06-17T18:51:24.3963854+10:00"
[2025-06-17 18:51:24.400 +10:00 INF] Initializing database service...
[2025-06-17 18:51:24.403 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 18:51:24.509 +10:00 INF] Database connection established successfully
[2025-06-17 18:51:24.510 +10:00 INF] Database service initialized successfully
[2025-06-17 18:51:24.536 +10:00 INF] Checking for pending work...
[2025-06-17 18:51:24.555 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 18:51:25.435 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 18:51:25.438 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 18:51:25.451 +10:00 INF] Found 0 credits to invoice
[2025-06-17 18:51:25.453 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 18:51:25.457 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 18:51:25.458 +10:00 INF] No pending work found
[2025-06-17 18:51:25.459 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 18:51:25.461 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 18:51:25.533 +10:00 INF] LEP Invoicer completed successfully in 1137ms. No work to process.
[2025-06-17 18:51:25.540 +10:00 INF] Database connection disposed
[2025-06-17 18:51:25.542 +10:00 INF] LEP Invoicer completed with result: 0
