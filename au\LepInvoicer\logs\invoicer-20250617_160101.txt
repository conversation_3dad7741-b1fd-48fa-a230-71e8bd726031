[2025-06-17 16:01:01.721 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 16:01:01.750 +10:00 INF] Initializing FastReport...
[2025-06-17 16:01:01.843 +10:00 INF] FastReport initialized successfully
[2025-06-17 16:01:02.392 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 16:01:03.797 +10:00 INF] Starting LEP Invoicer at "2025-06-17T16:01:03.7968077+10:00"
[2025-06-17 16:01:03.800 +10:00 INF] Initializing database service...
[2025-06-17 16:01:03.803 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 16:01:03.905 +10:00 INF] Database connection established successfully
[2025-06-17 16:01:03.906 +10:00 INF] Database service initialized successfully
[2025-06-17 16:01:03.909 +10:00 INF] Checking for pending work...
[2025-06-17 16:01:03.913 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 16:01:04.954 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 16:01:04.960 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 16:01:04.973 +10:00 INF] Found 0 credits to invoice
[2025-06-17 16:01:04.976 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 16:01:04.988 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 16:01:04.992 +10:00 INF] No pending work found
[2025-06-17 16:01:04.993 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 16:01:04.994 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 16:01:05.104 +10:00 INF] LEP Invoicer completed successfully in 1307ms. No work to process.
[2025-06-17 16:01:05.111 +10:00 INF] Database connection disposed
[2025-06-17 16:01:05.113 +10:00 INF] LEP Invoicer completed with result: 0
