[2025-06-17 16:21:52.622 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 16:21:52.665 +10:00 INF] Initializing FastReport...
[2025-06-17 16:21:52.752 +10:00 INF] FastReport initialized successfully
[2025-06-17 16:21:53.196 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 16:21:54.506 +10:00 INF] Starting LEP Invoicer at "2025-06-17T16:21:54.5060923+10:00"
[2025-06-17 16:21:54.510 +10:00 INF] Initializing database service...
[2025-06-17 16:21:54.513 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 16:21:54.640 +10:00 INF] Database connection established successfully
[2025-06-17 16:21:54.641 +10:00 INF] Database service initialized successfully
[2025-06-17 16:21:54.644 +10:00 INF] Checking for pending work...
[2025-06-17 16:21:54.647 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 16:21:55.531 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 16:21:55.534 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 16:21:55.547 +10:00 INF] Found 0 credits to invoice
[2025-06-17 16:21:55.549 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 16:21:55.555 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 16:21:55.557 +10:00 INF] No pending work found
[2025-06-17 16:21:55.558 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 16:21:55.559 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 16:21:55.635 +10:00 INF] LEP Invoicer completed successfully in 1129ms. No work to process.
[2025-06-17 16:21:55.642 +10:00 INF] Database connection disposed
[2025-06-17 16:21:55.644 +10:00 INF] LEP Invoicer completed with result: 0
