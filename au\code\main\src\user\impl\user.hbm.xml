<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2"
				   namespace="lep.user"
				   assembly="lep"
				   auto-import="true"
				   default-cascade="all">

    <class name="IUser" table="LepUser" discriminator-value="null">
        <cache usage="read-write" />
        <id name="Id" type="Int32" unsaved-value="0">
            <generator class="identity" />
        </id>
        <discriminator column="IsStaff" type="YesNo" />
        <property name="DateModified" column="DateModified" />
        <property name="Username" length="40" not-null="true" />
        <property name="HashPassword" length="60" not-null="true" column="Password" />
        <property name="IsEnabled" type="YesNo" not-null="true" />
        <property name="IsStaff" type="YesNo" not-null="true" insert="false" update="false" />
        <property name="Email" length="100" not-null="true" />
        <property name="FirstName" length="40" not-null="true" />
        <property name="LastName" length="40" not-null="true" />
        <property name="AreaCode" length="12" not-null="true" />
        <property name="Phone" length="12" not-null="true" />
        <property name="Mobile" length="12" not-null="true" />
        <property name="LastLogin" type="lumen.hibernate.type.DateTimeType, lumen" />
        <property name="DateCreated" column="DateCreated" type="lumen.hibernate.type.DateTimeType, lumen" not-null="false" update="false" insert="false" />

        <subclass name="lep.user.impl.Staff, lep" proxy="IStaff" discriminator-value="Y">
            <property name="Role" type="lep.GenericEnum`1[lep.user.Role], lep" not-null="true" />
            <property name="Barcode" length="128" not-null="false" />
            <one-to-one name="OrderSearchCriteria" class="lep.user.impl.OrderSearchCriteria, lep" cascade="all-delete-orphan" lazy="proxy" fetch="select" />
            <one-to-one name="RunSearchCriteria" class="lep.user.impl.RunSearchCriteria, lep" cascade="all-delete-orphan" lazy="proxy" fetch="select" />
            <property name="IPAddress" length="40" />
        </subclass>

        <subclass name="lep.user.impl.CustomerUser, lep" proxy="ICustomerUser" discriminator-value="N">
            <join table="Customer">
                <key column="CustomerId" />

                <property name="Name" length="200" not-null="true" />
                <property name="BusinessType" length="255" not-null="false" />

                <property name="ABN" length="20" not-null="true" />
                <property name="MYOB" length="50" not-null="true" />
                <property name="HasSystemAccess" type="YesNo" not-null="true" />
                <property name="PostalIsBilling" type="YesNo" not-null="true" />
                <property name="NotificationType" type="lep.GenericEnum`1[lep.user.NotificationType], lep" />
                <component name="BillingAddress" class="lep.address.impl.PhysicalAddress, lep">
                    <property name="Address1" column="BillingAddress1" length="80" not-null="true" />
                    <property name="Address2" column="BillingAddress2" length="80" />
                    <property name="Address3" column="BillingAddress3" length="80" />
                    <property name="City" column="BillingCity" length="80" not-null="true" />
                    <property name="State" column="BillingState" length="40" not-null="true" />
                    <property name="Postcode" column="BillingPostcode" length="10" not-null="true" />
                    <property name="Country" column="BillingCountry" length="80" not-null="true" />
                </component>
                <component name="PostalAddress" class="lep.address.impl.PhysicalAddress, lep">
                    <property name="Address1" column="PostalAddress1" length="80" not-null="true" />
                    <property name="Address2" column="PostalAddress2" length="80" not-null="true" />
                    <property name="Address3" column="PostalAddress3" length="80" not-null="true" />
                    <property name="City" column="PostalCity" length="80" not-null="true" />
                    <property name="State" column="PostalState" length="40" not-null="true" />
                    <property name="Postcode" column="PostalPostcode" length="10" not-null="true" />
                    <property name="Country" column="PostalCountry" length="80" not-null="true" />
                </component>

                <!--
                // to do : remove these columns from DB
                <component name="Contact1" class="lep.contact.impl.Contact, lep">
                  <property name="Name" column="Contact1Name" length="50" />
                  <property name="Phone" column="Contact1Phone" length="12" />
                  <property name="Mobile" column="Contact1Mobile" length="12" />
                  <property name="AreaCode" column="Contact1AreaCode" length="6" />
                  <property name="FaxAreaCode" column="Contact1FaxAreaCode" length="6" />
                  <property name="Fax" column="Contact1Fax" length="12" />
	              <property name="Email" column="Contact1Email" />
                </component>
                <component name="Contact2" class="lep.contact.impl.Contact, lep">
                  <property name="Name" column="Contact2Name" length="50" />
                  <property name="Phone" column="Contact2Phone" length="12" />
                  <property name="Mobile" column="Contact2Mobile" length="12" />
                  <property name="AreaCode" column="Contact2AreaCode" length="6" />
                  <property name="FaxAreaCode" column="Contact2FaxAreaCode" length="6" />
                  <property name="Fax" column="Contact2Fax" length="12" />
	              <property name="Email" column="Contact2Email" />
                </component>
		        -->

                <property name="Contacts"        column="Contacts"  type="lep.JsonType`1[lep.contact.ListOfContacts], lep" />
                <property name="ContactsJsonStr" column="Contacts" access="readonly"  insert="false" update="false" />

                <property name="LastOrderDate" />
                <property name="CreditLimit" type="int" not-null="false" />
                <property name="MYOBBalance" length="50" not-null="false" />
                <property name="MAT" not-null="false" />
                <property name="MAT3ma" not-null="false" update="true"/>
                <property name="MYOBPastDue"  />
                <property name="PaymentTerms" type="lep.GenericEnum`1[lep.PaymentTermsOptions], lep" not-null="true" />
                <property name="AccountEmail" length="100" not-null="true" />
                <property name="OtherEmail" length="100" not-null="true" />
                <!--<property name="PreferredCourier"  type="lep.GenericEnum`1[lep.courier.CourierType], lep" not-null="true" />-->

                <!--<property name="PreferredCourier"  type="lep.courier.CourierType, lep" not-null="true" />-->
                <!--<component name="PreferredCourier"  class="lep.courier.CourierType, lep">
                    <property name="CourierName" column="PreferredCourier"  length="50" not-null="true" />
                    <property name="ServiceName" column="PreferredCourierService"  length="50" not-null="true" />
                </component>-->

                <property name="AllowedSamples" type="YesNo" not-null="true" />
                <property name="SendSamples" type="YesNo" not-null="true" />

                <property name="IsChargedGST" type="YesNo" not-null="true" />
                <property name="IsEnrolledInIVR" type="YesNo" not-null="true" />
                <property name="Notes" type="StringClob" />
                <property name="ProductionNotes" length="75" not-null="false" />
                <property name="SiteLocation" type="lep.GenericEnum`1[lep.SiteLocation], lep" not-null="false" />
                <property name="ProductPriceCode"  length="5" not-null="false" />
                <property name="FreightPriceCode"  length="5" not-null="false" />

                <property name="QTP" type="YesNo" not-null="true" />
                <property name="CustomerStatus"   length="255" not-null="false" />
                <property name="ReturnedLapseDate"  type="lumen.hibernate.type.DateTimeType, lumen"  not-null="false" />
                <property name="FranchiseCode" length="255" not-null="false" />
                <property name="SalesConsultant"  length="255" not-null="false" />

                <property name="FirstOrderDate" not-null="false"/>

                <property name="LastLoginDate" not-null="false"/>
                <property name="LastOrderDispatchDate" not-null="false"/>

                <property name="Potential" />


                <!--
                <property name="FavouriteDeliveryDetails"  not-null="false"
                type="lep.JsonType`1[lep.address.impl.ListOfDeliveryDetails], lep" />

                <bag name="ConNotes" cascade="all-delete-orphan" lazy="true">
                    <key column="OrderId" />
                    <one-to-many class="lep.order.IOrderConNote, lep" />
                </bag>
   <bag name="FavouriteDeliveryDetails" cascade="all-delete-orphan" lazy="true">
                    <key column="Id" />
                    <one-to-many class="IDeliveryDetails" />
                </bag>
                 -->





                <property name="DeniedTemplates"  not-null="false" type="lep.JsonType`1[lep.ListOfInt], lep" />

                <property name="IsPrintPortalEnabled" column="WhiteLabelEnabled"  type="YesNo" not-null="true" />

                <component name="PrintPortalSettings"  class="lep.printPortal.PrintPortalSettings, lep">
                    <property name="Version"  column="PPVersion" not-null="false" />

                    <property name="IsAacEnabled" column="WhiteLabelAACEnabled"  type="YesNo" not-null="true" />

                    <property name="PayPalClientId"  length="50" not-null="false" />
                    <property name="StripePublishableKey"  length="50" not-null="false" />
                    <property name="StripeRestrictedChargeKey"  length="50" not-null="false" />

                    <property name="DeniedTemplates" column="WhiteLabelDeniedTemplates"  not-null="false" type="lep.JsonType`1[lep.ListOfInt], lep" />

                    <property name="WhiteLabelAllowedProducts" column="WhiteLabelAllowedProducts"  not-null="false" type="lep.JsonType`1[lep.printPortal.ListOfAllowedProducts], lep" />


                    <property name="PricingModel" type="lep.GenericEnum`1[lep.printPortal.PricingModel], lep" not-null="false" />
                    <property name="WhiteLabelGlobalMarkup"  not-null="false" />

                    <property name="CategoryMarkups"  column="WhiteLabelCategoryMarkups"  not-null="false"
                              type="lep.JsonType`1[lep.printPortal.ListOfCategoryMarkups], lep" />

                    <property name="PriceRangeMarkups" column="WhiteLabelPriceRangeMarkups" not-null="false"
                                    type="lep.JsonType`1[lep.printPortal.ListOfPriceRangeMarkups], lep" />

                    <property name="FavouriteCategoryMarkups"  column="WhiteLabelFavouriteCategoryMarkups"  not-null="false"
                              type="lep.JsonType`1[lep.printPortal.FavouriteCategoryMarkups], lep" />

                    <property name="FavouritePriceRangeMarkups" column="WhiteLabelFavouritePriceRangeMarkups" not-null="false"
                                    type="lep.JsonType`1[lep.printPortal.FavouritePriceRangeMarkups], lep" />

                    <property name="CustomCssURL"  column="WhiteLabelCustomCssURL" not-null="false" />
                </component>

                <many-to-one name="ParentCustomer" class="lep.user.impl.CustomerUser, lep" column="ParentCustomerId" not-null="false" cascade="none" />

                <property name="Archived" type="YesNo" not-null="true" />
                <!--  Print Portal related end -->




                <!-- <bag name="Contacts"  table="CustomerContacts" cascade="all-delete-orphan" lazy="true">
                    <key column="CustomerId" />
                    <one-to-many class="IContact" />
                </bag> -->
                <property name="SampleKitSentOn"  not-null="false" />
                <property name="MyobUid"  not-null="false" />
            </join>
        </subclass>
    </class>
    <class name="lep.user.impl.CustomerNote, lep" table="CustomerNotes1"  discriminator-value="null" schema="PRD_AU_NOTES.dbo">
        <cache usage="read-write" />
        <id name="Id" type="Int32" unsaved-value="0">
            <generator class="identity" />
        </id>
        <discriminator column="Id" type="Int32" insert="false" />
        <many-to-one name="Customer" class="lep.user.impl.CustomerUser, lep" column="CustomerId" not-null="true" cascade="none" />
        <property name="NoteText" />
        <property name="CreatedBy" />
        <property name="CreatedOn" />
        <property name="IsDocument" not-null="false"  />
        <property name="MimeType" not-null="false"  />
        <property name="FileName" not-null="false"  />
        <property name="FileSize" not-null="false"  />
        <!--<property name="DocumentBody" />-->
        <!--<subclass name="lep.user.impl.CustomerNote, lep" proxy="ICustomerNote"  discriminator-value="not null" />-->
    </class>

    <!--
    <class name="IContact" table="CustomerContacts"  discriminator-value="null">
        <cache usage="read-write" />
        <id name="Id" type="Int32" unsaved-value="0">
            <generator class="identity" />
        </id>
        <discriminator column="Id" type="Int32" insert="false" />

        <many-to-one name="Customer" column="CustomerId"
         class="ICustomerUser" not-null="true" cascade="none" />

        <property name="Name" />
        <property name="Phone" />
        <property name="Mobile" />
        <property name="Email" />


        <subclass name="lep.user.impl.Contact, lep" proxy="IContact"
        discriminator-value="not null" />
    </class>
    -->

    <!--

     <class name="IOrderConNote" table="ConNote" discriminator-value="null">
        <cache usage="read-write" />
        <id name="Id" type="Int32" unsaved-value="0">
            <generator class="identity" />
        </id>
        <discriminator column="Id" type="Int32" insert="false" />
        <timestamp name="DateModified" column="DateModified" />
        <property name="ConNote" length="25" not-null="true" />
        <property name="DateCreated" column="DateCreated" type="lumen.hibernate.type.DateTimeType, lumen" not-null="false" update="false" insert="false" />
        <property name="IsEmailGenerated"  type="Int32" not-null="true" />
        <property name="DispatchFacility" column="DispatchFacility" type="lep.GenericEnum`1[lep.job.Facility], lep" not-null="true" />

        <property name="CarrierName" length="30" />
        <property name="CarrierService" length="30" />

        <property name="TrackingLabels"  not-null="false" type="lep.JsonType`1[lep.ListOfString], lep" />

        <many-to-one name="Order" column="OrderId" class="lep.order.IOrder, lep" not-null="true" cascade="none" />
        <subclass name="lep.order.impl.OrderConNote, lep" proxy="IOrderConNote" discriminator-value="not null" />
    </class>


        -->

    <class name="lep.address.impl.DeliveryDetails" table="CustomerFavouriteDeliveryDetails" discriminator-value="null">
        <cache usage="read-write" />
        <id name="Id" type="Int32" unsaved-value="0">
            <generator class="identity" />
        </id>
        <discriminator column="Id" type="Int32" insert="false" />
        <timestamp name="DateModified" column="DateModified" />
        <property name="CustomerId"  />
        <property name="UserId"  />
        <property name="RecipientName"  />
        <property name="RecipientPhone"  />
        <property name="DeliveryInstructions"  />
        <component name="Address" class="lep.address.impl.PhysicalAddress, lep">
            <property name="Address1"  not-null="true" />
            <property name="Address2"  />
            <property name="Address3"  />
            <property name="City"      />
            <property name="State"     />
            <property name="Postcode"  />
            <property name="Country"   />
        </component>

    </class>



    <class name="lep.user.impl.OrderSearchCriteria, lep" table="OrderSearchCriteria" proxy="IOrderSearchCriteria">
        <cache usage="read-write" />
        <id name="Id" type="Int32">
            <generator class="foreign">
                <param name="property">Staff</param>
            </generator>
        </id>
        <one-to-one name="Staff" class="IUser" lazy="proxy" fetch="select" />
        <property name="IsSearchPanelOpen" type="YesNo" not-null="true" />
        <property name="OrderStatus" type="lep.GenericEnum`1[lep.OrderStatusOptions], lep" not-null="false" />
        <property name="JobType" type="lep.GenericEnum`1[lep.job.JobTypeOptions], lep" not-null="false" />
        <property name="Celloglaze" type="lep.GenericEnum`1[lep.run.RunCelloglazeOptions], lep" not-null="false" />
        <many-to-one name="Size" column="SizeId" class="lep.job.impl.PaperSize, lep" not-null="false" cascade="none" />
        <many-to-one name="Stock" column="StockId" class="lep.job.impl.Stock, lep" not-null="false" cascade="none" />
        <property name="Customer" length="40" not-null="true" />
        <property name="OrderNr" length="40" not-null="true" />
        <property name="JobNr" length="40" not-null="true" />
        <property name="IsNewOrder" type="YesNo" not-null="true" />
        <property name="IsOnlyUrgentOrder" type="YesNo" not-null="true" />
        <property name="IsOnPrepay" type="YesNo" not-null="true" />
        <property name="IsOnhold" type="YesNo" not-null="true" />
        <property name="IsAwaitingPayment" type="YesNo" not-null="true" />
        <property name="IsCorrectedOrder" type="YesNo" not-null="true" />
        <property name="IsOpenOrder" type="YesNo" not-null="true" />
        <property name="IsNonBusinessCard" type="YesNo" not-null="true" />
        <property name="IsWaitingApproval" type="YesNo" not-null="true" />
        <property name="IsWithdraw" type="YesNo" not-null="true" />
        <property name="IsQuoteRequired" type="YesNo" not-null="true" />
        <property name="Ordering" length="100" not-null="true" />
        <property name="Page" not-null="true" type="Int32" />
        <property name="IsOrderWithDigitalJob" type="YesNo" not-null="true" />
        <property name="IsOrderWithOutworkJob" type="YesNo" not-null="true" />
        <property name="IsPaidFor" type="YesNo" not-null="true" />
        <property name="HideOnHoldOrders" type="YesNo" not-null="true" />
        <property name="ShowOnlyOnHoldOrders" type="YesNo" not-null="true" />
    </class>

    <class name="lep.user.impl.RunSearchCriteria, lep" table="RunSearchCriteria" proxy="IRunSearchCriteria">
        <cache usage="read-write" />
        <id name="Id" type="Int32">
            <generator class="foreign">
                <param name="property">Staff</param>
            </generator>
        </id>
        <one-to-one name="Staff" class="IUser" fetch="select" constrained="true" lazy="proxy" />
        <property name="IsSearchPanelOpen" type="YesNo" />
        <!--<property name="OpenRun" type="StringClob"  />-->
        <!--<property name="RunStatus" type="StringClob"  />-->

        <property name="OpenRun" type="lep.CommaDelimitedSet`1[System.Int32], lep" />
        <property name="RunStatus" type="lep.CommaDelimitedSet`1[lep.RunStatusOptions], lep" />

        <property name="Customer" length="40" />
        <property name="OrderNr" length="40" />
        <property name="JobNr" length="40" />
        <property name="RunNr" length="40" />
        <property name="IsUrgent" type="YesNo" />
        <property name="IsOnHold" type="YesNo" />
        <many-to-one name="Size" column="SizeId" class="lep.job.impl.PaperSize, lep" not-null="false" cascade="none" />
        <property name="Cello" column="Cello"  type="lep.GenericEnum`1[lep.run.RunCelloglazeOptions], lep" not-null="false" />
        <property name="RunSearchOption" type="lep.PersistentEnumType`1[lep.RunSearchOptions], lep" not-null="true" />
        <property name="Side" type="Int32" />
        <property name="Ordering" length="100" not-null="true" />
        <property name="RunOrdering" length="100" not-null="true" />
        <property name="StockKind" length="20" />
        <many-to-one name="Stock" column="StockId" class="lep.job.impl.Stock, lep" not-null="false" cascade="none" />
        <property name="JobTypes" type="lep.CommaDelimitedSet`1[System.Int32], lep" not-null="false"  />
        <property name="JobType" type="Int32" not-null="false" />

        <property name="Facility" column="Facility" type="lep.GenericEnum`1[lep.job.Facility], lep" not-null="true" />
    </class>
</hibernate-mapping>
