[2025-06-17 19:11:01.596 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 19:11:01.625 +10:00 INF] Initializing FastReport...
[2025-06-17 19:11:01.699 +10:00 INF] FastReport initialized successfully
[2025-06-17 19:11:02.210 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 19:11:03.705 +10:00 INF] Starting LEP Invoicer at "2025-06-17T19:11:03.7052775+10:00"
[2025-06-17 19:11:03.709 +10:00 INF] Initializing database service...
[2025-06-17 19:11:03.711 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 19:11:03.811 +10:00 INF] Database connection established successfully
[2025-06-17 19:11:03.813 +10:00 INF] Database service initialized successfully
[2025-06-17 19:11:03.816 +10:00 INF] Checking for pending work...
[2025-06-17 19:11:03.819 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 19:11:04.746 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 19:11:04.749 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 19:11:04.761 +10:00 INF] Found 0 credits to invoice
[2025-06-17 19:11:04.763 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 19:11:04.767 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 19:11:04.768 +10:00 INF] No pending work found
[2025-06-17 19:11:04.770 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 19:11:04.781 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 19:11:04.860 +10:00 INF] LEP Invoicer completed successfully in 1154ms. No work to process.
[2025-06-17 19:11:04.866 +10:00 INF] Database connection disposed
[2025-06-17 19:11:04.868 +10:00 INF] LEP Invoicer completed with result: 0
