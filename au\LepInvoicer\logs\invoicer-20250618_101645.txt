[2025-06-18 10:16:45.792 +10:00 INF] Starting LEP Invoicer application
[2025-06-18 10:16:45.826 +10:00 INF] Initializing FastReport...
[2025-06-18 10:16:45.912 +10:00 INF] FastReport initialized successfully
[2025-06-18 10:16:46.479 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-18 10:16:48.033 +10:00 INF] Starting LEP Invoicer at "2025-06-18T10:16:48.0330807+10:00"
[2025-06-18 10:16:48.037 +10:00 INF] Initializing database service...
[2025-06-18 10:16:48.040 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-18 10:16:48.166 +10:00 INF] Database connection established successfully
[2025-06-18 10:16:48.168 +10:00 INF] Database service initialized successfully
[2025-06-18 10:16:48.170 +10:00 INF] Checking for pending work...
[2025-06-18 10:16:48.173 +10:00 INF] Getting 500 orders to invoice
[2025-06-18 10:16:49.296 +10:00 INF] Found 0 orders to invoice (filtered 16 candidates)
[2025-06-18 10:16:49.301 +10:00 INF] Getting 100 credits to invoice
[2025-06-18 10:16:49.316 +10:00 INF] Found 0 credits to invoice
[2025-06-18 10:16:49.319 +10:00 INF] Getting 100 refunds to invoice
[2025-06-18 10:16:49.324 +10:00 INF] Found 0 refunds to invoice
[2025-06-18 10:16:49.325 +10:00 INF] No pending work found
[2025-06-18 10:16:49.339 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-18 10:16:49.342 +10:00 INF] Cleaning up invoicer logs...
[2025-06-18 10:16:49.431 +10:00 INF] LEP Invoicer completed successfully in 1398ms. No work to process.
[2025-06-18 10:16:49.438 +10:00 INF] Database connection disposed
[2025-06-18 10:16:49.442 +10:00 INF] LEP Invoicer completed with result: 0
