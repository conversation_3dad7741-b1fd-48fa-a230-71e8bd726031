[2025-06-18 09:04:55.663 +10:00 INF] Starting LEP Invoicer application
[2025-06-18 09:04:55.692 +10:00 INF] Initializing FastReport...
[2025-06-18 09:04:55.767 +10:00 INF] FastReport initialized successfully
[2025-06-18 09:04:56.219 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-18 09:04:57.547 +10:00 INF] Starting LEP Invoicer at "2025-06-18T09:04:57.5476246+10:00"
[2025-06-18 09:04:57.551 +10:00 INF] Initializing database service...
[2025-06-18 09:04:57.553 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-18 09:04:57.661 +10:00 INF] Database connection established successfully
[2025-06-18 09:04:57.662 +10:00 INF] Database service initialized successfully
[2025-06-18 09:04:57.665 +10:00 INF] Checking for pending work...
[2025-06-18 09:04:57.668 +10:00 INF] Getting 500 orders to invoice
[2025-06-18 09:04:58.555 +10:00 INF] Found 0 orders to invoice (filtered 16 candidates)
[2025-06-18 09:04:58.558 +10:00 INF] Getting 100 credits to invoice
[2025-06-18 09:04:58.591 +10:00 INF] Found 0 credits to invoice
[2025-06-18 09:04:58.594 +10:00 INF] Getting 100 refunds to invoice
[2025-06-18 09:04:58.601 +10:00 INF] Found 0 refunds to invoice
[2025-06-18 09:04:58.606 +10:00 INF] No pending work found
[2025-06-18 09:04:58.608 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-18 09:04:58.610 +10:00 INF] Cleaning up invoicer logs...
[2025-06-18 09:04:58.691 +10:00 INF] LEP Invoicer completed successfully in 1143ms. No work to process.
[2025-06-18 09:04:58.697 +10:00 INF] Database connection disposed
[2025-06-18 09:04:58.699 +10:00 INF] LEP Invoicer completed with result: 0
