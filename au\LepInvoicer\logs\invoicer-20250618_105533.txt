[2025-06-18 10:55:33.626 +10:00 INF] Starting LEP Invoicer application
[2025-06-18 10:55:33.654 +10:00 INF] Initializing FastReport...
[2025-06-18 10:55:33.733 +10:00 INF] FastReport initialized successfully
[2025-06-18 10:55:34.216 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-18 10:55:35.702 +10:00 INF] Starting LEP Invoicer at "2025-06-18T10:55:35.7024303+10:00"
[2025-06-18 10:55:35.709 +10:00 INF] Initializing database service...
[2025-06-18 10:55:35.711 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-18 10:55:35.810 +10:00 INF] Database connection established successfully
[2025-06-18 10:55:35.811 +10:00 INF] Database service initialized successfully
[2025-06-18 10:55:35.814 +10:00 INF] Checking for pending work...
[2025-06-18 10:55:35.818 +10:00 INF] Getting 500 orders to invoice
[2025-06-18 10:55:36.577 +10:00 INF] Found 0 orders to invoice (marked 0 zero-priced as invoiced, filtered 0 candidates)
[2025-06-18 10:55:36.584 +10:00 INF] Getting 100 credits to invoice
[2025-06-18 10:55:36.600 +10:00 INF] Found 0 credits to invoice
[2025-06-18 10:55:36.603 +10:00 INF] Getting 100 refunds to invoice
[2025-06-18 10:55:36.610 +10:00 INF] Found 0 refunds to invoice
[2025-06-18 10:55:36.611 +10:00 INF] No pending work found
[2025-06-18 10:55:36.621 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-18 10:55:36.623 +10:00 INF] Cleaning up invoicer logs...
[2025-06-18 10:55:36.709 +10:00 INF] LEP Invoicer completed successfully in 1007ms. No work to process.
[2025-06-18 10:55:36.716 +10:00 INF] Database connection disposed
[2025-06-18 10:55:36.718 +10:00 INF] LEP Invoicer completed with result: 0
