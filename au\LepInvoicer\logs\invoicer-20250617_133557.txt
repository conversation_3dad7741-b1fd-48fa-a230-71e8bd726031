[2025-06-17 13:35:57.914 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 13:35:57.946 +10:00 INF] Initializing FastReport...
[2025-06-17 13:35:58.026 +10:00 INF] FastReport initialized successfully
[2025-06-17 13:35:58.489 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 13:35:59.989 +10:00 INF] Starting LEP Invoicer at "2025-06-17T13:35:59.9888348+10:00"
[2025-06-17 13:35:59.993 +10:00 INF] Initializing database service...
[2025-06-17 13:35:59.996 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 13:36:00.132 +10:00 INF] Database connection established successfully
[2025-06-17 13:36:00.134 +10:00 INF] Database service initialized successfully
[2025-06-17 13:36:00.137 +10:00 INF] Checking for pending work...
[2025-06-17 13:36:00.142 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 13:36:01.172 +10:00 INF] Found 4 orders to invoice (filtered 19 candidates)
[2025-06-17 13:36:01.176 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 13:36:01.212 +10:00 INF] Found 0 credits to invoice
[2025-06-17 13:36:01.215 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 13:36:01.236 +10:00 INF] Found 16 refunds to invoice
[2025-06-17 13:36:01.239 +10:00 INF] Found pending work: 4 orders, 16 refunds
[2025-06-17 13:36:01.242 +10:00 INF] Initializing MYOB and other services...
[2025-06-17 13:36:01.245 +10:00 INF] Initializing MYOB service...
[2025-06-17 13:36:01.249 +10:00 INF] Initializing MYOB with OAuth authentication
[2025-06-17 13:36:01.252 +10:00 INF] Starting OAuth authentication flow
[2025-06-17 13:36:01.258 +10:00 INF] Using existing OAuth tokens
[2025-06-17 13:36:01.259 +10:00 INF] Initializing MYOB services with OAuth keystore
[2025-06-17 13:36:01.265 +10:00 INF] MYOB services initialized successfully
[2025-06-17 13:36:01.266 +10:00 INF] OAuth keystore set for API calls
[2025-06-17 13:36:01.269 +10:00 INF] Getting company files from MYOB
[2025-06-17 13:36:01.571 +10:00 INF] MYOB service initialized with OAuth credentials. Using company file: LEP Colour Printers Pty Ltd
[2025-06-17 13:36:02.359 +10:00 INF] MYOB service initialized successfully with company file: LEP Colour Printers Pty Ltd
[2025-06-17 13:36:02.362 +10:00 INF] All services initialized successfully
[2025-06-17 13:36:02.367 +10:00 INF] Processing order invoices...
[2025-06-17 13:36:02.371 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 13:36:02.767 +10:00 INF] Found 4 orders to invoice (filtered 19 candidates)
[2025-06-17 13:36:02.769 +10:00 INF] Found 4 orders to process
[2025-06-17 13:36:02.772 +10:00 INF] Getting order 1418152
[2025-06-17 13:36:02.812 +10:00 INF] Processing order 1418152 with 1 jobs, total: ¤50.36
[2025-06-17 13:36:02.818 +10:00 INF] Creating order invoice for order 1418152
[2025-06-17 13:36:04.204 +10:00 INF] Successfully created MYOB invoice O1418152 for order 1418152
[2025-06-17 13:36:04.206 +10:00 INF] Successfully created MYOB invoice for order 1418152
[2025-06-17 13:36:04.211 +10:00 INF] Generating PDF invoice for order 1418152 at \\dfs01\resource\invoices\2025/Jun/17\O1418152.pdf
[2025-06-17 13:36:06.059 +10:00 INF] Successfully generated PDF for order 1418152
[2025-06-17 13:36:06.082 +10:00 WRN] No email address found for order 1418152
[2025-06-17 13:36:06.088 +10:00 INF] Successfully processed order 1418152 (MYOB + PDF)
[2025-06-17 13:36:06.091 +10:00 INF] Getting order 1417796
[2025-06-17 13:36:06.097 +10:00 INF] Processing order 1417796 with 1 jobs, total: ¤37.50
[2025-06-17 13:36:06.099 +10:00 INF] Creating order invoice for order 1417796
[2025-06-17 13:36:08.564 +10:00 INF] Successfully created MYOB invoice O1417796 for order 1417796
[2025-06-17 13:36:08.565 +10:00 INF] Successfully created MYOB invoice for order 1417796
[2025-06-17 13:36:08.572 +10:00 INF] Generating PDF invoice for order 1417796 at \\dfs01\resource\invoices\2025/Jun/17\O1417796.pdf
[2025-06-17 13:36:08.904 +10:00 INF] Successfully generated PDF for order 1417796
[2025-06-17 13:36:08.927 +10:00 WRN] No email address found for order 1417796
[2025-06-17 13:36:08.930 +10:00 INF] Successfully processed order 1417796 (MYOB + PDF)
[2025-06-17 13:36:08.934 +10:00 INF] Getting order 1417319
[2025-06-17 13:36:08.938 +10:00 INF] Processing order 1417319 with 1 jobs, total: ¤548.42
[2025-06-17 13:36:08.940 +10:00 INF] Creating order invoice for order 1417319
[2025-06-17 13:36:10.177 +10:00 INF] Successfully created MYOB invoice O1417319 for order 1417319
[2025-06-17 13:36:10.178 +10:00 INF] Successfully created MYOB invoice for order 1417319
[2025-06-17 13:36:10.180 +10:00 INF] Generating PDF invoice for order 1417319 at \\dfs01\resource\invoices\2025/Jun/17\O1417319.pdf
[2025-06-17 13:36:10.529 +10:00 INF] Successfully generated PDF for order 1417319
[2025-06-17 13:36:10.566 +10:00 WRN] No email address found for order 1417319
[2025-06-17 13:36:10.598 +10:00 INF] Successfully processed order 1417319 (MYOB + PDF)
[2025-06-17 13:36:10.619 +10:00 INF] Getting order 1417127
[2025-06-17 13:36:10.624 +10:00 INF] Processing order 1417127 with 1 jobs, total: ¤32.45
[2025-06-17 13:36:10.628 +10:00 INF] Creating order invoice for order 1417127
[2025-06-17 13:36:11.826 +10:00 INF] Successfully created MYOB invoice O1417127 for order 1417127
[2025-06-17 13:36:11.828 +10:00 INF] Successfully created MYOB invoice for order 1417127
[2025-06-17 13:36:11.830 +10:00 INF] Generating PDF invoice for order 1417127 at \\dfs01\resource\invoices\2025/Jun/17\O1417127.pdf
[2025-06-17 13:36:12.197 +10:00 INF] Successfully generated PDF for order 1417127
[2025-06-17 13:36:12.230 +10:00 WRN] No email address found for order 1417127
[2025-06-17 13:36:12.233 +10:00 INF] Successfully processed order 1417127 (MYOB + PDF)
[2025-06-17 13:36:12.237 +10:00 INF] Order processing completed. Processed: 4, Success: 4, Failed: 0
[2025-06-17 13:36:12.245 +10:00 INF] Processing credit invoices...
[2025-06-17 13:36:12.246 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 13:36:12.252 +10:00 INF] Found 0 credits to invoice
[2025-06-17 13:36:12.257 +10:00 INF] Processing refund invoices...
[2025-06-17 13:36:12.259 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 13:36:12.267 +10:00 INF] Found 16 refunds to invoice
[2025-06-17 13:36:12.271 +10:00 INF] Creating refund invoice for refund 1801, Amount: ¤1.69
[2025-06-17 13:36:12.786 +10:00 INF] Deleting existing invoice S248751801
[2025-06-17 13:36:13.621 +10:00 WRN] Failed to delete existing invoice S248751801 - invoice may be locked/paid in MYOB
MYOB.AccountRight.SDK.ApiValidationException: Encountered a validation error (https://arl2.api.myob.com/accountright/b2f20bad-65c4-4dd3-a921-0a20ccfffb73/Sale/Invoice/Service/ae7fcbd9-f0a0-4771-83a9-3dcdea23f5cb)
 ---> System.Net.WebException: The remote server returned an error: (400) Bad Request.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiDeleteRequestSync(Uri uri, ICompanyFileCredentials credentials)
   at MYOB.AccountRight.SDK.Services.MutableService`1.Delete(CompanyFile cf, Guid uid, ICompanyFileCredentials credentials, ErrorLevel errorLevel)
   at LepInvoicer.Implementations.MYOBService.<>c__DisplayClass33_1.<DeleteExistingInvoice>b__1() in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 875
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Implementations.MYOBService.DeleteExistingInvoice(String invoiceNumber) in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 874
[2025-06-17 13:36:13.672 +10:00 WRN] Skipping refund 1801 - cannot delete existing invoice S248751801
[2025-06-17 13:36:13.680 +10:00 INF] Creating refund invoice for refund 1802, Amount: ¤4.59
[2025-06-17 13:36:13.972 +10:00 INF] Deleting existing invoice S139771802
[2025-06-17 13:36:14.267 +10:00 WRN] Failed to delete existing invoice S139771802 - invoice may be locked/paid in MYOB
MYOB.AccountRight.SDK.ApiValidationException: Encountered a validation error (https://arl2.api.myob.com/accountright/b2f20bad-65c4-4dd3-a921-0a20ccfffb73/Sale/Invoice/Service/61295f40-7707-4574-825e-84da93a4b016)
 ---> System.Net.WebException: The remote server returned an error: (400) Bad Request.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiDeleteRequestSync(Uri uri, ICompanyFileCredentials credentials)
   at MYOB.AccountRight.SDK.Services.MutableService`1.Delete(CompanyFile cf, Guid uid, ICompanyFileCredentials credentials, ErrorLevel errorLevel)
   at LepInvoicer.Implementations.MYOBService.<>c__DisplayClass33_1.<DeleteExistingInvoice>b__1() in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 875
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Implementations.MYOBService.DeleteExistingInvoice(String invoiceNumber) in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 874
[2025-06-17 13:36:14.285 +10:00 WRN] Skipping refund 1802 - cannot delete existing invoice S139771802
[2025-06-17 13:36:14.293 +10:00 INF] Creating refund invoice for refund 1803, Amount: ¤9.27
[2025-06-17 13:36:14.778 +10:00 INF] Deleting existing invoice S150791803
[2025-06-17 13:36:15.184 +10:00 WRN] Failed to delete existing invoice S150791803 - invoice may be locked/paid in MYOB
MYOB.AccountRight.SDK.ApiValidationException: Encountered a validation error (https://arl2.api.myob.com/accountright/b2f20bad-65c4-4dd3-a921-0a20ccfffb73/Sale/Invoice/Service/ef2d3cc0-43a4-4c63-95e9-76389d058325)
 ---> System.Net.WebException: The remote server returned an error: (400) Bad Request.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiDeleteRequestSync(Uri uri, ICompanyFileCredentials credentials)
   at MYOB.AccountRight.SDK.Services.MutableService`1.Delete(CompanyFile cf, Guid uid, ICompanyFileCredentials credentials, ErrorLevel errorLevel)
   at LepInvoicer.Implementations.MYOBService.<>c__DisplayClass33_1.<DeleteExistingInvoice>b__1() in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 875
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Implementations.MYOBService.DeleteExistingInvoice(String invoiceNumber) in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 874
[2025-06-17 13:36:15.208 +10:00 WRN] Skipping refund 1803 - cannot delete existing invoice S150791803
[2025-06-17 13:36:15.214 +10:00 INF] Creating refund invoice for refund 1804, Amount: ¤11.23
[2025-06-17 13:36:15.611 +10:00 INF] Deleting existing invoice S150791804
[2025-06-17 13:36:15.972 +10:00 WRN] Failed to delete existing invoice S150791804 - invoice may be locked/paid in MYOB
MYOB.AccountRight.SDK.ApiValidationException: Encountered a validation error (https://arl2.api.myob.com/accountright/b2f20bad-65c4-4dd3-a921-0a20ccfffb73/Sale/Invoice/Service/903aeac6-182f-4aae-b4c4-d8e81708ec76)
 ---> System.Net.WebException: The remote server returned an error: (400) Bad Request.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiDeleteRequestSync(Uri uri, ICompanyFileCredentials credentials)
   at MYOB.AccountRight.SDK.Services.MutableService`1.Delete(CompanyFile cf, Guid uid, ICompanyFileCredentials credentials, ErrorLevel errorLevel)
   at LepInvoicer.Implementations.MYOBService.<>c__DisplayClass33_1.<DeleteExistingInvoice>b__1() in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 875
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Implementations.MYOBService.DeleteExistingInvoice(String invoiceNumber) in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 874
[2025-06-17 13:36:15.990 +10:00 WRN] Skipping refund 1804 - cannot delete existing invoice S150791804
[2025-06-17 13:36:15.998 +10:00 INF] Creating refund invoice for refund 1805, Amount: ¤32.62
[2025-06-17 13:36:16.342 +10:00 INF] Deleting existing invoice S252801805
[2025-06-17 13:36:16.692 +10:00 WRN] Failed to delete existing invoice S252801805 - invoice may be locked/paid in MYOB
MYOB.AccountRight.SDK.ApiValidationException: Encountered a validation error (https://arl2.api.myob.com/accountright/b2f20bad-65c4-4dd3-a921-0a20ccfffb73/Sale/Invoice/Service/6942f99f-e33c-424d-861a-2215ef9a1e09)
 ---> System.Net.WebException: The remote server returned an error: (400) Bad Request.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiDeleteRequestSync(Uri uri, ICompanyFileCredentials credentials)
   at MYOB.AccountRight.SDK.Services.MutableService`1.Delete(CompanyFile cf, Guid uid, ICompanyFileCredentials credentials, ErrorLevel errorLevel)
   at LepInvoicer.Implementations.MYOBService.<>c__DisplayClass33_1.<DeleteExistingInvoice>b__1() in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 875
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Implementations.MYOBService.DeleteExistingInvoice(String invoiceNumber) in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 874
[2025-06-17 13:36:16.747 +10:00 WRN] Skipping refund 1805 - cannot delete existing invoice S252801805
[2025-06-17 13:36:16.753 +10:00 INF] Creating refund invoice for refund 1806, Amount: ¤36.12
[2025-06-17 13:36:17.174 +10:00 INF] Deleting existing invoice S141881806
[2025-06-17 13:36:17.675 +10:00 WRN] Failed to delete existing invoice S141881806 - invoice may be locked/paid in MYOB
MYOB.AccountRight.SDK.ApiValidationException: Encountered a validation error (https://arl2.api.myob.com/accountright/b2f20bad-65c4-4dd3-a921-0a20ccfffb73/Sale/Invoice/Service/818eaa9f-5e51-4882-ab77-6b4bb0624735)
 ---> System.Net.WebException: The remote server returned an error: (400) Bad Request.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiDeleteRequestSync(Uri uri, ICompanyFileCredentials credentials)
   at MYOB.AccountRight.SDK.Services.MutableService`1.Delete(CompanyFile cf, Guid uid, ICompanyFileCredentials credentials, ErrorLevel errorLevel)
   at LepInvoicer.Implementations.MYOBService.<>c__DisplayClass33_1.<DeleteExistingInvoice>b__1() in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 875
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Implementations.MYOBService.DeleteExistingInvoice(String invoiceNumber) in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 874
[2025-06-17 13:36:17.692 +10:00 WRN] Skipping refund 1806 - cannot delete existing invoice S141881806
[2025-06-17 13:36:17.698 +10:00 INF] Creating refund invoice for refund 1807, Amount: ¤1.77
[2025-06-17 13:36:18.091 +10:00 INF] Deleting existing invoice S152161807
[2025-06-17 13:36:18.347 +10:00 WRN] Failed to delete existing invoice S152161807 - invoice may be locked/paid in MYOB
MYOB.AccountRight.SDK.ApiValidationException: Encountered a validation error (https://arl2.api.myob.com/accountright/b2f20bad-65c4-4dd3-a921-0a20ccfffb73/Sale/Invoice/Service/793efdaa-dcb5-422e-9ef2-69458a598c33)
 ---> System.Net.WebException: The remote server returned an error: (400) Bad Request.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiDeleteRequestSync(Uri uri, ICompanyFileCredentials credentials)
   at MYOB.AccountRight.SDK.Services.MutableService`1.Delete(CompanyFile cf, Guid uid, ICompanyFileCredentials credentials, ErrorLevel errorLevel)
   at LepInvoicer.Implementations.MYOBService.<>c__DisplayClass33_1.<DeleteExistingInvoice>b__1() in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 875
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Implementations.MYOBService.DeleteExistingInvoice(String invoiceNumber) in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 874
[2025-06-17 13:36:18.380 +10:00 WRN] Skipping refund 1807 - cannot delete existing invoice S152161807
[2025-06-17 13:36:18.393 +10:00 INF] Creating refund invoice for refund 1812, Amount: ¤1,312.65
[2025-06-17 13:36:19.754 +10:00 INF] Deleting existing invoice S251951812
[2025-06-17 13:36:20.125 +10:00 WRN] Failed to delete existing invoice S251951812 - invoice may be locked/paid in MYOB
MYOB.AccountRight.SDK.ApiValidationException: Encountered a validation error (https://arl2.api.myob.com/accountright/b2f20bad-65c4-4dd3-a921-0a20ccfffb73/Sale/Invoice/Service/a713338d-93e4-4f31-ab91-4947eb95c2d0)
 ---> System.Net.WebException: The remote server returned an error: (400) Bad Request.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiDeleteRequestSync(Uri uri, ICompanyFileCredentials credentials)
   at MYOB.AccountRight.SDK.Services.MutableService`1.Delete(CompanyFile cf, Guid uid, ICompanyFileCredentials credentials, ErrorLevel errorLevel)
   at LepInvoicer.Implementations.MYOBService.<>c__DisplayClass33_1.<DeleteExistingInvoice>b__1() in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 875
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Implementations.MYOBService.DeleteExistingInvoice(String invoiceNumber) in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 874
[2025-06-17 13:36:20.154 +10:00 WRN] Skipping refund 1812 - cannot delete existing invoice S251951812
[2025-06-17 13:36:20.162 +10:00 INF] Creating refund invoice for refund 1814, Amount: ¤1.37
[2025-06-17 13:36:20.528 +10:00 INF] Deleting existing invoice S137511814
[2025-06-17 13:36:20.838 +10:00 WRN] Failed to delete existing invoice S137511814 - invoice may be locked/paid in MYOB
MYOB.AccountRight.SDK.ApiValidationException: Encountered a validation error (https://arl2.api.myob.com/accountright/b2f20bad-65c4-4dd3-a921-0a20ccfffb73/Sale/Invoice/Service/ae7cf4da-1e73-4681-98b6-92685935c4b0)
 ---> System.Net.WebException: The remote server returned an error: (400) Bad Request.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiDeleteRequestSync(Uri uri, ICompanyFileCredentials credentials)
   at MYOB.AccountRight.SDK.Services.MutableService`1.Delete(CompanyFile cf, Guid uid, ICompanyFileCredentials credentials, ErrorLevel errorLevel)
   at LepInvoicer.Implementations.MYOBService.<>c__DisplayClass33_1.<DeleteExistingInvoice>b__1() in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 875
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Implementations.MYOBService.DeleteExistingInvoice(String invoiceNumber) in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 874
[2025-06-17 13:36:20.864 +10:00 WRN] Skipping refund 1814 - cannot delete existing invoice S137511814
[2025-06-17 13:36:20.881 +10:00 INF] Creating refund invoice for refund 1815, Amount: ¤3.83
[2025-06-17 13:36:21.228 +10:00 INF] Deleting existing invoice S138991815
[2025-06-17 13:36:24.484 +10:00 WRN] Failed to delete existing invoice S138991815 - invoice may be locked/paid in MYOB
MYOB.AccountRight.SDK.ApiValidationException: Encountered a validation error (https://arl2.api.myob.com/accountright/b2f20bad-65c4-4dd3-a921-0a20ccfffb73/Sale/Invoice/Service/0aaeec22-6d8e-4d71-87bd-33f814e5f06f)
 ---> System.Net.WebException: The remote server returned an error: (400) Bad Request.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiDeleteRequestSync(Uri uri, ICompanyFileCredentials credentials)
   at MYOB.AccountRight.SDK.Services.MutableService`1.Delete(CompanyFile cf, Guid uid, ICompanyFileCredentials credentials, ErrorLevel errorLevel)
   at LepInvoicer.Implementations.MYOBService.<>c__DisplayClass33_1.<DeleteExistingInvoice>b__1() in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 875
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Implementations.MYOBService.DeleteExistingInvoice(String invoiceNumber) in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 874
[2025-06-17 13:36:24.503 +10:00 WRN] Skipping refund 1815 - cannot delete existing invoice S138991815
[2025-06-17 13:36:24.512 +10:00 INF] Creating refund invoice for refund 1816, Amount: ¤6.80
[2025-06-17 13:36:25.031 +10:00 INF] Deleting existing invoice S143701816
[2025-06-17 13:36:25.486 +10:00 WRN] Failed to delete existing invoice S143701816 - invoice may be locked/paid in MYOB
MYOB.AccountRight.SDK.ApiValidationException: Encountered a validation error (https://arl2.api.myob.com/accountright/b2f20bad-65c4-4dd3-a921-0a20ccfffb73/Sale/Invoice/Service/5ac7ff46-5d70-47ca-a353-44c57a10c35c)
 ---> System.Net.WebException: The remote server returned an error: (400) Bad Request.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiDeleteRequestSync(Uri uri, ICompanyFileCredentials credentials)
   at MYOB.AccountRight.SDK.Services.MutableService`1.Delete(CompanyFile cf, Guid uid, ICompanyFileCredentials credentials, ErrorLevel errorLevel)
   at LepInvoicer.Implementations.MYOBService.<>c__DisplayClass33_1.<DeleteExistingInvoice>b__1() in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 875
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Implementations.MYOBService.DeleteExistingInvoice(String invoiceNumber) in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 874
[2025-06-17 13:36:25.509 +10:00 WRN] Skipping refund 1816 - cannot delete existing invoice S143701816
[2025-06-17 13:36:25.522 +10:00 INF] Creating refund invoice for refund 1817, Amount: ¤19.84
[2025-06-17 13:36:25.828 +10:00 INF] Deleting existing invoice S143701817
[2025-06-17 13:36:26.159 +10:00 WRN] Failed to delete existing invoice S143701817 - invoice may be locked/paid in MYOB
MYOB.AccountRight.SDK.ApiValidationException: Encountered a validation error (https://arl2.api.myob.com/accountright/b2f20bad-65c4-4dd3-a921-0a20ccfffb73/Sale/Invoice/Service/2e674898-d09f-451c-921f-b6054b956d02)
 ---> System.Net.WebException: The remote server returned an error: (400) Bad Request.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiDeleteRequestSync(Uri uri, ICompanyFileCredentials credentials)
   at MYOB.AccountRight.SDK.Services.MutableService`1.Delete(CompanyFile cf, Guid uid, ICompanyFileCredentials credentials, ErrorLevel errorLevel)
   at LepInvoicer.Implementations.MYOBService.<>c__DisplayClass33_1.<DeleteExistingInvoice>b__1() in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 875
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Implementations.MYOBService.DeleteExistingInvoice(String invoiceNumber) in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 874
[2025-06-17 13:36:26.185 +10:00 WRN] Skipping refund 1817 - cannot delete existing invoice S143701817
[2025-06-17 13:36:26.190 +10:00 INF] Creating refund invoice for refund 1818, Amount: ¤61.92
[2025-06-17 13:36:26.513 +10:00 INF] Deleting existing invoice S1531791818
[2025-06-17 13:36:26.816 +10:00 WRN] Failed to delete existing invoice S1531791818 - invoice may be locked/paid in MYOB
MYOB.AccountRight.SDK.ApiValidationException: Encountered a validation error (https://arl2.api.myob.com/accountright/b2f20bad-65c4-4dd3-a921-0a20ccfffb73/Sale/Invoice/Service/f2d3d1a7-f2e9-4aee-b0fe-cf5f0c77f7bc)
 ---> System.Net.WebException: The remote server returned an error: (400) Bad Request.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiDeleteRequestSync(Uri uri, ICompanyFileCredentials credentials)
   at MYOB.AccountRight.SDK.Services.MutableService`1.Delete(CompanyFile cf, Guid uid, ICompanyFileCredentials credentials, ErrorLevel errorLevel)
   at LepInvoicer.Implementations.MYOBService.<>c__DisplayClass33_1.<DeleteExistingInvoice>b__1() in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 875
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Implementations.MYOBService.DeleteExistingInvoice(String invoiceNumber) in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 874
[2025-06-17 13:36:26.854 +10:00 WRN] Skipping refund 1818 - cannot delete existing invoice S1531791818
[2025-06-17 13:36:26.866 +10:00 INF] Creating refund invoice for refund 1824, Amount: ¤70.04
[2025-06-17 13:36:27.249 +10:00 INF] Deleting existing invoice S189871824
[2025-06-17 13:36:27.593 +10:00 WRN] Failed to delete existing invoice S189871824 - invoice may be locked/paid in MYOB
MYOB.AccountRight.SDK.ApiValidationException: Encountered a validation error (https://arl2.api.myob.com/accountright/b2f20bad-65c4-4dd3-a921-0a20ccfffb73/Sale/Invoice/Service/3366c74b-aec7-4ee3-87d0-4ed666a81e70)
 ---> System.Net.WebException: The remote server returned an error: (400) Bad Request.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiDeleteRequestSync(Uri uri, ICompanyFileCredentials credentials)
   at MYOB.AccountRight.SDK.Services.MutableService`1.Delete(CompanyFile cf, Guid uid, ICompanyFileCredentials credentials, ErrorLevel errorLevel)
   at LepInvoicer.Implementations.MYOBService.<>c__DisplayClass33_1.<DeleteExistingInvoice>b__1() in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 875
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Implementations.MYOBService.DeleteExistingInvoice(String invoiceNumber) in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 874
[2025-06-17 13:36:27.613 +10:00 WRN] Skipping refund 1824 - cannot delete existing invoice S189871824
[2025-06-17 13:36:27.623 +10:00 INF] Creating refund invoice for refund 1836, Amount: ¤32.32
[2025-06-17 13:36:27.961 +10:00 INF] Deleting existing invoice S252801836
[2025-06-17 13:36:28.283 +10:00 WRN] Failed to delete existing invoice S252801836 - invoice may be locked/paid in MYOB
MYOB.AccountRight.SDK.ApiValidationException: Encountered a validation error (https://arl2.api.myob.com/accountright/b2f20bad-65c4-4dd3-a921-0a20ccfffb73/Sale/Invoice/Service/4b9f90a9-fe2b-4248-bce3-1aa759007fcd)
 ---> System.Net.WebException: The remote server returned an error: (400) Bad Request.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiDeleteRequestSync(Uri uri, ICompanyFileCredentials credentials)
   at MYOB.AccountRight.SDK.Services.MutableService`1.Delete(CompanyFile cf, Guid uid, ICompanyFileCredentials credentials, ErrorLevel errorLevel)
   at LepInvoicer.Implementations.MYOBService.<>c__DisplayClass33_1.<DeleteExistingInvoice>b__1() in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 875
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Implementations.MYOBService.DeleteExistingInvoice(String invoiceNumber) in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 874
[2025-06-17 13:36:28.312 +10:00 WRN] Skipping refund 1836 - cannot delete existing invoice S252801836
[2025-06-17 13:36:28.321 +10:00 INF] Creating refund invoice for refund 1837, Amount: ¤26.00
[2025-06-17 13:36:29.346 +10:00 INF] Deleting existing invoice S191871837
[2025-06-17 13:36:29.740 +10:00 WRN] Failed to delete existing invoice S191871837 - invoice may be locked/paid in MYOB
MYOB.AccountRight.SDK.ApiValidationException: Encountered a validation error (https://arl2.api.myob.com/accountright/b2f20bad-65c4-4dd3-a921-0a20ccfffb73/Sale/Invoice/Service/27c54bcf-d87b-45d8-a5e4-997d4b831e2e)
 ---> System.Net.WebException: The remote server returned an error: (400) Bad Request.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiDeleteRequestSync(Uri uri, ICompanyFileCredentials credentials)
   at MYOB.AccountRight.SDK.Services.MutableService`1.Delete(CompanyFile cf, Guid uid, ICompanyFileCredentials credentials, ErrorLevel errorLevel)
   at LepInvoicer.Implementations.MYOBService.<>c__DisplayClass33_1.<DeleteExistingInvoice>b__1() in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 875
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Implementations.MYOBService.DeleteExistingInvoice(String invoiceNumber) in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 874
[2025-06-17 13:36:29.761 +10:00 WRN] Skipping refund 1837 - cannot delete existing invoice S191871837
[2025-06-17 13:36:29.769 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 13:36:29.882 +10:00 INF] LEP Invoicer completed successfully in 29893ms. Orders: 4, Credits: 0, Refunds: 16
[2025-06-17 13:36:29.891 +10:00 INF] Database connection disposed
[2025-06-17 13:36:29.895 +10:00 INF] LEP Invoicer completed with result: 0
