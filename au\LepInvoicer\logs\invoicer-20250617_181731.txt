[2025-06-17 18:17:31.537 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 18:17:31.563 +10:00 INF] Initializing FastReport...
[2025-06-17 18:17:31.633 +10:00 INF] FastReport initialized successfully
[2025-06-17 18:17:32.016 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 18:17:33.346 +10:00 INF] Starting LEP Invoicer at "2025-06-17T18:17:33.3460555+10:00"
[2025-06-17 18:17:33.375 +10:00 INF] Initializing database service...
[2025-06-17 18:17:33.378 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 18:17:33.490 +10:00 INF] Database connection established successfully
[2025-06-17 18:17:33.491 +10:00 INF] Database service initialized successfully
[2025-06-17 18:17:33.494 +10:00 INF] Checking for pending work...
[2025-06-17 18:17:33.497 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 18:17:34.383 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 18:17:34.386 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 18:17:34.400 +10:00 INF] Found 0 credits to invoice
[2025-06-17 18:17:34.402 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 18:17:34.407 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 18:17:34.408 +10:00 INF] No pending work found
[2025-06-17 18:17:34.410 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 18:17:34.411 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 18:17:34.489 +10:00 INF] LEP Invoicer completed successfully in 1143ms. No work to process.
[2025-06-17 18:17:34.496 +10:00 INF] Database connection disposed
[2025-06-17 18:17:34.498 +10:00 INF] LEP Invoicer completed with result: 0
