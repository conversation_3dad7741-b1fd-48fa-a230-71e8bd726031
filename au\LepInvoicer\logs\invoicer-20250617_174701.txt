[2025-06-17 17:47:01.535 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 17:47:01.574 +10:00 INF] Initializing FastReport...
[2025-06-17 17:47:01.652 +10:00 INF] FastReport initialized successfully
[2025-06-17 17:47:02.180 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 17:47:03.454 +10:00 INF] Starting LEP Invoicer at "2025-06-17T17:47:03.4544866+10:00"
[2025-06-17 17:47:03.460 +10:00 INF] Initializing database service...
[2025-06-17 17:47:03.463 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 17:47:03.583 +10:00 INF] Database connection established successfully
[2025-06-17 17:47:03.588 +10:00 INF] Database service initialized successfully
[2025-06-17 17:47:03.591 +10:00 INF] Checking for pending work...
[2025-06-17 17:47:03.596 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 17:47:04.510 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 17:47:04.512 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 17:47:04.525 +10:00 INF] Found 0 credits to invoice
[2025-06-17 17:47:04.527 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 17:47:04.530 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 17:47:04.531 +10:00 INF] No pending work found
[2025-06-17 17:47:04.533 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 17:47:04.534 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 17:47:04.608 +10:00 INF] LEP Invoicer completed successfully in 1153ms. No work to process.
[2025-06-17 17:47:04.614 +10:00 INF] Database connection disposed
[2025-06-17 17:47:04.616 +10:00 INF] LEP Invoicer completed with result: 0
