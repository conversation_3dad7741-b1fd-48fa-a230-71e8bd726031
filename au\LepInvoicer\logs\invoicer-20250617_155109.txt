[2025-06-17 15:51:09.709 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 15:51:09.737 +10:00 INF] Initializing FastReport...
[2025-06-17 15:51:09.815 +10:00 INF] FastReport initialized successfully
[2025-06-17 15:51:10.277 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 15:51:11.617 +10:00 INF] Starting LEP Invoicer at "2025-06-17T15:51:11.6167402+10:00"
[2025-06-17 15:51:11.620 +10:00 INF] Initializing database service...
[2025-06-17 15:51:11.623 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 15:51:11.726 +10:00 INF] Database connection established successfully
[2025-06-17 15:51:11.727 +10:00 INF] Database service initialized successfully
[2025-06-17 15:51:11.730 +10:00 INF] Checking for pending work...
[2025-06-17 15:51:11.733 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 15:51:12.670 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 15:51:12.673 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 15:51:12.685 +10:00 INF] Found 0 credits to invoice
[2025-06-17 15:51:12.687 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 15:51:12.691 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 15:51:12.692 +10:00 INF] No pending work found
[2025-06-17 15:51:12.694 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 15:51:12.695 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 15:51:12.776 +10:00 INF] LEP Invoicer completed successfully in 1160ms. No work to process.
[2025-06-17 15:51:12.783 +10:00 INF] Database connection disposed
[2025-06-17 15:51:12.785 +10:00 INF] LEP Invoicer completed with result: 0
