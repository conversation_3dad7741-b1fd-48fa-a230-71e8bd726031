# Email Resend Functionality

## Overview
A new function has been added to the LEP Invoicer to resend emails for all successfully invoiced orders after a specific date. This is useful when email addresses were missing during the initial invoicing process but have since been configured.

## Usage

### Command Line Arguments

#### Resend emails for orders after June 9th, 2025 (default):
```bash
dotnet run --resend-emails
```

#### Resend emails for orders after a specific date:
```bash
dotnet run --resend-emails 2025-06-15
```

#### Examples:
```bash
# Resend emails for all orders invoiced after June 9th, 2025
dotnet run --resend-emails

# Resend emails for all orders invoiced after June 15th, 2025
dotnet run --resend-emails 2025-06-15

# Resend emails for all orders invoiced after January 1st, 2025
dotnet run --resend-emails 2025-01-01
```

## What the Function Does

1. **Queries Database**: Finds all orders with `Invoiced2 = 'Y'` (successfully invoiced) after the specified date
2. **Filters Orders**: Excludes orders from ignored customers (same as normal processing)
3. **Checks PDFs**: Verifies that the PDF file exists in the order folder structure (`orders/{yyyyMMdd}/{OrderNr}/Extrafiles/O{orderId}.pdf`)
4. **Determines Email**: Uses the same email logic as normal processing:
   - `EmailPdfAddress` from config (if set)
   - `Customer.AccountEmail` (if available)
   - `Customer.Email` (if available)
5. **Sends Emails**: Attempts to send email for each order with a valid email address
6. **Logs Results**: Provides detailed logging of success/failure counts

## Email Address Requirements

For emails to be sent, one of the following must be configured:

### Option 1: Global Email Override (Quick Fix)
Set `EmailPdfAddress` in `appsettings.json`:
```json
{
  "Invoicer": {
    "EmailPdfAddress": "<EMAIL>"
  }
}
```

### Option 2: Customer Email Addresses
Populate customer email fields in the database:
- `Customer.AccountEmail`
- `Customer.Contact1Email` (not currently checked)
- `Customer.Contact2Email` (not currently checked)
- `Customer.OtherEmail` (not currently checked)

## Output and Logging

The function provides detailed logging:
- Number of orders found
- Success/failure counts
- Email addresses used
- Missing PDFs
- Missing email addresses

Example output:
```
[10:30:00 INF] Starting email resend for orders invoiced after 2025-06-09
[10:30:01 INF] Found 25 successfully invoiced orders to resend emails for
[10:30:05 INF] Email resent successfully for order 1418322 to <EMAIL>
[10:30:06 WRN] No email address found for order 1418170 - skipping
[10:30:10 INF] Email resend completed. Success: 15, Failed: 2, No Email: 8, Total: 25
```

## PDF File Location

The function looks for PDF files in the correct order folder structure:
```
{DataDirectory}/orders/{yyyyMMdd}/{OrderNr}/Extrafiles/O{orderId}.pdf
```

For example:
```
C:\LEPDATA\orders\********\LEP123456\Extrafiles\********.pdf
```

This matches where PDFs are actually saved during normal invoice processing.

## Safety Features

- **PDF Verification**: Only attempts to send emails if the PDF file exists in the correct order folder
- **Email Validation**: Skips orders without valid email addresses
- **Rate Limiting**: 500ms delay between emails to avoid overwhelming the email server
- **Error Handling**: Continues processing even if individual orders fail
- **Detailed Logging**: Comprehensive logging for troubleshooting

## Normal Operation

To run normal invoicing (not email resend):
```bash
dotnet run
```

The application will automatically detect the presence of command-line arguments and choose the appropriate operation mode.
