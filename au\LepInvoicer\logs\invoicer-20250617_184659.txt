[2025-06-17 18:46:59.533 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 18:46:59.560 +10:00 INF] Initializing FastReport...
[2025-06-17 18:46:59.640 +10:00 INF] FastReport initialized successfully
[2025-06-17 18:47:00.286 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 18:47:01.557 +10:00 INF] Starting LEP Invoicer at "2025-06-17T18:47:01.5572359+10:00"
[2025-06-17 18:47:01.561 +10:00 INF] Initializing database service...
[2025-06-17 18:47:01.564 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 18:47:01.666 +10:00 INF] Database connection established successfully
[2025-06-17 18:47:01.668 +10:00 INF] Database service initialized successfully
[2025-06-17 18:47:01.670 +10:00 INF] Checking for pending work...
[2025-06-17 18:47:01.673 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 18:47:02.651 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 18:47:02.654 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 18:47:02.668 +10:00 INF] Found 0 credits to invoice
[2025-06-17 18:47:02.670 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 18:47:02.682 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 18:47:02.683 +10:00 INF] No pending work found
[2025-06-17 18:47:02.684 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 18:47:02.686 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 18:47:02.762 +10:00 INF] LEP Invoicer completed successfully in 1205ms. No work to process.
[2025-06-17 18:47:02.769 +10:00 INF] Database connection disposed
[2025-06-17 18:47:02.770 +10:00 INF] LEP Invoicer completed with result: 0
