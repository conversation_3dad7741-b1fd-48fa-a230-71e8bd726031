[2025-06-17 17:01:15.733 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 17:01:15.773 +10:00 INF] Initializing FastReport...
[2025-06-17 17:01:15.853 +10:00 INF] FastReport initialized successfully
[2025-06-17 17:01:16.301 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 17:01:17.623 +10:00 INF] Starting LEP Invoicer at "2025-06-17T17:01:17.6228683+10:00"
[2025-06-17 17:01:17.630 +10:00 INF] Initializing database service...
[2025-06-17 17:01:17.635 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 17:01:17.746 +10:00 INF] Database connection established successfully
[2025-06-17 17:01:17.748 +10:00 INF] Database service initialized successfully
[2025-06-17 17:01:17.751 +10:00 INF] Checking for pending work...
[2025-06-17 17:01:17.754 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 17:01:18.684 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 17:01:18.688 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 17:01:18.702 +10:00 INF] Found 0 credits to invoice
[2025-06-17 17:01:18.706 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 17:01:18.710 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 17:01:18.712 +10:00 INF] No pending work found
[2025-06-17 17:01:18.718 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 17:01:18.720 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 17:01:18.809 +10:00 INF] LEP Invoicer completed successfully in 1186ms. No work to process.
[2025-06-17 17:01:18.816 +10:00 INF] Database connection disposed
[2025-06-17 17:01:18.818 +10:00 INF] LEP Invoicer completed with result: 0
