[2025-06-17 18:20:47.506 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 18:20:47.544 +10:00 INF] Initializing FastReport...
[2025-06-17 18:20:47.615 +10:00 INF] FastReport initialized successfully
[2025-06-17 18:20:48.038 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 18:20:49.374 +10:00 INF] Starting LEP Invoicer at "2025-06-17T18:20:49.3741171+10:00"
[2025-06-17 18:20:49.377 +10:00 INF] Initializing database service...
[2025-06-17 18:20:49.381 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 18:20:49.477 +10:00 INF] Database connection established successfully
[2025-06-17 18:20:49.478 +10:00 INF] Database service initialized successfully
[2025-06-17 18:20:49.483 +10:00 INF] Checking for pending work...
[2025-06-17 18:20:49.486 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 18:20:50.420 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 18:20:50.422 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 18:20:50.436 +10:00 INF] Found 0 credits to invoice
[2025-06-17 18:20:50.438 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 18:20:50.444 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 18:20:50.445 +10:00 INF] No pending work found
[2025-06-17 18:20:50.446 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 18:20:50.448 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 18:20:50.532 +10:00 INF] LEP Invoicer completed successfully in 1157ms. No work to process.
[2025-06-17 18:20:50.539 +10:00 INF] Database connection disposed
[2025-06-17 18:20:50.541 +10:00 INF] LEP Invoicer completed with result: 0
