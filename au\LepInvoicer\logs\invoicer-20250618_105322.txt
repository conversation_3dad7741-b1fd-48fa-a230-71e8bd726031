[2025-06-18 10:53:22.882 +10:00 INF] Starting LEP Invoicer application
[2025-06-18 10:53:22.927 +10:00 INF] Initializing FastReport...
[2025-06-18 10:53:23.050 +10:00 INF] FastReport initialized successfully
[2025-06-18 10:53:23.813 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-18 10:53:25.266 +10:00 INF] Starting LEP Invoicer at "2025-06-18T10:53:25.2660040+10:00"
[2025-06-18 10:53:25.270 +10:00 INF] Initializing database service...
[2025-06-18 10:53:25.273 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-18 10:53:25.386 +10:00 INF] Database connection established successfully
[2025-06-18 10:53:25.388 +10:00 INF] Database service initialized successfully
[2025-06-18 10:53:25.391 +10:00 INF] Checking for pending work...
[2025-06-18 10:53:25.395 +10:00 INF] Getting 500 orders to invoice
[2025-06-18 10:53:26.076 +10:00 INF] Found 0 orders to invoice (marked 0 zero-priced as invoiced, filtered 0 candidates)
[2025-06-18 10:53:26.079 +10:00 INF] Getting 100 credits to invoice
[2025-06-18 10:53:26.094 +10:00 INF] Found 0 credits to invoice
[2025-06-18 10:53:26.096 +10:00 INF] Getting 100 refunds to invoice
[2025-06-18 10:53:26.100 +10:00 INF] Found 0 refunds to invoice
[2025-06-18 10:53:26.102 +10:00 INF] No pending work found
[2025-06-18 10:53:26.103 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-18 10:53:26.105 +10:00 INF] Cleaning up invoicer logs...
[2025-06-18 10:53:26.188 +10:00 INF] LEP Invoicer completed successfully in 922ms. No work to process.
[2025-06-18 10:53:26.195 +10:00 INF] Database connection disposed
[2025-06-18 10:53:26.197 +10:00 INF] LEP Invoicer completed with result: 0
