[2025-06-17 15:56:37.390 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 15:56:37.417 +10:00 INF] Initializing FastReport...
[2025-06-17 15:56:37.490 +10:00 INF] FastReport initialized successfully
[2025-06-17 15:56:37.959 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 15:56:39.246 +10:00 INF] Starting LEP Invoicer at "2025-06-17T15:56:39.2466696+10:00"
[2025-06-17 15:56:39.250 +10:00 INF] Initializing database service...
[2025-06-17 15:56:39.255 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 15:56:39.353 +10:00 INF] Database connection established successfully
[2025-06-17 15:56:39.354 +10:00 INF] Database service initialized successfully
[2025-06-17 15:56:39.357 +10:00 INF] Checking for pending work...
[2025-06-17 15:56:39.359 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 15:56:40.218 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 15:56:40.221 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 15:56:40.234 +10:00 INF] Found 0 credits to invoice
[2025-06-17 15:56:40.238 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 15:56:40.251 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 15:56:40.254 +10:00 INF] No pending work found
[2025-06-17 15:56:40.256 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 15:56:40.259 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 15:56:40.360 +10:00 INF] LEP Invoicer completed successfully in 1113ms. No work to process.
[2025-06-17 15:56:40.366 +10:00 INF] Database connection disposed
[2025-06-17 15:56:40.368 +10:00 INF] LEP Invoicer completed with result: 0
