[2025-06-18 10:35:27.644 +10:00 INF] Starting LEP Invoicer application
[2025-06-18 10:35:27.688 +10:00 INF] Initializing FastReport...
[2025-06-18 10:35:27.762 +10:00 INF] FastReport initialized successfully
[2025-06-18 10:35:28.172 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-18 10:35:29.480 +10:00 INF] Starting LEP Invoicer at "2025-06-18T10:35:29.4798308+10:00"
[2025-06-18 10:35:29.483 +10:00 INF] Initializing database service...
[2025-06-18 10:35:29.486 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-18 10:35:29.595 +10:00 INF] Database connection established successfully
[2025-06-18 10:35:29.596 +10:00 INF] Database service initialized successfully
[2025-06-18 10:35:29.598 +10:00 INF] Checking for pending work...
[2025-06-18 10:35:29.601 +10:00 INF] Getting 500 orders to invoice
[2025-06-18 10:35:30.473 +10:00 INF] Found 0 orders to invoice (filtered 17 candidates)
[2025-06-18 10:35:30.476 +10:00 INF] Getting 100 credits to invoice
[2025-06-18 10:35:30.489 +10:00 INF] Found 0 credits to invoice
[2025-06-18 10:35:30.491 +10:00 INF] Getting 100 refunds to invoice
[2025-06-18 10:35:30.494 +10:00 INF] Found 0 refunds to invoice
[2025-06-18 10:35:30.496 +10:00 INF] No pending work found
[2025-06-18 10:35:30.497 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-18 10:35:30.498 +10:00 INF] Cleaning up invoicer logs...
[2025-06-18 10:35:30.591 +10:00 INF] LEP Invoicer completed successfully in 1111ms. No work to process.
[2025-06-18 10:35:30.597 +10:00 INF] Database connection disposed
[2025-06-18 10:35:30.604 +10:00 INF] LEP Invoicer completed with result: 0
