[2025-06-18 10:44:19.984 +10:00 INF] Starting LEP Invoicer application
[2025-06-18 10:44:20.017 +10:00 INF] Initializing FastReport...
[2025-06-18 10:44:20.097 +10:00 INF] FastReport initialized successfully
[2025-06-18 10:44:20.541 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-18 10:44:22.206 +10:00 INF] Starting LEP Invoicer at "2025-06-18T10:44:22.2057383+10:00"
[2025-06-18 10:44:22.211 +10:00 INF] Initializing database service...
[2025-06-18 10:44:22.214 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-18 10:44:22.332 +10:00 INF] Database connection established successfully
[2025-06-18 10:44:22.334 +10:00 INF] Database service initialized successfully
[2025-06-18 10:44:22.336 +10:00 INF] Checking for pending work...
[2025-06-18 10:44:22.339 +10:00 INF] Getting 500 orders to invoice
[2025-06-18 10:44:23.319 +10:00 INF] Found 0 orders to invoice (filtered 17 candidates)
[2025-06-18 10:44:23.321 +10:00 INF] Getting 100 credits to invoice
[2025-06-18 10:44:23.335 +10:00 INF] Found 0 credits to invoice
[2025-06-18 10:44:23.337 +10:00 INF] Getting 100 refunds to invoice
[2025-06-18 10:44:23.340 +10:00 INF] Found 0 refunds to invoice
[2025-06-18 10:44:23.342 +10:00 INF] No pending work found
[2025-06-18 10:44:23.343 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-18 10:44:23.344 +10:00 INF] Cleaning up invoicer logs...
[2025-06-18 10:44:23.422 +10:00 INF] LEP Invoicer completed successfully in 1216ms. No work to process.
[2025-06-18 10:44:23.428 +10:00 INF] Database connection disposed
[2025-06-18 10:44:23.434 +10:00 INF] LEP Invoicer completed with result: 0
