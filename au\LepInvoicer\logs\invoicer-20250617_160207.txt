[2025-06-17 16:02:07.499 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 16:02:07.526 +10:00 INF] Initializing FastReport...
[2025-06-17 16:02:07.595 +10:00 INF] FastReport initialized successfully
[2025-06-17 16:02:08.022 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 16:02:09.484 +10:00 INF] Starting LEP Invoicer at "2025-06-17T16:02:09.4838522+10:00"
[2025-06-17 16:02:09.489 +10:00 INF] Initializing database service...
[2025-06-17 16:02:09.491 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 16:02:09.598 +10:00 INF] Database connection established successfully
[2025-06-17 16:02:09.600 +10:00 INF] Database service initialized successfully
[2025-06-17 16:02:09.602 +10:00 INF] Checking for pending work...
[2025-06-17 16:02:09.605 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 16:02:10.528 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 16:02:10.531 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 16:02:10.543 +10:00 INF] Found 0 credits to invoice
[2025-06-17 16:02:10.545 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 16:02:10.549 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 16:02:10.551 +10:00 INF] No pending work found
[2025-06-17 16:02:10.552 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 16:02:10.553 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 16:02:10.632 +10:00 INF] LEP Invoicer completed successfully in 1148ms. No work to process.
[2025-06-17 16:02:10.638 +10:00 INF] Database connection disposed
[2025-06-17 16:02:10.640 +10:00 INF] LEP Invoicer completed with result: 0
