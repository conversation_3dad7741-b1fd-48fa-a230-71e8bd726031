[2025-06-17 16:30:38.614 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 16:30:38.642 +10:00 INF] Initializing FastReport...
[2025-06-17 16:30:38.717 +10:00 INF] FastReport initialized successfully
[2025-06-17 16:30:39.217 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 16:30:40.990 +10:00 INF] Starting LEP Invoicer at "2025-06-17T16:30:40.9897789+10:00"
[2025-06-17 16:30:40.993 +10:00 INF] Initializing database service...
[2025-06-17 16:30:40.996 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 16:30:41.103 +10:00 INF] Database connection established successfully
[2025-06-17 16:30:41.105 +10:00 INF] Database service initialized successfully
[2025-06-17 16:30:41.107 +10:00 INF] Checking for pending work...
[2025-06-17 16:30:41.111 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 16:30:41.996 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 16:30:41.998 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 16:30:42.011 +10:00 INF] Found 0 credits to invoice
[2025-06-17 16:30:42.013 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 16:30:42.016 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 16:30:42.018 +10:00 INF] No pending work found
[2025-06-17 16:30:42.019 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 16:30:42.022 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 16:30:42.141 +10:00 INF] LEP Invoicer completed successfully in 1151ms. No work to process.
[2025-06-17 16:30:42.147 +10:00 INF] Database connection disposed
[2025-06-17 16:30:42.149 +10:00 INF] LEP Invoicer completed with result: 0
