#region

using lep.job;
using lep.run;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;

#endregion

namespace lep.user.impl
{
	[Serializable]
    public class RunSearchCriteria : IRunSearchCriteria
    {
        public RunSearchCriteria()
        {
        }

        public virtual int Id { get; set; }

        //public virtual string RunStatus {get; set; }
        public virtual HashSet<RunStatusOptions> RunStatus { get; set; } = new HashSet<RunStatusOptions>();

        public virtual bool IsSearchPanelOpen { get; set; }
        public virtual string Customer { get; set; } = String.Empty;
        public virtual string OrderNr { get; set; } = String.Empty;
        public virtual string JobNr { get; set; } = String.Empty;
        public virtual string RunNr { get; set; } = String.Empty;
        public virtual bool IsUrgent { get; set; }
        public virtual bool IsOnHold { get; set; }
        public virtual RunSearchOptions RunSearchOption { get; set; }
        public virtual IPaperSize Size { get; set; }
        public virtual RunCelloglazeOptions? Cello { get; set; }
        public virtual int? Side { get; set; }
        public virtual IStock Stock { get; set; }
		public virtual string StockKind { get; set; }
		public virtual string Ordering { get; set; } = String.Empty;
        public virtual string RunOrdering { get; set; } = String.Empty;

        // New properties for Category and Template filters
        public virtual HashSet<int> JobTypes { get; set; } = new HashSet<int>();
        public virtual int? JobType { get; set; }

        public virtual HashSet<int> OpenRun { get; set; } = new HashSet<int>();

        [JsonIgnore]
        public virtual IUser Staff { get; set; }

        public virtual Facility Facility { get; set; }
    }
}
