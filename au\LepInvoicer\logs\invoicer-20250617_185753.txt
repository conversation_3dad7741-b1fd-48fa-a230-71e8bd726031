[2025-06-17 18:57:53.630 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 18:57:53.661 +10:00 INF] Initializing FastReport...
[2025-06-17 18:57:53.750 +10:00 INF] FastReport initialized successfully
[2025-06-17 18:57:54.186 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 18:57:55.508 +10:00 INF] Starting LEP Invoicer at "2025-06-17T18:57:55.5084746+10:00"
[2025-06-17 18:57:55.512 +10:00 INF] Initializing database service...
[2025-06-17 18:57:55.515 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 18:57:55.618 +10:00 INF] Database connection established successfully
[2025-06-17 18:57:55.627 +10:00 INF] Database service initialized successfully
[2025-06-17 18:57:55.637 +10:00 INF] Checking for pending work...
[2025-06-17 18:57:55.641 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 18:57:56.607 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 18:57:56.610 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 18:57:56.623 +10:00 INF] Found 0 credits to invoice
[2025-06-17 18:57:56.625 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 18:57:56.631 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 18:57:56.632 +10:00 INF] No pending work found
[2025-06-17 18:57:56.633 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 18:57:56.635 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 18:57:56.715 +10:00 INF] LEP Invoicer completed successfully in 1206ms. No work to process.
[2025-06-17 18:57:56.722 +10:00 INF] Database connection disposed
[2025-06-17 18:57:56.724 +10:00 INF] LEP Invoicer completed with result: 0
