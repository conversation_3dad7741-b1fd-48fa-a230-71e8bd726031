﻿using System;
using System.Runtime.InteropServices;

namespace lep
{
	public static class ShellNotification
    {
        [DllImport("shell32.dll", ExactSpelling = true)]
        public static extern void ILFree(IntPtr pidlList);

        [DllImport("shell32.dll", CharSet = CharSet.Auto)]
        public static extern Int32 SHParseDisplayName(
            [MarshalAs(UnmanagedType.LPWStr)] String pszName,
            IntPtr pbc,
            out IntPtr ppidl,
            UInt32 sfgaoIn,
            out UInt32 psfgaoOut);

        [DllImport("shell32.dll", CharSet = CharSet.Auto)]
        internal static extern void SHChangeNotify(
            UInt32 wEventId,
            UInt32 uFlags,
            IntPtr dwItem1,
            IntPtr dwItem2);

        [Flags]
        private enum ShellChangeNotificationEvents : uint
        {
            //...
            SHCNE_UPDATEITEM = 0x00002000,

            //...
        }

        [Flags]
        private enum ShellChangeNotificationFlags
        {
            //...
            SHCNF_FLUSH = 0x1000,

            //...
        }

        public static void refreshThumbnail(string path)
        {
            try
            {
                uint iAttribute;
                IntPtr pidl;
                SHParseDisplayName(path, IntPtr.Zero, out pidl, 0, out iAttribute);
                SHChangeNotify(
                    (uint)ShellChangeNotificationEvents.SHCNE_UPDATEITEM,
                    (uint)ShellChangeNotificationFlags.SHCNF_FLUSH,
                    pidl,
                    IntPtr.Zero);

                ILFree(pidl);
            }
            catch (Exception ex)
            {
                ///Console.WriteLine(ex.Message);
            }
        }
    }
}