[2025-06-17 16:37:13.528 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 16:37:13.559 +10:00 INF] Initializing FastReport...
[2025-06-17 16:37:13.630 +10:00 INF] FastReport initialized successfully
[2025-06-17 16:37:14.078 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 16:37:15.325 +10:00 INF] Starting LEP Invoicer at "2025-06-17T16:37:15.3252090+10:00"
[2025-06-17 16:37:15.329 +10:00 INF] Initializing database service...
[2025-06-17 16:37:15.346 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 16:37:15.461 +10:00 INF] Database connection established successfully
[2025-06-17 16:37:15.463 +10:00 INF] Database service initialized successfully
[2025-06-17 16:37:15.466 +10:00 INF] Checking for pending work...
[2025-06-17 16:37:15.469 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 16:37:16.361 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 16:37:16.365 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 16:37:16.383 +10:00 INF] Found 0 credits to invoice
[2025-06-17 16:37:16.388 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 16:37:16.392 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 16:37:16.396 +10:00 INF] No pending work found
[2025-06-17 16:37:16.398 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 16:37:16.400 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 16:37:16.482 +10:00 INF] LEP Invoicer completed successfully in 1157ms. No work to process.
[2025-06-17 16:37:16.496 +10:00 INF] Database connection disposed
[2025-06-17 16:37:16.498 +10:00 INF] LEP Invoicer completed with result: 0
