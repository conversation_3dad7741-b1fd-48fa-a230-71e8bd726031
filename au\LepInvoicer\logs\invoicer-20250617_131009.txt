[2025-06-17 13:10:09.343 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 13:10:09.370 +10:00 INF] Initializing FastReport...
[2025-06-17 13:10:09.472 +10:00 INF] FastReport initialized successfully
[2025-06-17 13:10:10.128 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 13:10:11.945 +10:00 INF] Starting LEP Invoicer at "2025-06-17T13:10:11.9454447+10:00"
[2025-06-17 13:10:11.950 +10:00 INF] Initializing database service...
[2025-06-17 13:10:11.953 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 13:10:12.125 +10:00 INF] Database connection established successfully
[2025-06-17 13:10:12.127 +10:00 INF] Database service initialized successfully
[2025-06-17 13:10:12.130 +10:00 INF] Checking for pending work...
[2025-06-17 13:10:12.134 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 13:10:13.228 +10:00 INF] Found 10 orders to invoice (filtered 25 candidates)
[2025-06-17 13:10:13.231 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 13:10:13.256 +10:00 INF] Found 0 credits to invoice
[2025-06-17 13:10:13.259 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 13:10:13.282 +10:00 INF] Found 16 refunds to invoice
[2025-06-17 13:10:13.283 +10:00 INF] Found pending work: 10 orders, 16 refunds
[2025-06-17 13:10:13.285 +10:00 INF] Initializing MYOB and other services...
[2025-06-17 13:10:13.290 +10:00 INF] Initializing MYOB service...
[2025-06-17 13:10:13.292 +10:00 INF] Initializing MYOB with OAuth authentication
[2025-06-17 13:10:13.295 +10:00 INF] Starting OAuth authentication flow
[2025-06-17 13:10:13.327 +10:00 INF] Using existing OAuth tokens
[2025-06-17 13:10:13.328 +10:00 INF] Initializing MYOB services with OAuth keystore
[2025-06-17 13:10:13.335 +10:00 INF] MYOB services initialized successfully
[2025-06-17 13:10:13.336 +10:00 INF] OAuth keystore set for API calls
[2025-06-17 13:10:13.338 +10:00 INF] Getting company files from MYOB
[2025-06-17 13:10:13.620 +10:00 INF] MYOB service initialized with OAuth credentials. Using company file: LEP Colour Printers Pty Ltd
[2025-06-17 13:10:14.505 +10:00 INF] MYOB service initialized successfully with company file: LEP Colour Printers Pty Ltd
[2025-06-17 13:10:14.511 +10:00 INF] All services initialized successfully
[2025-06-17 13:10:14.520 +10:00 INF] Processing order invoices...
[2025-06-17 13:10:14.521 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 13:10:14.858 +10:00 INF] Found 10 orders to invoice (filtered 25 candidates)
[2025-06-17 13:10:14.864 +10:00 INF] Found 10 orders to process
[2025-06-17 13:10:14.874 +10:00 INF] Getting order 1418120
[2025-06-17 13:10:14.979 +10:00 INF] Processing order 1418120 with 1 jobs, total: ¤34.50
[2025-06-17 13:10:14.984 +10:00 INF] Creating order invoice for order 1418120
[2025-06-17 13:10:16.186 +10:00 INF] Successfully created MYOB invoice O1418120 for order 1418120
[2025-06-17 13:10:16.188 +10:00 INF] Successfully created MYOB invoice for order 1418120
[2025-06-17 13:10:16.204 +10:00 INF] Generating PDF invoice for order 1418120 at \\dfs01\resource\invoices\2025/Jun/17\O1418120.pdf
[2025-06-17 13:10:21.826 +10:00 INF] Successfully generated PDF for order 1418120
[2025-06-17 13:10:21.861 +10:00 WRN] No email address found for order 1418120
[2025-06-17 13:10:21.873 +10:00 INF] Successfully processed order 1418120 (MYOB + PDF)
[2025-06-17 13:10:21.894 +10:00 INF] Getting order 1418052
[2025-06-17 13:10:21.903 +10:00 INF] Processing order 1418052 with 2 jobs, total: ¤89.06
[2025-06-17 13:10:21.905 +10:00 INF] Creating order invoice for order 1418052
[2025-06-17 13:10:24.318 +10:00 INF] Successfully created MYOB invoice O1418052 for order 1418052
[2025-06-17 13:10:24.320 +10:00 INF] Successfully created MYOB invoice for order 1418052
[2025-06-17 13:10:24.322 +10:00 INF] Generating PDF invoice for order 1418052 at \\dfs01\resource\invoices\2025/Jun/17\O1418052.pdf
[2025-06-17 13:10:24.807 +10:00 INF] Successfully generated PDF for order 1418052
[2025-06-17 13:10:24.854 +10:00 WRN] No email address found for order 1418052
[2025-06-17 13:10:24.857 +10:00 INF] Successfully processed order 1418052 (MYOB + PDF)
[2025-06-17 13:10:24.861 +10:00 INF] Getting order 1418014
[2025-06-17 13:10:24.868 +10:00 INF] Processing order 1418014 with 5 jobs, total: ¤311.76
[2025-06-17 13:10:24.870 +10:00 INF] Creating order invoice for order 1418014
[2025-06-17 13:10:26.451 +10:00 INF] Successfully created MYOB invoice O1418014 for order 1418014
[2025-06-17 13:10:26.453 +10:00 INF] Successfully created MYOB invoice for order 1418014
[2025-06-17 13:10:26.456 +10:00 INF] Generating PDF invoice for order 1418014 at \\dfs01\resource\invoices\2025/Jun/17\O1418014.pdf
[2025-06-17 13:10:26.949 +10:00 INF] Successfully generated PDF for order 1418014
[2025-06-17 13:10:27.016 +10:00 WRN] No email address found for order 1418014
[2025-06-17 13:10:27.021 +10:00 INF] Successfully processed order 1418014 (MYOB + PDF)
[2025-06-17 13:10:27.024 +10:00 INF] Getting order 1417920
[2025-06-17 13:10:27.030 +10:00 INF] Processing order 1417920 with 1 jobs, total: ¤70.36
[2025-06-17 13:10:27.032 +10:00 INF] Creating order invoice for order 1417920
[2025-06-17 13:10:28.411 +10:00 INF] Successfully created MYOB invoice O1417920 for order 1417920
[2025-06-17 13:10:28.414 +10:00 INF] Successfully created MYOB invoice for order 1417920
[2025-06-17 13:10:28.416 +10:00 INF] Generating PDF invoice for order 1417920 at \\dfs01\resource\invoices\2025/Jun/17\O1417920.pdf
[2025-06-17 13:10:28.907 +10:00 INF] Successfully generated PDF for order 1417920
[2025-06-17 13:10:28.945 +10:00 WRN] No email address found for order 1417920
[2025-06-17 13:10:28.951 +10:00 INF] Successfully processed order 1417920 (MYOB + PDF)
[2025-06-17 13:10:28.960 +10:00 INF] Getting order 1417750
[2025-06-17 13:10:28.965 +10:00 INF] Processing order 1417750 with 1 jobs, total: ¤82.90
[2025-06-17 13:10:28.968 +10:00 INF] Creating order invoice for order 1417750
[2025-06-17 13:10:30.621 +10:00 INF] Successfully created MYOB invoice O1417750 for order 1417750
[2025-06-17 13:10:30.623 +10:00 INF] Successfully created MYOB invoice for order 1417750
[2025-06-17 13:10:30.625 +10:00 INF] Generating PDF invoice for order 1417750 at \\dfs01\resource\invoices\2025/Jun/17\O1417750.pdf
[2025-06-17 13:10:30.985 +10:00 INF] Successfully generated PDF for order 1417750
[2025-06-17 13:10:31.103 +10:00 WRN] No email address found for order 1417750
[2025-06-17 13:10:31.107 +10:00 INF] Successfully processed order 1417750 (MYOB + PDF)
[2025-06-17 13:10:31.111 +10:00 INF] Getting order 1417746
[2025-06-17 13:10:31.116 +10:00 INF] Processing order 1417746 with 5 jobs, total: ¤289.33
[2025-06-17 13:10:31.127 +10:00 INF] Creating order invoice for order 1417746
[2025-06-17 13:10:32.736 +10:00 INF] Successfully created MYOB invoice O1417746 for order 1417746
[2025-06-17 13:10:32.739 +10:00 INF] Successfully created MYOB invoice for order 1417746
[2025-06-17 13:10:32.749 +10:00 INF] Generating PDF invoice for order 1417746 at \\dfs01\resource\invoices\2025/Jun/17\O1417746.pdf
[2025-06-17 13:10:33.277 +10:00 INF] Successfully generated PDF for order 1417746
[2025-06-17 13:10:33.335 +10:00 WRN] No email address found for order 1417746
[2025-06-17 13:10:33.344 +10:00 INF] Successfully processed order 1417746 (MYOB + PDF)
[2025-06-17 13:10:33.348 +10:00 INF] Getting order 1417710
[2025-06-17 13:10:33.354 +10:00 INF] Processing order 1417710 with 2 jobs, total: ¤76.29
[2025-06-17 13:10:33.357 +10:00 INF] Creating order invoice for order 1417710
[2025-06-17 13:10:34.823 +10:00 INF] Successfully created MYOB invoice O1417710 for order 1417710
[2025-06-17 13:10:34.824 +10:00 INF] Successfully created MYOB invoice for order 1417710
[2025-06-17 13:10:34.826 +10:00 INF] Generating PDF invoice for order 1417710 at \\dfs01\resource\invoices\2025/Jun/17\O1417710.pdf
[2025-06-17 13:10:35.250 +10:00 INF] Successfully generated PDF for order 1417710
[2025-06-17 13:10:35.278 +10:00 WRN] No email address found for order 1417710
[2025-06-17 13:10:35.289 +10:00 INF] Successfully processed order 1417710 (MYOB + PDF)
[2025-06-17 13:10:35.293 +10:00 INF] Getting order 1417701
[2025-06-17 13:10:35.310 +10:00 INF] Processing order 1417701 with 1 jobs, total: ¤78.76
[2025-06-17 13:10:35.314 +10:00 INF] Creating order invoice for order 1417701
[2025-06-17 13:10:36.868 +10:00 INF] Successfully created MYOB invoice O1417701 for order 1417701
[2025-06-17 13:10:36.870 +10:00 INF] Successfully created MYOB invoice for order 1417701
[2025-06-17 13:10:36.873 +10:00 INF] Generating PDF invoice for order 1417701 at \\dfs01\resource\invoices\2025/Jun/17\O1417701.pdf
[2025-06-17 13:10:37.215 +10:00 INF] Successfully generated PDF for order 1417701
[2025-06-17 13:10:37.257 +10:00 WRN] No email address found for order 1417701
[2025-06-17 13:10:37.260 +10:00 INF] Successfully processed order 1417701 (MYOB + PDF)
[2025-06-17 13:10:37.263 +10:00 INF] Getting order 1416773
[2025-06-17 13:10:37.267 +10:00 INF] Processing order 1416773 with 1 jobs, total: ¤52.39
[2025-06-17 13:10:37.269 +10:00 INF] Creating order invoice for order 1416773
[2025-06-17 13:10:38.995 +10:00 INF] Successfully created MYOB invoice O1416773 for order 1416773
[2025-06-17 13:10:38.997 +10:00 INF] Successfully created MYOB invoice for order 1416773
[2025-06-17 13:10:38.998 +10:00 INF] Generating PDF invoice for order 1416773 at \\dfs01\resource\invoices\2025/Jun/17\O1416773.pdf
[2025-06-17 13:10:39.481 +10:00 INF] Successfully generated PDF for order 1416773
[2025-06-17 13:10:39.519 +10:00 WRN] No email address found for order 1416773
[2025-06-17 13:10:39.527 +10:00 INF] Successfully processed order 1416773 (MYOB + PDF)
[2025-06-17 13:10:39.531 +10:00 INF] Getting order 1416501
[2025-06-17 13:10:39.537 +10:00 INF] Processing order 1416501 with 2 jobs, total: ¤201.79
[2025-06-17 13:10:39.540 +10:00 INF] Creating order invoice for order 1416501
[2025-06-17 13:10:45.833 +10:00 INF] Successfully created MYOB invoice O1416501 for order 1416501
[2025-06-17 13:10:45.836 +10:00 INF] Successfully created MYOB invoice for order 1416501
[2025-06-17 13:10:45.837 +10:00 INF] Generating PDF invoice for order 1416501 at \\dfs01\resource\invoices\2025/Jun/17\O1416501.pdf
[2025-06-17 13:10:46.247 +10:00 INF] Successfully generated PDF for order 1416501
[2025-06-17 13:10:46.278 +10:00 WRN] No email address found for order 1416501
[2025-06-17 13:10:46.285 +10:00 INF] Successfully processed order 1416501 (MYOB + PDF)
[2025-06-17 13:10:46.288 +10:00 INF] Order processing completed. Processed: 10, Success: 10, Failed: 0
[2025-06-17 13:10:46.293 +10:00 INF] Processing credit invoices...
[2025-06-17 13:10:46.294 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 13:10:46.299 +10:00 INF] Found 0 credits to invoice
[2025-06-17 13:10:46.314 +10:00 INF] Processing refund invoices...
[2025-06-17 13:10:46.316 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 13:10:46.322 +10:00 INF] Found 16 refunds to invoice
[2025-06-17 13:10:46.335 +10:00 INF] Creating refund invoice for refund 1801, Amount: ¤1.69
[2025-06-17 13:10:48.323 +10:00 INF] Deleting existing invoice S248751801
[2025-06-17 13:10:48.709 +10:00 WRN] Failed to delete existing invoice S248751801 - invoice may be locked/paid in MYOB
MYOB.AccountRight.SDK.ApiValidationException: Encountered a validation error (https://arl2.api.myob.com/accountright/b2f20bad-65c4-4dd3-a921-0a20ccfffb73/Sale/Invoice/Service/ae7fcbd9-f0a0-4771-83a9-3dcdea23f5cb)
 ---> System.Net.WebException: The remote server returned an error: (400) Bad Request.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiDeleteRequestSync(Uri uri, ICompanyFileCredentials credentials)
   at MYOB.AccountRight.SDK.Services.MutableService`1.Delete(CompanyFile cf, Guid uid, ICompanyFileCredentials credentials, ErrorLevel errorLevel)
   at LepInvoicer.Implementations.MYOBService.<>c__DisplayClass33_1.<DeleteExistingInvoice>b__1() in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 875
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Implementations.MYOBService.DeleteExistingInvoice(String invoiceNumber) in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 874
[2025-06-17 13:10:48.747 +10:00 WRN] Skipping refund 1801 - cannot delete existing invoice S248751801
[2025-06-17 13:10:48.763 +10:00 INF] Creating refund invoice for refund 1802, Amount: ¤4.59
[2025-06-17 13:10:49.332 +10:00 INF] Deleting existing invoice S139771802
[2025-06-17 13:10:50.607 +10:00 WRN] Failed to delete existing invoice S139771802 - invoice may be locked/paid in MYOB
MYOB.AccountRight.SDK.ApiValidationException: Encountered a validation error (https://arl2.api.myob.com/accountright/b2f20bad-65c4-4dd3-a921-0a20ccfffb73/Sale/Invoice/Service/61295f40-7707-4574-825e-84da93a4b016)
 ---> System.Net.WebException: The remote server returned an error: (400) Bad Request.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiDeleteRequestSync(Uri uri, ICompanyFileCredentials credentials)
   at MYOB.AccountRight.SDK.Services.MutableService`1.Delete(CompanyFile cf, Guid uid, ICompanyFileCredentials credentials, ErrorLevel errorLevel)
   at LepInvoicer.Implementations.MYOBService.<>c__DisplayClass33_1.<DeleteExistingInvoice>b__1() in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 875
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Implementations.MYOBService.DeleteExistingInvoice(String invoiceNumber) in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 874
[2025-06-17 13:10:50.629 +10:00 WRN] Skipping refund 1802 - cannot delete existing invoice S139771802
[2025-06-17 13:10:50.637 +10:00 INF] Creating refund invoice for refund 1803, Amount: ¤9.27
[2025-06-17 13:10:51.073 +10:00 INF] Deleting existing invoice S150791803
[2025-06-17 13:10:51.426 +10:00 WRN] Failed to delete existing invoice S150791803 - invoice may be locked/paid in MYOB
MYOB.AccountRight.SDK.ApiValidationException: Encountered a validation error (https://arl2.api.myob.com/accountright/b2f20bad-65c4-4dd3-a921-0a20ccfffb73/Sale/Invoice/Service/ef2d3cc0-43a4-4c63-95e9-76389d058325)
 ---> System.Net.WebException: The remote server returned an error: (400) Bad Request.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiDeleteRequestSync(Uri uri, ICompanyFileCredentials credentials)
   at MYOB.AccountRight.SDK.Services.MutableService`1.Delete(CompanyFile cf, Guid uid, ICompanyFileCredentials credentials, ErrorLevel errorLevel)
   at LepInvoicer.Implementations.MYOBService.<>c__DisplayClass33_1.<DeleteExistingInvoice>b__1() in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 875
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Implementations.MYOBService.DeleteExistingInvoice(String invoiceNumber) in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 874
[2025-06-17 13:10:51.436 +10:00 WRN] Skipping refund 1803 - cannot delete existing invoice S150791803
[2025-06-17 13:10:51.442 +10:00 INF] Creating refund invoice for refund 1804, Amount: ¤11.23
[2025-06-17 13:10:52.871 +10:00 INF] Deleting existing invoice S150791804
[2025-06-17 13:10:53.259 +10:00 WRN] Failed to delete existing invoice S150791804 - invoice may be locked/paid in MYOB
MYOB.AccountRight.SDK.ApiValidationException: Encountered a validation error (https://arl2.api.myob.com/accountright/b2f20bad-65c4-4dd3-a921-0a20ccfffb73/Sale/Invoice/Service/903aeac6-182f-4aae-b4c4-d8e81708ec76)
 ---> System.Net.WebException: The remote server returned an error: (400) Bad Request.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiDeleteRequestSync(Uri uri, ICompanyFileCredentials credentials)
   at MYOB.AccountRight.SDK.Services.MutableService`1.Delete(CompanyFile cf, Guid uid, ICompanyFileCredentials credentials, ErrorLevel errorLevel)
   at LepInvoicer.Implementations.MYOBService.<>c__DisplayClass33_1.<DeleteExistingInvoice>b__1() in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 875
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Implementations.MYOBService.DeleteExistingInvoice(String invoiceNumber) in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 874
[2025-06-17 13:10:53.289 +10:00 WRN] Skipping refund 1804 - cannot delete existing invoice S150791804
[2025-06-17 13:10:53.309 +10:00 INF] Creating refund invoice for refund 1805, Amount: ¤32.62
[2025-06-17 13:10:53.605 +10:00 INF] Deleting existing invoice S252801805
[2025-06-17 13:10:53.964 +10:00 WRN] Failed to delete existing invoice S252801805 - invoice may be locked/paid in MYOB
MYOB.AccountRight.SDK.ApiValidationException: Encountered a validation error (https://arl2.api.myob.com/accountright/b2f20bad-65c4-4dd3-a921-0a20ccfffb73/Sale/Invoice/Service/6942f99f-e33c-424d-861a-2215ef9a1e09)
 ---> System.Net.WebException: The remote server returned an error: (400) Bad Request.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiDeleteRequestSync(Uri uri, ICompanyFileCredentials credentials)
   at MYOB.AccountRight.SDK.Services.MutableService`1.Delete(CompanyFile cf, Guid uid, ICompanyFileCredentials credentials, ErrorLevel errorLevel)
   at LepInvoicer.Implementations.MYOBService.<>c__DisplayClass33_1.<DeleteExistingInvoice>b__1() in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 875
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Implementations.MYOBService.DeleteExistingInvoice(String invoiceNumber) in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 874
[2025-06-17 13:10:53.998 +10:00 WRN] Skipping refund 1805 - cannot delete existing invoice S252801805
[2025-06-17 13:10:54.011 +10:00 INF] Creating refund invoice for refund 1806, Amount: ¤36.12
[2025-06-17 13:10:54.491 +10:00 INF] Deleting existing invoice S141881806
[2025-06-17 13:10:54.983 +10:00 WRN] Failed to delete existing invoice S141881806 - invoice may be locked/paid in MYOB
MYOB.AccountRight.SDK.ApiValidationException: Encountered a validation error (https://arl2.api.myob.com/accountright/b2f20bad-65c4-4dd3-a921-0a20ccfffb73/Sale/Invoice/Service/818eaa9f-5e51-4882-ab77-6b4bb0624735)
 ---> System.Net.WebException: The remote server returned an error: (400) Bad Request.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiDeleteRequestSync(Uri uri, ICompanyFileCredentials credentials)
   at MYOB.AccountRight.SDK.Services.MutableService`1.Delete(CompanyFile cf, Guid uid, ICompanyFileCredentials credentials, ErrorLevel errorLevel)
   at LepInvoicer.Implementations.MYOBService.<>c__DisplayClass33_1.<DeleteExistingInvoice>b__1() in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 875
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Implementations.MYOBService.DeleteExistingInvoice(String invoiceNumber) in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 874
[2025-06-17 13:10:55.014 +10:00 WRN] Skipping refund 1806 - cannot delete existing invoice S141881806
[2025-06-17 13:10:55.031 +10:00 INF] Creating refund invoice for refund 1807, Amount: ¤1.77
[2025-06-17 13:10:55.475 +10:00 INF] Deleting existing invoice S152161807
[2025-06-17 13:10:55.936 +10:00 WRN] Failed to delete existing invoice S152161807 - invoice may be locked/paid in MYOB
MYOB.AccountRight.SDK.ApiValidationException: Encountered a validation error (https://arl2.api.myob.com/accountright/b2f20bad-65c4-4dd3-a921-0a20ccfffb73/Sale/Invoice/Service/793efdaa-dcb5-422e-9ef2-69458a598c33)
 ---> System.Net.WebException: The remote server returned an error: (400) Bad Request.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiDeleteRequestSync(Uri uri, ICompanyFileCredentials credentials)
   at MYOB.AccountRight.SDK.Services.MutableService`1.Delete(CompanyFile cf, Guid uid, ICompanyFileCredentials credentials, ErrorLevel errorLevel)
   at LepInvoicer.Implementations.MYOBService.<>c__DisplayClass33_1.<DeleteExistingInvoice>b__1() in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 875
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Implementations.MYOBService.DeleteExistingInvoice(String invoiceNumber) in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 874
[2025-06-17 13:10:55.981 +10:00 WRN] Skipping refund 1807 - cannot delete existing invoice S152161807
[2025-06-17 13:10:55.992 +10:00 INF] Creating refund invoice for refund 1812, Amount: ¤1,312.65
[2025-06-17 13:10:58.087 +10:00 INF] Deleting existing invoice S251951812
[2025-06-17 13:10:59.325 +10:00 WRN] Failed to delete existing invoice S251951812 - invoice may be locked/paid in MYOB
MYOB.AccountRight.SDK.ApiValidationException: Encountered a validation error (https://arl2.api.myob.com/accountright/b2f20bad-65c4-4dd3-a921-0a20ccfffb73/Sale/Invoice/Service/a713338d-93e4-4f31-ab91-4947eb95c2d0)
 ---> System.Net.WebException: The remote server returned an error: (400) Bad Request.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiDeleteRequestSync(Uri uri, ICompanyFileCredentials credentials)
   at MYOB.AccountRight.SDK.Services.MutableService`1.Delete(CompanyFile cf, Guid uid, ICompanyFileCredentials credentials, ErrorLevel errorLevel)
   at LepInvoicer.Implementations.MYOBService.<>c__DisplayClass33_1.<DeleteExistingInvoice>b__1() in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 875
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Implementations.MYOBService.DeleteExistingInvoice(String invoiceNumber) in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 874
[2025-06-17 13:10:59.338 +10:00 WRN] Skipping refund 1812 - cannot delete existing invoice S251951812
[2025-06-17 13:10:59.387 +10:00 INF] Creating refund invoice for refund 1814, Amount: ¤1.37
[2025-06-17 13:10:59.822 +10:00 INF] Deleting existing invoice S137511814
[2025-06-17 13:11:00.351 +10:00 WRN] Failed to delete existing invoice S137511814 - invoice may be locked/paid in MYOB
MYOB.AccountRight.SDK.ApiValidationException: Encountered a validation error (https://arl2.api.myob.com/accountright/b2f20bad-65c4-4dd3-a921-0a20ccfffb73/Sale/Invoice/Service/ae7cf4da-1e73-4681-98b6-92685935c4b0)
 ---> System.Net.WebException: The remote server returned an error: (400) Bad Request.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiDeleteRequestSync(Uri uri, ICompanyFileCredentials credentials)
   at MYOB.AccountRight.SDK.Services.MutableService`1.Delete(CompanyFile cf, Guid uid, ICompanyFileCredentials credentials, ErrorLevel errorLevel)
   at LepInvoicer.Implementations.MYOBService.<>c__DisplayClass33_1.<DeleteExistingInvoice>b__1() in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 875
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Implementations.MYOBService.DeleteExistingInvoice(String invoiceNumber) in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 874
[2025-06-17 13:11:00.393 +10:00 WRN] Skipping refund 1814 - cannot delete existing invoice S137511814
[2025-06-17 13:11:00.402 +10:00 INF] Creating refund invoice for refund 1815, Amount: ¤3.83
[2025-06-17 13:11:00.789 +10:00 INF] Deleting existing invoice S138991815
[2025-06-17 13:11:01.557 +10:00 WRN] Failed to delete existing invoice S138991815 - invoice may be locked/paid in MYOB
MYOB.AccountRight.SDK.ApiValidationException: Encountered a validation error (https://arl2.api.myob.com/accountright/b2f20bad-65c4-4dd3-a921-0a20ccfffb73/Sale/Invoice/Service/0aaeec22-6d8e-4d71-87bd-33f814e5f06f)
 ---> System.Net.WebException: The remote server returned an error: (400) Bad Request.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiDeleteRequestSync(Uri uri, ICompanyFileCredentials credentials)
   at MYOB.AccountRight.SDK.Services.MutableService`1.Delete(CompanyFile cf, Guid uid, ICompanyFileCredentials credentials, ErrorLevel errorLevel)
   at LepInvoicer.Implementations.MYOBService.<>c__DisplayClass33_1.<DeleteExistingInvoice>b__1() in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 875
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Implementations.MYOBService.DeleteExistingInvoice(String invoiceNumber) in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 874
[2025-06-17 13:11:01.593 +10:00 WRN] Skipping refund 1815 - cannot delete existing invoice S138991815
[2025-06-17 13:11:01.609 +10:00 INF] Creating refund invoice for refund 1816, Amount: ¤6.80
[2025-06-17 13:11:02.231 +10:00 INF] Deleting existing invoice S143701816
[2025-06-17 13:11:02.830 +10:00 WRN] Failed to delete existing invoice S143701816 - invoice may be locked/paid in MYOB
MYOB.AccountRight.SDK.ApiValidationException: Encountered a validation error (https://arl2.api.myob.com/accountright/b2f20bad-65c4-4dd3-a921-0a20ccfffb73/Sale/Invoice/Service/5ac7ff46-5d70-47ca-a353-44c57a10c35c)
 ---> System.Net.WebException: The remote server returned an error: (400) Bad Request.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiDeleteRequestSync(Uri uri, ICompanyFileCredentials credentials)
   at MYOB.AccountRight.SDK.Services.MutableService`1.Delete(CompanyFile cf, Guid uid, ICompanyFileCredentials credentials, ErrorLevel errorLevel)
   at LepInvoicer.Implementations.MYOBService.<>c__DisplayClass33_1.<DeleteExistingInvoice>b__1() in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 875
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Implementations.MYOBService.DeleteExistingInvoice(String invoiceNumber) in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 874
[2025-06-17 13:11:02.844 +10:00 WRN] Skipping refund 1816 - cannot delete existing invoice S143701816
[2025-06-17 13:11:02.857 +10:00 INF] Creating refund invoice for refund 1817, Amount: ¤19.84
[2025-06-17 13:11:03.192 +10:00 INF] Deleting existing invoice S143701817
[2025-06-17 13:11:05.008 +10:00 WRN] Failed to delete existing invoice S143701817 - invoice may be locked/paid in MYOB
MYOB.AccountRight.SDK.ApiValidationException: Encountered a validation error (https://arl2.api.myob.com/accountright/b2f20bad-65c4-4dd3-a921-0a20ccfffb73/Sale/Invoice/Service/2e674898-d09f-451c-921f-b6054b956d02)
 ---> System.Net.WebException: The remote server returned an error: (400) Bad Request.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiDeleteRequestSync(Uri uri, ICompanyFileCredentials credentials)
   at MYOB.AccountRight.SDK.Services.MutableService`1.Delete(CompanyFile cf, Guid uid, ICompanyFileCredentials credentials, ErrorLevel errorLevel)
   at LepInvoicer.Implementations.MYOBService.<>c__DisplayClass33_1.<DeleteExistingInvoice>b__1() in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 875
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Implementations.MYOBService.DeleteExistingInvoice(String invoiceNumber) in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 874
[2025-06-17 13:11:05.042 +10:00 WRN] Skipping refund 1817 - cannot delete existing invoice S143701817
[2025-06-17 13:11:05.051 +10:00 INF] Creating refund invoice for refund 1818, Amount: ¤61.92
[2025-06-17 13:11:05.421 +10:00 INF] Deleting existing invoice S1531791818
[2025-06-17 13:11:05.817 +10:00 WRN] Failed to delete existing invoice S1531791818 - invoice may be locked/paid in MYOB
MYOB.AccountRight.SDK.ApiValidationException: Encountered a validation error (https://arl2.api.myob.com/accountright/b2f20bad-65c4-4dd3-a921-0a20ccfffb73/Sale/Invoice/Service/f2d3d1a7-f2e9-4aee-b0fe-cf5f0c77f7bc)
 ---> System.Net.WebException: The remote server returned an error: (400) Bad Request.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiDeleteRequestSync(Uri uri, ICompanyFileCredentials credentials)
   at MYOB.AccountRight.SDK.Services.MutableService`1.Delete(CompanyFile cf, Guid uid, ICompanyFileCredentials credentials, ErrorLevel errorLevel)
   at LepInvoicer.Implementations.MYOBService.<>c__DisplayClass33_1.<DeleteExistingInvoice>b__1() in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 875
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Implementations.MYOBService.DeleteExistingInvoice(String invoiceNumber) in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 874
[2025-06-17 13:11:05.876 +10:00 WRN] Skipping refund 1818 - cannot delete existing invoice S1531791818
[2025-06-17 13:11:05.883 +10:00 INF] Creating refund invoice for refund 1824, Amount: ¤70.04
[2025-06-17 13:11:06.240 +10:00 INF] Deleting existing invoice S189871824
[2025-06-17 13:11:09.080 +10:00 WRN] Failed to delete existing invoice S189871824 - invoice may be locked/paid in MYOB
MYOB.AccountRight.SDK.ApiValidationException: Encountered a validation error (https://arl2.api.myob.com/accountright/b2f20bad-65c4-4dd3-a921-0a20ccfffb73/Sale/Invoice/Service/3366c74b-aec7-4ee3-87d0-4ed666a81e70)
 ---> System.Net.WebException: The remote server returned an error: (400) Bad Request.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiDeleteRequestSync(Uri uri, ICompanyFileCredentials credentials)
   at MYOB.AccountRight.SDK.Services.MutableService`1.Delete(CompanyFile cf, Guid uid, ICompanyFileCredentials credentials, ErrorLevel errorLevel)
   at LepInvoicer.Implementations.MYOBService.<>c__DisplayClass33_1.<DeleteExistingInvoice>b__1() in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 875
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Implementations.MYOBService.DeleteExistingInvoice(String invoiceNumber) in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 874
[2025-06-17 13:11:09.088 +10:00 WRN] Skipping refund 1824 - cannot delete existing invoice S189871824
[2025-06-17 13:11:09.094 +10:00 INF] Creating refund invoice for refund 1836, Amount: ¤32.32
[2025-06-17 13:11:09.477 +10:00 INF] Deleting existing invoice S252801836
[2025-06-17 13:11:09.849 +10:00 WRN] Failed to delete existing invoice S252801836 - invoice may be locked/paid in MYOB
MYOB.AccountRight.SDK.ApiValidationException: Encountered a validation error (https://arl2.api.myob.com/accountright/b2f20bad-65c4-4dd3-a921-0a20ccfffb73/Sale/Invoice/Service/4b9f90a9-fe2b-4248-bce3-1aa759007fcd)
 ---> System.Net.WebException: The remote server returned an error: (400) Bad Request.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiDeleteRequestSync(Uri uri, ICompanyFileCredentials credentials)
   at MYOB.AccountRight.SDK.Services.MutableService`1.Delete(CompanyFile cf, Guid uid, ICompanyFileCredentials credentials, ErrorLevel errorLevel)
   at LepInvoicer.Implementations.MYOBService.<>c__DisplayClass33_1.<DeleteExistingInvoice>b__1() in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 875
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Implementations.MYOBService.DeleteExistingInvoice(String invoiceNumber) in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 874
[2025-06-17 13:11:09.870 +10:00 WRN] Skipping refund 1836 - cannot delete existing invoice S252801836
[2025-06-17 13:11:09.879 +10:00 INF] Creating refund invoice for refund 1837, Amount: ¤26.00
[2025-06-17 13:11:10.164 +10:00 INF] Deleting existing invoice S191871837
[2025-06-17 13:11:10.530 +10:00 WRN] Failed to delete existing invoice S191871837 - invoice may be locked/paid in MYOB
MYOB.AccountRight.SDK.ApiValidationException: Encountered a validation error (https://arl2.api.myob.com/accountright/b2f20bad-65c4-4dd3-a921-0a20ccfffb73/Sale/Invoice/Service/27c54bcf-d87b-45d8-a5e4-997d4b831e2e)
 ---> System.Net.WebException: The remote server returned an error: (400) Bad Request.
   at System.Net.HttpWebRequest.EndGetResponse(IAsyncResult asyncResult)
   at MYOB.AccountRight.SDK.Communication.BaseRequestHandler.HandleResponseCallback[T,TResp](IAsyncResult asynchronousResult)
   --- End of inner exception stack trace ---
   at MYOB.AccountRight.SDK.Extensions.ExceptionExtensions.ProcessException(Exception ex, Uri requestUri)
   at MYOB.AccountRight.SDK.Services.ServiceBase.MakeApiDeleteRequestSync(Uri uri, ICompanyFileCredentials credentials)
   at MYOB.AccountRight.SDK.Services.MutableService`1.Delete(CompanyFile cf, Guid uid, ICompanyFileCredentials credentials, ErrorLevel errorLevel)
   at LepInvoicer.Implementations.MYOBService.<>c__DisplayClass33_1.<DeleteExistingInvoice>b__1() in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 875
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at LepInvoicer.Implementations.MYOBService.DeleteExistingInvoice(String invoiceNumber) in C:\LepSF\au\LepInvoicer\Implementations\MYOBService.cs:line 874
[2025-06-17 13:11:10.540 +10:00 WRN] Skipping refund 1837 - cannot delete existing invoice S191871837
[2025-06-17 13:11:10.549 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 13:11:10.679 +10:00 INF] LEP Invoicer completed successfully in 58731ms. Orders: 10, Credits: 0, Refunds: 16
[2025-06-17 13:11:10.686 +10:00 INF] Database connection disposed
[2025-06-17 13:11:10.688 +10:00 INF] LEP Invoicer completed with result: 0
