[2025-06-17 16:43:47.692 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 16:43:47.720 +10:00 INF] Initializing FastReport...
[2025-06-17 16:43:47.797 +10:00 INF] FastReport initialized successfully
[2025-06-17 16:43:48.223 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 16:43:49.532 +10:00 INF] Starting LEP Invoicer at "2025-06-17T16:43:49.5320620+10:00"
[2025-06-17 16:43:49.536 +10:00 INF] Initializing database service...
[2025-06-17 16:43:49.539 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 16:43:49.636 +10:00 INF] Database connection established successfully
[2025-06-17 16:43:49.639 +10:00 INF] Database service initialized successfully
[2025-06-17 16:43:49.643 +10:00 INF] Checking for pending work...
[2025-06-17 16:43:49.649 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 16:43:50.661 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 16:43:50.663 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 16:43:50.676 +10:00 INF] Found 0 credits to invoice
[2025-06-17 16:43:50.679 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 16:43:50.684 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 16:43:50.685 +10:00 INF] No pending work found
[2025-06-17 16:43:50.686 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 16:43:50.687 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 16:43:50.785 +10:00 INF] LEP Invoicer completed successfully in 1253ms. No work to process.
[2025-06-17 16:43:50.796 +10:00 INF] Database connection disposed
[2025-06-17 16:43:50.798 +10:00 INF] LEP Invoicer completed with result: 0
