using lep.job;
using lep.run;
using System.Linq;
using static lep.job.JobTypeOptions;
using static lep.run.RunCelloglazeOptions;

namespace lep.extensionmethods
{
	/// <summary>
	/// Summary description for RenderExtension
	/// </summary>
	public static class RenderExtension
    {
        public static string GetJobColorSides(this IJob j)
        {
            return j.Template.ColourSide(j.FrontPrinting, j.BackPrinting).ToString();
        }

        public static int GetJobQuantity(this IJob j)
        {
            var qty = j.Quantity;

            if (j.Template.Is(Notepads))
            {
                qty = j.Quantity * j.Pages;
            }

            return qty;
        }

		// short Template name from job
		public static string GetJobTemplateName(this IJob j)
		{
			return j.Template.Name
				.Replace("same day dispatch", "SDD")
			    .Replace("next day dispatch", "NDD");
		}


		public static string GetJobSize(this IJob j)
        {
            var size = j.FinishedSize.PaperSize.Name;
			int w = j.FinishedSize.Width;
			int h = j.FinishedSize.Height;
			string wh = $" ({ w} x { h})";


			int colorSide = 0;
			string colorSideStr = "";
			
			if (!j.Is<PERSON>agazine())
			{
				if (j.FrontPrinting != JobPrintOptions.Unprinted) colorSide++;
				if (j.BackPrinting != JobPrintOptions.Unprinted) colorSide++;
				colorSideStr = colorSide == 2 ? " DS " : " SS ";
			}
			if (size == "Custom")
            {
				
				size = $"Custom {wh}";
            }

            if (j.Template.Is(PresentationFolder))
            {
                size = "PF";
            }
			else if ((j.Template.Is(MagazineSeparate, Magazine, MagazineNDD, Notepads, DuplicateNCRBooks, TriplicateNCRBooks, QuadruplicateNCRBooks,
							A4CalendarSelfCover, A4CalendarSeparateCover)) && j.Pages > 0)
			{
                size = $"{size} {j.Pages}pp";
            }
            else if (j.Template.Is(BusinessCard, BusinessCardNdd, BusinessCardSdd, DoubleBusinessCard))
            {
				size = size
					.Replace("Double Business Card ", "B/C Double ")
					.Replace("Business Card ", "B/C ");

				if (j.RoundOption != RoundOption.None)
				{
					size += "\n" + j.RoundOption.ToDescription();
				}
			}
			else if (j.Template.Is(Postcard))
			{
				size = "P/C " + j.FinishedSize.PaperSize.Name.Replace("Postcard", "Standard") + wh;
			}

			size = size + colorSideStr;
			return size;
        }

        public static string GetJobFoldedSize(this IJob j)
        {
            var size = j.FoldedSize?.PaperSize?.Name ?? "None";

            if (size == "Custom")
            {
                size = $"Custom ({j.FoldedSize.Width} x {j.FoldedSize.Height})";
            }
            return size;
        }

        public static string SideString(this IRun r)
        {
            if (!r.ManuallyManage && r.IsBusinessCard)
            {
                return r.Cmyk ? "2" : "1";
            }
            return "-";
        }

        public static string StockString(this IRun r)
        {
            var stockTxt = "-";
            foreach (var j in r.Jobs)
            {
                if (stockTxt == "-")
                {
                    stockTxt = StockString(j.FinalStock);
                }
                else if (stockTxt != StockString(j.FinalStock))
                {
                    return "Various";
                }
            }
            return stockTxt;
        }

        public static string StockString(this IStock stock)
        {
            return stock.Name.Replace("LEP", " ").Replace("Deluxe", " ").Replace("Light", " ");
        }

		public static string CelloStringSimplified (this IRun r)
		{

				try
				{
					var cellos = r.Jobs.Select(j => {
						if (j.Celloglaze == RunCelloglazeOptions.None)
							return "None";
						return j.Celloglaze.ToDescription()
													.Replace("EMBOSS ", "")
													.Replace("FOIL ", "")
													.Replace("SpotUV ", "");

					}
					).Distinct();
					if (cellos.Count() == 1)
					{
						return r.Jobs.First().Celloglaze.ToDescription();
					}
					else
					{
						return "Various";
					}
				}
				catch (System.Exception ex)
				{
					return "-";
				}

		}

		public static string CelloStringsFront(this IJob job)
		{
			switch (job.Celloglaze)
			{
				case None: return "";
				case MattFront: return "Matt";
				case MattBoth: return "Matt";
				case GlossFront: return "Gloss";
				case GlossBoth: return "Gloss";
				case VelvetFront: return "Velvet";
				case VelvetBoth: return "Velvet";
				case EmbossGlossFront: return "Gloss";
				case EmbossMattFront: return "Matt";
				case FoilFrontMattBoth: return "Matt";
				case SpotUVFrontMattBoth: return "Matt";
				case SpotUVFrontMattFront: return "Matt";
				case GlossFrontMattBack: return "Gloss";
				case MattAntiScuffBoth: return "MattAntiScuff";
				case MattAntiScuffFront: return "MattAntiScuff";
			}
			return "";
		}

		public static string CelloStringsBack(this IJob job)
		{
			switch (job.Celloglaze)
			{
				case None: return "";
				case MattFront: return "";
				case MattBoth: return "Matt";
				case GlossFront: return "";
				case GlossBoth: return "Gloss";
				case VelvetFront: return "";
				case VelvetBoth: return "Velvet";
				case EmbossGlossFront: return "";
				case EmbossMattFront: return "";
				case FoilFrontMattBoth: return "Matt";
				case SpotUVFrontMattBoth: return "Matt";
				case SpotUVFrontMattFront: return "";
				case GlossFrontMattBack: return "Gloss";
				case MattAntiScuffBoth: return "MattAntiScuff";
				case MattAntiScuffFront: return "";
			}
			return "";
		}

		public static string CelloStringsExtra(this IJob job)
		{
			switch (job.Celloglaze)
			{
				case None: return "";
				case MattFront: return "";
				case MattBoth: return "";
				case GlossFront: return "";
				case GlossBoth: return "";
				case VelvetFront: return "";
				case VelvetBoth: return "";
				case EmbossGlossFront: return "Emboss";
				case EmbossMattFront: return "Emboss";
				case FoilFrontMattBoth:  return "Foil - " + job.FoilColour;
				case SpotUVFrontMattBoth: return "SpotUV" ;
				case SpotUVFrontMattFront: return "SpotUV";
				case GlossFrontMattBack: return "";
			}
			return "";
		}



		public static string CelloString(this IRun r)
        {
            if (r.IsBusinessCard)
            {
				try
				{
					var cellos = r.Jobs.Select(j => j.Celloglaze).Distinct();
					if (cellos.Count() == 1)
					{
						return r.Celloglaze.ToDescription();
					}
					else
					{
						return "Various";
					}
				} catch(System.Exception ex)
				{
					return "-";
				}
            }
            return "-";
        }

        public static string GetPressDetailsPaperSizes(this IJob j)
        {
            var dps = string.Empty;
            if (j.PressDetails.Any())
            {
                dps = string.Join(" ", j.PressDetails.Select(pd => pd.Size).Distinct().ToArray());
            }
            return dps;
        }

        public static int GetNumberOfPressSheets(this IJob j)
        {
            var numberOfPressSheets = 0;
            if (j.IsBusinessCard())
            {
                if (j.Runs.Any() && j.Runs[0].NumOfPressSheets.HasValue)
                {
                    numberOfPressSheets = j.Runs[0].NumOfPressSheets.Value;
                }
            }
            else
            {
                if (j.IsMagazine())
                //If Magazine (Separate or Self) Need to iteratew through each pd (counter) and multiple sect by qty, then Add next PD row.
                {
                    for (var i = 0; i < j.PressDetails.Count; i++)
                    {
                        //if (j.PressDetails[i].Sect > 0 &&  j.PressDetails[i].Qty)
                        {
                            numberOfPressSheets += j.PressDetails[i].Sect * j.PressDetails[i].Qty;
                        }
                    }
                }
                else
                {
                    numberOfPressSheets = j.PressDetails.Sum(pd => pd.Qty);
                }
            }
            return numberOfPressSheets;
        }

        public static string TrimNewLines(this string s)
        {
            return s.TrimEnd('\r', '\n').TrimStart('\r', '\n');
        }
    }
}
