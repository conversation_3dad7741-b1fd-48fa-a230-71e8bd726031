[2025-06-17 15:52:15.721 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 15:52:15.749 +10:00 INF] Initializing FastReport...
[2025-06-17 15:52:15.825 +10:00 INF] FastReport initialized successfully
[2025-06-17 15:52:16.219 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 15:52:17.549 +10:00 INF] Starting LEP Invoicer at "2025-06-17T15:52:17.5488777+10:00"
[2025-06-17 15:52:17.552 +10:00 INF] Initializing database service...
[2025-06-17 15:52:17.555 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 15:52:17.656 +10:00 INF] Database connection established successfully
[2025-06-17 15:52:17.657 +10:00 INF] Database service initialized successfully
[2025-06-17 15:52:17.660 +10:00 INF] Checking for pending work...
[2025-06-17 15:52:17.663 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 15:52:18.567 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 15:52:18.570 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 15:52:18.583 +10:00 INF] Found 0 credits to invoice
[2025-06-17 15:52:18.585 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 15:52:18.590 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 15:52:18.593 +10:00 INF] No pending work found
[2025-06-17 15:52:18.594 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 15:52:18.595 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 15:52:18.674 +10:00 INF] LEP Invoicer completed successfully in 1125ms. No work to process.
[2025-06-17 15:52:18.681 +10:00 INF] Database connection disposed
[2025-06-17 15:52:18.682 +10:00 INF] LEP Invoicer completed with result: 0
