[2025-06-18 08:51:45.560 +10:00 INF] Starting LEP Invoicer application
[2025-06-18 08:51:45.589 +10:00 INF] Initializing FastReport...
[2025-06-18 08:51:45.678 +10:00 INF] FastReport initialized successfully
[2025-06-18 08:51:46.088 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-18 08:51:47.369 +10:00 INF] Starting LEP Invoicer at "2025-06-18T08:51:47.3692766+10:00"
[2025-06-18 08:51:47.391 +10:00 INF] Initializing database service...
[2025-06-18 08:51:47.394 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-18 08:51:47.519 +10:00 INF] Database connection established successfully
[2025-06-18 08:51:47.520 +10:00 INF] Database service initialized successfully
[2025-06-18 08:51:47.523 +10:00 INF] Checking for pending work...
[2025-06-18 08:51:47.526 +10:00 INF] Getting 500 orders to invoice
[2025-06-18 08:51:48.468 +10:00 INF] Found 0 orders to invoice (filtered 16 candidates)
[2025-06-18 08:51:48.471 +10:00 INF] Getting 100 credits to invoice
[2025-06-18 08:51:48.484 +10:00 INF] Found 0 credits to invoice
[2025-06-18 08:51:48.486 +10:00 INF] Getting 100 refunds to invoice
[2025-06-18 08:51:48.492 +10:00 INF] Found 0 refunds to invoice
[2025-06-18 08:51:48.493 +10:00 INF] No pending work found
[2025-06-18 08:51:48.495 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-18 08:51:48.497 +10:00 INF] Cleaning up invoicer logs...
[2025-06-18 08:51:48.588 +10:00 INF] LEP Invoicer completed successfully in 1219ms. No work to process.
[2025-06-18 08:51:48.595 +10:00 INF] Database connection disposed
[2025-06-18 08:51:48.596 +10:00 INF] LEP Invoicer completed with result: 0
