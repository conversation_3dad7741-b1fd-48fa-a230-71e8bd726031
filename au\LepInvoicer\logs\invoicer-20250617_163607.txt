[2025-06-17 16:36:07.488 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 16:36:07.522 +10:00 INF] Initializing FastReport...
[2025-06-17 16:36:07.607 +10:00 INF] FastReport initialized successfully
[2025-06-17 16:36:08.110 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 16:36:09.752 +10:00 INF] Starting LEP Invoicer at "2025-06-17T16:36:09.7518639+10:00"
[2025-06-17 16:36:09.756 +10:00 INF] Initializing database service...
[2025-06-17 16:36:09.759 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 16:36:09.914 +10:00 INF] Database connection established successfully
[2025-06-17 16:36:09.915 +10:00 INF] Database service initialized successfully
[2025-06-17 16:36:09.918 +10:00 INF] Checking for pending work...
[2025-06-17 16:36:09.921 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 16:36:11.266 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 16:36:11.271 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 16:36:11.286 +10:00 INF] Found 0 credits to invoice
[2025-06-17 16:36:11.288 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 16:36:11.302 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 16:36:11.303 +10:00 INF] No pending work found
[2025-06-17 16:36:11.305 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 16:36:11.306 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 16:36:11.437 +10:00 INF] LEP Invoicer completed successfully in 1685ms. No work to process.
[2025-06-17 16:36:11.448 +10:00 INF] Database connection disposed
[2025-06-17 16:36:11.451 +10:00 INF] LEP Invoicer completed with result: 0
