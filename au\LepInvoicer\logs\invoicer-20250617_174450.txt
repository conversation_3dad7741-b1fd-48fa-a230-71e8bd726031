[2025-06-17 17:44:50.584 +10:00 INF] Starting LEP Invoicer application
[2025-06-17 17:44:50.614 +10:00 INF] Initializing FastReport...
[2025-06-17 17:44:50.711 +10:00 INF] FastReport initialized successfully
[2025-06-17 17:44:51.200 +10:00 INF] Loaded MYOB cache: 269 accounts, 18 tax codes, 393 customers
[2025-06-17 17:44:52.414 +10:00 INF] Starting LEP Invoicer at "2025-06-17T17:44:52.4138285+10:00"
[2025-06-17 17:44:52.418 +10:00 INF] Initializing database service...
[2025-06-17 17:44:52.422 +10:00 INF] Initializing database connection and NHibernate session...
[2025-06-17 17:44:52.524 +10:00 INF] Database connection established successfully
[2025-06-17 17:44:52.526 +10:00 INF] Database service initialized successfully
[2025-06-17 17:44:52.529 +10:00 INF] Checking for pending work...
[2025-06-17 17:44:52.533 +10:00 INF] Getting 500 orders to invoice
[2025-06-17 17:44:53.431 +10:00 INF] Found 0 orders to invoice (filtered 15 candidates)
[2025-06-17 17:44:53.434 +10:00 INF] Getting 100 credits to invoice
[2025-06-17 17:44:53.447 +10:00 INF] Found 0 credits to invoice
[2025-06-17 17:44:53.449 +10:00 INF] Getting 100 refunds to invoice
[2025-06-17 17:44:53.459 +10:00 INF] Found 0 refunds to invoice
[2025-06-17 17:44:53.461 +10:00 INF] No pending work found
[2025-06-17 17:44:53.462 +10:00 INF] No pending work found - skipping MYOB initialization
[2025-06-17 17:44:53.464 +10:00 INF] Cleaning up invoicer logs...
[2025-06-17 17:44:53.537 +10:00 INF] LEP Invoicer completed successfully in 1123ms. No work to process.
[2025-06-17 17:44:53.543 +10:00 INF] Database connection disposed
[2025-06-17 17:44:53.544 +10:00 INF] LEP Invoicer completed with result: 0
